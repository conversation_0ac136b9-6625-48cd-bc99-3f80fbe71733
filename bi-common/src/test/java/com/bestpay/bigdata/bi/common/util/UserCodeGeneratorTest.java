package com.bestpay.bigdata.bi.common.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.net.InetAddress;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @ClassName UserCodeGeneratorTest
 * @description UserCodeGenerator工具测试类
 * @date 2025/7/5
 */
@RunWith(MockitoJUnitRunner.class)
public class UserCodeGeneratorTest {

  private static final String TEST_PREFIX = "UG_";

  private UserCodeGenerator userCodeGenerator;

  // 重置单例实例
  @Before
  public void setUp() {
   resetSingleton();
  }

  // 重置单例实例
  public void resetSingleton() {
    try {
      userCodeGenerator = new UserCodeGenerator();
    } catch (Exception e) {
      // 不是单例或其他错误，忽略
    }
  }


  /**
   * 测试点：基础格式验证 - 验证前缀是否正确 - 验证总长度是否符合预期 - 验证时间部分格式 - 验证节点ID格式 - 验证计数器格式 - 验证CRC校验位
   */
  @Test
  public void testGeneratedCodeFormat() {
    // 执行
    String code = userCodeGenerator.generateUserCode(TEST_PREFIX);

    // 验证
    assertNotNull("生成的code不应为null", code);
    assertTrue("前缀不匹配", code.startsWith(TEST_PREFIX));
    assertEquals("总长度不符合预期", TEST_PREFIX.length() + 16, code.length());

    // 提取各部分
    String timePart = code.substring(TEST_PREFIX.length(), TEST_PREFIX.length() + 10);
    String nodeIdPart = code.substring(TEST_PREFIX.length() + 10, TEST_PREFIX.length() + 12);
    String counterPart = code.substring(TEST_PREFIX.length() + 12, TEST_PREFIX.length() + 14);
    String crcPart = code.substring(TEST_PREFIX.length() + 14);

    // 验证时间部分格式
    assertTrue("时间部分格式无效: " + timePart, timePart.matches("\\d{10}"));

    // 验证节点ID
    assertEquals("节点ID长度应为2", 2, nodeIdPart.length());
    assertTrue("节点ID应只包含字母数字: " + nodeIdPart, nodeIdPart.matches("[a-zA-Z0-9]{2}"));

    // 验证计数器
    assertEquals("计数器长度应为2", 2, counterPart.length());
    assertTrue("计数器应只包含数字: " + counterPart, counterPart.matches("\\d{2}"));

    // 验证CRC
    assertEquals("CRC长度应为2", 2, crcPart.length());
    assertTrue("CRC应为十六进制格式: " + crcPart, crcPart.matches("[0-9A-F]{2}"));
  }

  /**
   * 测试点：计数器递增逻辑 - 验证同一毫秒内生成的计数器值递增 - 验证计数器达到99后正确重置
   */
  @Test
  public void testCounterIncrementLogic() {
    // 安全重置计数器状态
    ReflectionTestUtils.setField(userCodeGenerator, "counter", new AtomicInteger(0));

    // 获取计数器字段
    String code0 = userCodeGenerator.generateUserCode(TEST_PREFIX);
    String counter0 = code0.substring(TEST_PREFIX.length() + 12, TEST_PREFIX.length() + 14);
    assertEquals("00", counter0);

    // 安全重置计数器状态
    ReflectionTestUtils.setField(userCodeGenerator, "counter", new AtomicInteger(0));
    AtomicInteger counter1 = (AtomicInteger) ReflectionTestUtils.getField(userCodeGenerator, "counter");

    // 设置初始计数器值
    counter1.set(97);

    // 第一次生成（计数器98）
    String code1 = userCodeGenerator.generateUserCode(TEST_PREFIX);
    String counter11 = code1.substring(TEST_PREFIX.length() + 12, TEST_PREFIX.length() + 14);
    assertEquals("98", counter11);

    // 第二次生成（计数器99）
    String code2 = userCodeGenerator.generateUserCode(TEST_PREFIX);
    String counter2 = code2.substring(TEST_PREFIX.length() + 12, TEST_PREFIX.length() + 14);
    assertEquals("99", counter2);

    // 第三次生成（计数器重置为100）
    String code3 = userCodeGenerator.generateUserCode(TEST_PREFIX);
    String counter3 = code3.substring(TEST_PREFIX.length() + 12, TEST_PREFIX.length() + 14);
    assertEquals("00", counter3);
  }

  /**
   * 测试点：不同前缀生成 - 验证不同前缀生成正确 - 验证前缀长度不影响整体格式
   */
  @Test
  public void testDifferentPrefixes() {
    String shortPrefix = "A";
    String longPrefix = "LONG_PREFIX_";

    // 短前缀测试
    String shortCode = userCodeGenerator.generateUserCode(shortPrefix);
    assertTrue("短前缀不匹配", shortCode.startsWith(shortPrefix));
    assertEquals("短前缀总长度错误", shortPrefix.length() + 16, shortCode.length());

    // 长前缀测试
    String longCode = userCodeGenerator.generateUserCode(longPrefix);
    assertTrue("长前缀不匹配", longCode.startsWith(longPrefix));
    assertEquals("长前缀总长度错误", longPrefix.length() + 16, longCode.length());
  }


  /**
   * 测试点：时间变化逻辑 - 验证时间部分变化时计数器正确重置 - 验证时间部分格式正确
   */
  @Test
  public void testTimeChangeLogic() {
    // 初始时间
    LocalDateTime baseTime = LocalDateTime.of(2023, 1, 1, 12, 0);

    try (MockedStatic<LocalDateTime> mockedTime = mockStatic(LocalDateTime.class)) {
      // 设置初始时间
      mockedTime.when(LocalDateTime::now).thenReturn(baseTime);

      // 第一次生成
      String code1 = userCodeGenerator.generateUserCode(TEST_PREFIX);
      System.out.println("code1:" + code1);
      String timePart1 = code1.substring(TEST_PREFIX.length(), TEST_PREFIX.length() + 10);
      String counter1 = code1.substring(TEST_PREFIX.length() + 12, TEST_PREFIX.length() + 14);

      // 验证初始计数器
      assertEquals("初始计数器应为00", "00", counter1);

      // 同一时间窗口内生成第二个代码
      String code2 = userCodeGenerator.generateUserCode(TEST_PREFIX);
      System.out.println("code2:" + code2);
      String timePart2 = code2.substring(TEST_PREFIX.length(), TEST_PREFIX.length() + 10);
      String counter2 = code2.substring(TEST_PREFIX.length() + 12, TEST_PREFIX.length() + 14);

      // 验证时间部分相同，计数器递增
      assertEquals("同一时间窗口内时间部分应相同", timePart1, timePart2);
      assertEquals("同一时间窗口内计数器应递增", "01", counter2);

      // 模拟时间前进1分钟
      mockedTime.when(LocalDateTime::now).thenReturn(baseTime.plusMinutes(1));

      // 时间变化后生成第三个代码
      String code3 = userCodeGenerator.generateUserCode(TEST_PREFIX);
      System.out.println("code3:" + code3);
      String timePart3 = code3.substring(TEST_PREFIX.length(), TEST_PREFIX.length() + 10);
      String counter3 = code3.substring(TEST_PREFIX.length() + 12, TEST_PREFIX.length() + 14);

      // 验证时间部分变化，计数器重置
      assertNotEquals("时间变化后时间部分应不同", timePart1, timePart3);
      assertEquals("时间变化后计数器应重置", "00", counter3);
    }
  }

  /**
   * 测试点：节点ID稳定性 - 验证同一实例多次生成的节点ID相同 - 验证不同实例生成的节点ID可能不同
   */
  @Test
  public void testNodeIdStability() {
    // 测试同一实例生成的节点ID稳定性

    // 第一次生成（使用真实环境）
    String code1 = userCodeGenerator.generateUserCode(TEST_PREFIX);
    String nodeId1 = extractNodeId(code1);

    // 第二次生成（同一实例，相同环境）
    String code2 = userCodeGenerator.generateUserCode(TEST_PREFIX);
    String nodeId2 = extractNodeId(code2);

    // 验证同一实例的节点ID稳定
    assertEquals("同一实例的节点ID应相同", nodeId1, nodeId2);

    // 使用Mockito mock InetAddress静态方法
    try (MockedStatic<InetAddress> mockedInet = mockStatic(InetAddress.class)) {
      // 模拟不同的服务器环境（服务器1）
      InetAddress mockAddress1 = mock(InetAddress.class);
      when(mockAddress1.getHostName()).thenReturn("server1.example.com");
      when(mockAddress1.getHostAddress()).thenReturn("***********");
      mockedInet.when(InetAddress::getLocalHost).thenReturn(mockAddress1);

      // 创建新实例并生成节点ID
      resetSingleton();
      String code3 = userCodeGenerator.generateUserCode(TEST_PREFIX);
      String nodeId3 = extractNodeId(code3);

      // 模拟另一个不同的服务器环境（服务器2）
      InetAddress mockAddress2 = mock(InetAddress.class);
      when(mockAddress2.getHostName()).thenReturn("server2.example.con");
      when(mockAddress2.getHostAddress()).thenReturn("************");
      mockedInet.when(InetAddress::getLocalHost).thenReturn(mockAddress2);

      // 创建新实例并生成节点ID
      resetSingleton();
      String code4 = userCodeGenerator.generateUserCode(TEST_PREFIX);
      String nodeId4 = extractNodeId(code4);

      // 验证不同环境下的节点ID不同
      assertNotEquals("不同服务器环境下的节点ID应不同", nodeId3, nodeId4);

      // 验证节点ID与主机名/IP相关
      assertTrue("节点ID应反映主机名/IP信息", nodeId3.hashCode() != nodeId4.hashCode());
    }
  }

  // 辅助方法：提取节点ID
  private String extractNodeId(String code) {
    return code.substring(TEST_PREFIX.length() + 10, TEST_PREFIX.length() + 12);
  }

  /**
   * 测试点：时间窗口内唯一性与数量限制
   * - 验证每分钟最多生成99个唯一编码 - 验证时间窗口切换后计数器重置
   */
  @Test
  public void testTimeWindowLimit() throws InterruptedException {
    final int THREAD_COUNT = 20;
    final int GENERATIONS_PER_THREAD = 10;
    // 每分钟最多100个
    final int EXPECTED_TOTAL = 100;
    final Set<String> uniqueCodes = Collections.synchronizedSet(new HashSet<>());
    final CountDownLatch startLatch = new CountDownLatch(1);
    final CountDownLatch endLatch = new CountDownLatch(THREAD_COUNT);
    final ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
    final List<Throwable> exceptions = Collections.synchronizedList(new ArrayList<>());

    try (MockedStatic<LocalDateTime> mockedTime = mockStatic(LocalDateTime.class)) {
      // 初始时间：2023-01-01 00:00:00
      LocalDateTime baseTime = LocalDateTime.of(2023, 1, 1, 0, 0);
      mockedTime.when(LocalDateTime::now).thenReturn(baseTime);

      // 提交所有线程任务
      for (int i = 0; i < THREAD_COUNT; i++) {
        final int threadId = i;
        executor.submit(() -> {
          try {
            // 等待所有线程准备好
            startLatch.await();

            for (int j = 0; j < GENERATIONS_PER_THREAD; j++) {
              try {
                String code = userCodeGenerator.generateUserCode(TEST_PREFIX);

                // 前99个编码必须唯一
                if (uniqueCodes.size() < EXPECTED_TOTAL) {
                  if (!uniqueCodes.add(code)) {
                    exceptions.add(new IllegalStateException("线程 " + threadId + " 生成重复编码: " + code));
                  }
                } else {
                  // 超过99个后应开始生成重复编码
                  if (uniqueCodes.add(code)) {
                    exceptions.add(new IllegalStateException("超过时间窗口限制后生成了新编码: " + code));
                  }
                }
              } catch (Exception e) {
                exceptions.add(new RuntimeException("编码生成失败", e));
              }
            }
          } catch (InterruptedException e) {
            exceptions.add(e);
            Thread.currentThread().interrupt();
          } finally {
            endLatch.countDown();
          }
        });
      }

      // 所有线程同时开始
      startLatch.countDown();
      endLatch.await();

      // 验证时间窗口内生成的编码数量
      assertEquals("时间窗口内生成的编码数量不符合预期", EXPECTED_TOTAL, uniqueCodes.size());
    } finally {
      executor.shutdownNow();
      executor.awaitTermination(5, TimeUnit.SECONDS);
    }
  }
}

