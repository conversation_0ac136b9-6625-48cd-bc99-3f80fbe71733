package com.bestpay.bigdata.bi.common.dto.report;

import org.junit.jupiter.api.Test;

import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Author: wybStart
 * @Date: 2025/6/6  15:31
 * @Description:
 */
class ReportUuidGenerateUtilTest {

    @Test
    public void testGenerateReportConfigUuid_Format() {
        String uuid = ReportUuidGenerateUtil.generateReportConfigUuid();
        System.out.println(uuid);
        assertNotNull(uuid);
        assertTrue(uuid.startsWith(ReportUuidGenerateUtil.REPORT_CONFIG_UUID_PREFIX));

        // 验证 UUID 格式是否符合标准
        String uuidPart = uuid.replace(ReportUuidGenerateUtil.REPORT_CONFIG_UUID_PREFIX, "");
        assertTrue(Pattern.matches("\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b", uuidPart));
    }

    @Test
    public void testGenerateReportComputeUuid_Format() {
        String uuid = ReportUuidGenerateUtil.generateReportComputeUuid();
        System.out.println(uuid);
        assertNotNull(uuid);
        assertTrue(uuid.startsWith(ReportUuidGenerateUtil.REPORT_COMPUTE_UUID_PREFIX));

        String uuidPart = uuid.replace(ReportUuidGenerateUtil.REPORT_COMPUTE_UUID_PREFIX, "");
        assertTrue(Pattern.matches("\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b", uuidPart));
    }
}