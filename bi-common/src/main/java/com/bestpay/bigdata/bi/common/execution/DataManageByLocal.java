package com.bestpay.bigdata.bi.common.execution;

import com.bestpay.bigdata.bi.common.entity.ResultBlock;
import com.bestpay.bigdata.bi.common.entity.ResultBlockMetadata;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-13-17:28
 */
public class DataManageByLocal implements DataManage {


    @Override
    public void writeResultBlock(ResultBlock resultBlock) {

    }

    @Override
    public ResultBlock readResultBlock(ResultBlockMetadata metadata) {
        return null;
    }

    @Override
    public void expireResultBlockList(List<ResultBlockMetadata> resultBlockMetadata) {

    }
}
