package com.bestpay.bigdata.bi.common.error;

public enum PageErrorCode implements ErrorCodeSupplier {
    PAGE_INFO_EMPTY_ERROR("00001",ErrorType.BUSINESS_ERROR),
    PAGE_QUERY_ERROR("00001",ErrorType.BUSINESS_ERROR),

    ;

    private static final String PREFIX = "PAGE_";

    private final ErrorCode errorCode;

    PageErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
