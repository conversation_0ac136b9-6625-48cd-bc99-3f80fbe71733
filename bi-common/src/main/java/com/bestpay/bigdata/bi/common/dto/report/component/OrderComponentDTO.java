package com.bestpay.bigdata.bi.common.dto.report.component;

import com.bestpay.bigdata.bi.common.dto.report.ContrastOrderParam;
import com.bestpay.bigdata.bi.common.dto.report.ReportUuidGenerateUtil;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("排序字段")
public class OrderComponentDTO {

  @ApiModelProperty(value = "主键ID")
  private Long id;

  @ApiModelProperty(value = "唯一标识")
  private String uuid;

  @ApiModelProperty(value = "报表配置的唯一标识")
  protected String configUuid;

  @ApiModelProperty(value = "中文-字段名称",notes = "入参")
  private String name;

  @ApiModelProperty(value = "中文-字段原名",notes = "入参")
  private String originalName;

  @ApiModelProperty(value = "字段显示类型名称")
  private String showTypeName;

  @ApiModelProperty(value = "字段英文名称")
  private String enName;

  @ApiModelProperty(value = "指标名称")
  private String indexName;

  @ApiModelProperty(value = "排序类型（DESC、ASC）")
  private String type;

  @ApiModelProperty(value = "计算字段计算逻辑")
  private String fun;

  @ApiModelProperty(value = "源字段英文")
  private String originEnName;

  @ApiModelProperty(value = "类型名称")
  private String typeName;

  @ApiModelProperty(value = "聚合方式,主要用于生成别名使用")
  private String polymerization;

  @ApiModelProperty(value = "类型名称")
  private String reportField;

  @ApiModelProperty(value = "支持自定义排序")
  private List<String> customOrderList;

  @ApiModelProperty(value = "计算逻辑")
  private String calculateLogic;

  @ApiModelProperty(value = "对比表头排序参数")
  private ContrastOrderParam orderParam;

  @ApiModelProperty(value = "是否是计算字段")
  private Boolean isComputeField;

  public static void setOrderUuid(List<OrderComponentDTO> components,
      Map<String, String> computerUuidMap, Map<String, String> configUuidMap) {
    for (OrderComponentDTO componentPropertyDTO : components) {
      componentPropertyDTO.setUuid(getUuid(componentPropertyDTO.getUuid(),computerUuidMap));

      String newConfigUuid = ReportUuidGenerateUtil.generateReportConfigUuid();
      configUuidMap.put(componentPropertyDTO.getConfigUuid(), newConfigUuid);
      componentPropertyDTO.setConfigUuid(newConfigUuid);
    }
  }

  private static String getUuid(String originUuid, Map<String, String> computerUuidMap){
    if(originUuid==null){
      return "";
    }
    String newUuid = computerUuidMap.get(originUuid);
    if(newUuid==null){
      return "";
    }

    return newUuid;
  }
}
