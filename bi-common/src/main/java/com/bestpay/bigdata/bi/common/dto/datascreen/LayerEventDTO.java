package com.bestpay.bigdata.bi.common.dto.datascreen;

import lombok.Data;

@Data
public class LayerEventDTO {

    /**
     * 状态码：1.启用 0.禁用
     */
    private Boolean eventEnabled;

    /**
     * 展示方式：1.点击跳转页面 2.点击弹出图层
     */
    private Integer showType;

    /**
     * 页面类型：1.外部 2.内部
     */
    private Integer webType;

    /**
     * 网页地址
     */
    private String webUrl;

    /**
     * 打开方式：1.新窗口 2.当前窗口 3.弹窗
     */
    private Integer openType;

    /**
     * 绑定图层的卡片id
     */
    private String bindCardUniqueKey;
}
