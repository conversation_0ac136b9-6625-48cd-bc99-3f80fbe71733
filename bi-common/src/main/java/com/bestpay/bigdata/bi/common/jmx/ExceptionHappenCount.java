package com.bestpay.bigdata.bi.common.jmx;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022-04-13-13:50
 */
public class ExceptionHappenCount implements ExceptionHappenCountMBean {

    private int oomCount;
    private int npCount;
    private int fiveMinuteOomCount;
    private int fiveMinuteNpCount;
    private Date ooMLastDate = new Date();
    private Date npLastDate = new Date();

    private final long TIME_INTERVAL = 5 * 60 * 1000L;


    @Override
    public int getOutOfMemoryErrorCount() {
        return oomCount;
    }

    @Override
    public int getNullPointerExceptionCount() {
        return npCount;
    }

    @Override
    public int getFiveMinuteOutOfMemoryErrorCount() {
        expireOomCount();
        return fiveMinuteOomCount;
    }

    @Override
    public int getFiveMinuteNullPointerExceptionCount() {
        expireNpCount();
        return fiveMinuteNpCount;
    }

    public void incrementFiveMinuteOomCount() {
        expireOomCount();
        ooMLastDate = new Date();
        ++this.fiveMinuteOomCount;
    }

    public void incrementFiveMinuteNpCount() {
        expireNpCount();
        npLastDate = new Date();
        ++this.fiveMinuteNpCount;
    }

    private void expireOomCount(){
        Date currentDate = new Date();
        if(currentDate.getTime() - ooMLastDate.getTime() > TIME_INTERVAL){
            fiveMinuteOomCount = 0;
        }
    }

    private void expireNpCount(){
        Date currentDate = new Date();
        if(currentDate.getTime() - npLastDate.getTime() > TIME_INTERVAL){
            fiveMinuteNpCount = 0;
        }
    }

    public void setOomCount(int oomCount) {
        this.oomCount = oomCount;
    }

    public void setNpCount(int npCount) {
        this.npCount = npCount;
    }
}
