package com.bestpay.bigdata.bi.common.dto.report.component;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "计算字段组件")
public class ComputeComponentPropertyDTO extends CommonComponentPropertyDTO {

  @ApiModelProperty(value = "字段值列表")
  private List<String> valueList;

  @ApiModelProperty(value = "是否隐藏")
  private Boolean isHide = false;
}
