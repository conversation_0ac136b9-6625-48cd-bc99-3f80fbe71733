package com.bestpay.bigdata.bi.common.bean.aiapi;

import com.bestpay.bigdata.bi.common.entity.PermissionInfo;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName ApiPlusUserInfo
 * @description 新用户信息
 * @date 2025/7/4
 */
@Data
@Accessors(chain = true)
public class ApiPlusUserInfo implements Serializable {

  /**
   * 智能账户ID
   */
  private String uuid;
  /**
   * 智能账户名
   */
  private String account;

  /**
   * 用户名
   */
  private String username;

  /**
   * 用户邮箱
   */
  private String email;

  /**
   * 手机号
   */
  private String mobile;

  /**
   * 组织
   */
  private QueryCodeAndNameResultVO org;
  /**
   * 省公司
   */
  private QueryCodeAndNameResultVO province;
  /**
   * 城市
   */
  private QueryCodeAndNameResultVO city;


  /**
   * 用户权限标识集合
   */
  private List<PermissionInfo> permissionList;


  /**
   * 渠道信息
   */
  private List<ChannelInfoVO> channelInfoList;

  /**
   * 状态正常-1-冻结-2-注销-3
   */
  private Integer status;

}
