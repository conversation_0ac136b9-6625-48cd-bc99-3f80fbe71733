package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仪表板报表坐标轴配置
 * <AUTHOR>
 */
@Data
public class FunnelChartCoordinateAxisConfigRequest extends CommonCoordinateAxisConfigRequest{

    @ApiModelProperty(value = "坐标轴Y轴")
    private CoordinateAxisYRequest coordinateAxisY;

    @ApiModelProperty(value = "坐标轴Y轴")
    private CoordinateAxisXRequest coordinateAxisX;

    @ApiModelProperty(value = "数据标签配置")
    private FunnelChartDataLabelConfigRequest dataLabelConfig;
}
