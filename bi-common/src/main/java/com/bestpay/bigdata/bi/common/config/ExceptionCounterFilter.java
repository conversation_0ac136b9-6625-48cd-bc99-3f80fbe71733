package com.bestpay.bigdata.bi.common.config;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.extension.Activate;
import com.alibaba.dubbo.rpc.*;
import com.bestpay.bigdata.bi.common.jmx.ExceptionHappenCount;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.ExceptionHandlerMethodResolver;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;
import java.lang.reflect.Method;

/**
 * dubbo api call exception filter statics
 * <AUTHOR>
 * @create 2022-04-13-14:06
 */
@Activate(group = {Constants.CONSUMER,Constants.PROVIDER})
@Slf4j
public class ExceptionCounterFilter implements Filter {

    private static ExceptionHappenCount exceptionHappenCount;
    {
        exceptionHappenCount = new ExceptionHappenCount();
        MBeanServer server = ManagementFactory.getPlatformMBeanServer();
        ObjectName backendExceptionCounter = null;
        try {
            backendExceptionCounter = new ObjectName("com.bestpay.bigdata.bi.exception:name=dubboExceptionCounter");
            //create mbean and register mbean
            server.registerMBean(exceptionHappenCount, backendExceptionCounter);
        } catch (Exception e) {
            log.error("create mbean and register mbean error", e);
        }
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        return invoker.invoke(invocation);
    }

    @ExceptionHandler(value = NullPointerException.class)
    public Object handleIOException(NullPointerException e){
        log.warn("Catch exception", e);
        String exceptionName=e.getClass().getName();
        int count = exceptionHappenCount.getNullPointerExceptionCount();
        exceptionHappenCount.setNpCount(++count);
        exceptionHappenCount.incrementFiveMinuteNpCount();
        return exceptionName.concat(e.getMessage());
    }


    @ExceptionHandler(value = OutOfMemoryError.class)
    public Object handleIOException(OutOfMemoryError e){
        log.warn("Catch exception", e);
        String exceptionName=e.getClass().getName();
        int count = exceptionHappenCount.getOutOfMemoryErrorCount();
        exceptionHappenCount.setOomCount(++count);
        exceptionHappenCount.incrementFiveMinuteOomCount();
        return exceptionName.concat(e.getMessage());
    }
}
