package com.bestpay.bigdata.bi.common.util;

import java.util.Base64;

/**
 * <AUTHOR>
 * @create 2023-05-25-19:36
 */
public class Base64Util {

    /***
     * BASE64解密
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] decryBASE64(String key){
        return Base64.getUrlDecoder().decode(key);
    }

    /***
     * BASE64加密
     * @param key
     * @return
     * @throws Exception
     */
    public static String encryptBASE64(byte[] key){
        return new String(Base64.getUrlEncoder().encode(key));
    }

    public static void main(String[] args) {
        System.out.println(new String(decryBASE64("bWluaWlvOi8vbG9naWNOYW1lPWJpZ2RhdGEtYmktb3NzLGJ1Y2tldD1iaWdkYXRhLWJpLXRudC0xMSxvYmplY3ROYW1lPThKNTJfMTcvYmlnZGF0YS1iaV8xNy8yMDI0MTEyMi91bkFCMzRhNTJlMGFmZjYyNGZkOGJmODI3ZDRmODJmYjhmODk4NTM5MjMyNS5wbmc=")));
    }
}
