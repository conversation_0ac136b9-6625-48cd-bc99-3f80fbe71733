package com.bestpay.bigdata.bi.common.util;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;

/**
 * <AUTHOR>
 * @date 2021/6/4 11:25
 **/
public class RsaUtil {

    public static final String PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJjf24urWBZeIhiTe3X0tZeb9dzk+khC/B3Bru3FICfBcBigmJjWVBxD22ksmqEUZLhx0yidV3JnTezbajwjVHHwb2gK7tT/DwzkFB1tmr895wC2/Y/fbd/GVh4DVb09Y6kwLH7WE665XHAIyuh9WgrHqOHjUyEaxDi342IpNTjVAgMBAAECgYBEMUdLzEjk4WSwNcmS2m/Oi0FnIbULWuX/mBAh/BbV5RHNyoWHV8P5P8O17LGlPOWY6R1aNqV1YON/znhbDx9xajCoNCAro1QR1QUAtMiW0Z7sZACYuKZS1RSpEC/r0lVWYIbUuqQn8AHZ4kqqI/V0Fj+3lNcNUinAiC5aUGO+mQJBAN5AIoBtIhv2dJBuUqs1rAn/NQ/KWkcXg5jESC7olvayN9fSgX+mgowSGVZCqacfuRYjpTZUbi7J6xyJAFkFTssCQQCwFsQtRvEeQquwAiUdfZSTZKtQf3DZ3XLvCDpIJjfkkgRFINS1yBl0A1+cVly6PoWkYwQ0yrLpQPKs7eEh6ALfAkAZhbfwk5fFKko8g87Ohn1ZMIuBYrV3UIX2NyQq7t7XOaQcDrp8VDzNpQ5vz3v4CzaQCkvgr1Vv3hQ31KvLjUZ9AkAw4+FMAOppUHGCyNWtPnTGB6lZDEk09Ds5CrvD1HioSbJNzzO/1PLcNyOQsJnGTB2m6qb8UVsjUBkQ0mszkstZAkEAgRyfBRy8Z1ZkufvGEME16Wgu/fU42UcNqWq5jGcEhsdjN17ddtxIBltSyLo1Apjpqw8ZOha6SBdHr/JiStdrwA==";

    public static final String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCY39uLq1gWXiIYk3t19LWXm/Xc5PpIQvwdwa7txSAnwXAYoJiY1lQcQ9tpLJqhFGS4cdMonVdyZ03s22o8I1Rx8G9oCu7U/w8M5BQdbZq/PecAtv2P323fxlYeA1W9PWOpMCx+1hOuuVxwCMrofVoKx6jh41MhGsQ4t+NiKTU41QIDAQAB";


    /**
     * 智加数据解密
     * @param ciphertext 手机号密文
     * @return 手机号
     */
    public static String decrypt(String ciphertext,String privateKey) {
        return SecureUtil.rsa(privateKey, null).decryptStr(ciphertext, KeyType.PrivateKey);
    }


    /**
     * 智加数据加密
     * @param ciphertext
     * @return
     */
    public static String encrypt(String ciphertext) {
        return SecureUtil.rsa(null, PUBLIC_KEY).encryptBase64(ciphertext, KeyType.PublicKey);
    }

}
