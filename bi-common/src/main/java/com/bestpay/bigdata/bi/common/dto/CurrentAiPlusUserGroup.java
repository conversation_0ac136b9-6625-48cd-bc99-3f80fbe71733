package com.bestpay.bigdata.bi.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("当前用户组")
public class CurrentAiPlusUserGroup {

  @ApiModelProperty(value = "用户组名称")
  private String name;

  @ApiModelProperty(value = "用户组code")
  private String code;

  @ApiModelProperty(value = "组织层次")
  private Integer level;

}
