package com.bestpay.bigdata.bi.common.dto.report.component;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "关键字组件")
public class KeywordComponentPropertyDTO extends CommonComponentPropertyDTO {


  @ApiModelProperty(value = "聚合方式")
  private String polymerization;

  @ApiModelProperty(value = "值列表")
  private List<String> valueList;

  @ApiModelProperty(value = "比较 @ScopeFilterTypeEnum")
  private String scopeFilterType;

  @ApiModelProperty(value = "是否隐藏")
  private Boolean isHide = false;

  @ApiModelProperty(value = "比较符用到的常量值，当fieldType是SELECT-INPUT字符时，这个字段有值，逗号隔开")
  private String stringValue;
}
