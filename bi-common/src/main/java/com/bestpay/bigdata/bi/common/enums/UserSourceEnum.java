package com.bestpay.bigdata.bi.common.enums;

/**
 * <AUTHOR>
 */
public enum UserSourceEnum {
    MOBILE("mobile","移动端"),
    PC("pc","PC端"),;

    private final String code;
    private final String name;

    UserSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
