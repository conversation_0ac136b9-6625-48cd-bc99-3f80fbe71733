package com.bestpay.bigdata.bi.common.dto.report;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/4 16:23
 * @Description :
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LodFunction extends HighFunction {

    public static List<String> lodFunctionList = Lists.newArrayList("fixed", "include", "exclude");

    /** actual function name */
    private String functionName;

    /** like FIXED(ABC, ERT:MAX(SDCS)) , then preParamList is [ABC, ERT] */
    /**
     * 这里存的英文名称
     */
    private List<String> preParamList;

    /** MAX(SDCS) */
    /**
     * 这里存的是计算字段的英文名称
     */
    private String targetAggFunction;

    /** / * - + */
    private String operator;

    /** TEST / FIXED(ABC, ERT:MAX(SDCS)) , anotherFunctionExpression: TEST  */
    private String anotherFunctionExpression;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        LodFunction that = (LodFunction) o;
        return Objects.equals(functionName, that.functionName) && Objects.equals(preParamList, that.preParamList) && Objects.equals(targetAggFunction, that.targetAggFunction);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), functionName, preParamList, targetAggFunction);
    }
}
