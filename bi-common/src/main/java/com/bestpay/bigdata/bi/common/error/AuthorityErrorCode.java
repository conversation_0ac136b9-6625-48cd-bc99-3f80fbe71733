package com.bestpay.bigdata.bi.common.error;

public enum AuthorityErrorCode implements ErrorCodeSupplier {
    PERMISSION_ERROR("00001",ErrorType.BUSINESS_ERROR),

    ;

    private static final String PREFIX = "AUTHORITY_";

    private final ErrorCode errorCode;

    AuthorityErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
