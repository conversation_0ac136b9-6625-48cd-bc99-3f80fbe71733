package com.bestpay.bigdata.bi.common.dto.report.component;

import com.bestpay.bigdata.bi.common.dto.report.AdvancedComputing;
import com.bestpay.bigdata.bi.common.enums.ScopeFilterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "过滤器组件")
public class FilterComponentPropertyDTO extends CommonComponentPropertyDTO {

  @ApiModelProperty(value = "范围过滤类型",required = true,allowableValues = ScopeFilterTypeEnum.ALL_VALUE)
  private String scopeFilterType;

  @ApiModelProperty(value = "高级计算")
  private AdvancedComputing advancedComputing;

  @ApiModelProperty(value = "计算字段解析后的逻辑")
  private String computeFieldLogic;

  @ApiModelProperty(value = "区间 1动态时间 2固定时间")
  private String intervalType;

  @ApiModelProperty(value = "比较符用到的常量值，当fieldType是SELECT-INPUT字符时，这个字段有值，逗号隔开")
  private String stringValue;

  @ApiModelProperty(value = "比较符用到的常量值")
  private List<String> valueList;

  @ApiModelProperty(value = "如果选择了动态时间,这里为动态时间 见FilterDateValueEnum 定义")
  private String dynamicDate;

  @ApiModelProperty(value = "日期配置id")
  private Long datePickerId;

  @ApiModelProperty(value = "是否隐藏")
  private Boolean isHide = false;

  @ApiModelProperty(value = "日期筛选器")
  private String timeType;

  @ApiModelProperty(value = "日期类型")
  private String dateType;

  @ApiModelProperty(value = "过滤器类型")
  private String filterType;

  @ApiModelProperty(value = "默认值类型")
  private String defaultType;

  @ApiModelProperty(value = "是否包含当前日期")
  private Boolean includeCurrent;

  @ApiModelProperty(value = "默认值")
  private List<String> defaultValues;
}
