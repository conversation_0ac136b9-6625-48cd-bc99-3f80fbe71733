package com.bestpay.bigdata.bi.common.dto.dashboard;

import java.io.Serializable;
import lombok.Data;

/**
 * 卡片位置信息
 * <AUTHOR>
 */
@Data
public class DashboardCardLocationDTO implements Serializable {
    private static final long serialVersionUID = -4462573082598449403L;
    private Integer x;
    private Integer y;
    private Integer w;
    private Integer h;


    /**
     * 获取默认位置信息
     * 移动端和pc端有不同的默认位置设置
     *
     * @param type
     * @return
     */
    public static DashboardCardLocationDTO getDefaultLocation(String type) {
        if ("pc".equals(type)) {
            // 默认位置
            DashboardCardLocationDTO pcLocation = new DashboardCardLocationDTO();
            pcLocation.setX(0);
            pcLocation.setW(8);
            pcLocation.setH(13);
            pcLocation.setY(0);
            return pcLocation;
        }
        if ("mobile".equals(type)) {
            DashboardCardLocationDTO mobileLocation = new DashboardCardLocationDTO();
            mobileLocation.setX(0);
            mobileLocation.setW(24);
            mobileLocation.setH(13);
            mobileLocation.setY(0);
            return mobileLocation;
        }
        DashboardCardLocationDTO dashboardCardLocationDTO = new DashboardCardLocationDTO();
        dashboardCardLocationDTO.setX(0);
        dashboardCardLocationDTO.setH(13);
        dashboardCardLocationDTO.setW(8);
        dashboardCardLocationDTO.setY(0);
        return dashboardCardLocationDTO;
    }
}
