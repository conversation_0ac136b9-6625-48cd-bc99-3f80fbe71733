package com.bestpay.bigdata.bi.common.enums;

/**
 * <AUTHOR>
 */

public enum IndexCardTypeEnum {
    EMBED_TEXT("embedText", "嵌入文本"),
    EMBED_DASHBOARD("embedDashboard", "嵌入仪表板"),;

    private final String code;
    private final String message;

    IndexCardTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
