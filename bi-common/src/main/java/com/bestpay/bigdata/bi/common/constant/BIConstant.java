package com.bestpay.bigdata.bi.common.constant;

/**
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/11/12 16:32
 */
public interface BIConstant {

    /**查询脚本执行数*/
    int QUERY_SCRIPT_EXECUTE_COUNT = 5;

    /**查询脚本字符数*/
    int QUERY_SCRIPT_CHAR_LENGTH = 10000;

    //sql默认limit值
    int SQL_LIMIT_DEFAULT_VALUE = 1000;

    String APPLICATION_NAME ="BIGDATA-BI";

    // 部门安全管理员 - jira
    String DEPARTMENT_SEC_MANAGER = "customfield_25800";

    String NAME = "name";
    String SEPARATORS_DOT = ".";
    String BLANK_SPACE = " ";
    // 文件csv后缀
    String FILE_SUFFIX_CSV = "csv";
    // 文件zip后缀
    String FILE_SUFFIX_ZIP = "zip";
    // 内网访问
    String NETWORK_ACCESS_INNER= "inner";
    // 单引号
    String SINGLE_QUOTES= "'";
    String EMAIL = "email";
    String SEPARATORS_CN_DOT = "。";
    String SEPARATORS_CHAR = "/";
    String APPLICATION_MODULE_PROBE_NAME="BI-PROBE";

    String APPLICATION_MODULE_REPORT_NAME="BI-REPORT";

    String appKeyName="appKey";
    String SEPARATORS_COMMA = ",";
    String appKeySalt="bigdata-bi-appKey:";
    // 转移字符
    String ESCAPE_CHARACTER_DOT = "\\.";

    // 仪表板|数据大屏访问量统计
    String OBJECT_TYPE_REPORT = "report";
    String OBJECT_TYPE_REPORT_CARD = "report_card";
    String OBJECT_TYPE_DASHBOARD = "dashboard";
    String OBJECT_TYPE_DATASCREEN = "datascreen";
    String OBJECT_TYPE_NEW_DATASCREEN = "new_datascreen";
    String UNKNOWN_USER = "unknown";

    String DESCRIPTION = "申请原因：{}\n"
        + "数据条数：{}条\n"
        + "敏感字段：{}\n"
        + "表头字段：{}\n"
        + "是否重要数据：{}\n";


    String MODIFY_PERMISSION = "modify";
    /**
     * BI页面权限资源
     */
    String PAGE_BI_DASHBOARD_RESOURCE = "BI:dashboard";
    /**
     * 已经被删除的字段
     */
    String DEL_FIELD_TIP = "数据异常，数据集 %s 变更导致报表字段 %s 字段被删除，请重新配置";
    String MODIFY_FIELD_TIP = "数据异常，数据集 %s 维度/度量变更导致 %s 字段出现异常配置，请重新配置";
    String FIELD_TYPE_TIP = "数据异常，数据集 %s 字段类型变更导致 %s 字段出现异常配置，请重新配置";
    String NAME_REPEATED = "别名重复,uuid=%s, name=%s";

    /**
     * mysql 关键字  EXPLAIN
     */
    String MYSQL_KEYWORD_EXPLAIN = "EXPLAIN";
    String PREVIEW_SQL = "预览sql";
    String TOTAL_CALCULATION_SQL = "总计计算sql";

    String COMPUTE_COLUMN_FLAG = "compute_";

    /**
     * 标点符号，逗号
     */
    String COMMA_SYMBOL = ",";

    /**
     * 密钥
     */
    String ENCRYPT_CODE="bigdata-bi-datasource-config";

    /**
     * 卡片报表
     */
    String CARD_REPORT = "cardReport";
}
