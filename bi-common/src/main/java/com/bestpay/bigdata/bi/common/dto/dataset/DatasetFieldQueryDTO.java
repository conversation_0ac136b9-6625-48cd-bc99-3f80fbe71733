package com.bestpay.bigdata.bi.common.dto.dataset;

import lombok.Data;
import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class DatasetFieldQueryDTO implements Serializable {
    private static final long serialVersionUID = 3728415161519923371L;
    private String datasourceType;
    private String datasourceName;
    private String databaseName;
    private String tableName;
    private Set<Long> idList;
}
