package com.bestpay.bigdata.bi.common.dto.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewCardDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("卡片信息")
public class TableCardInfoDTO {

  @ApiModelProperty(value = "卡片code")
  private String cardCode;

  @ApiModelProperty(value = "卡片名称")
  private String cardName;

  @ApiModelProperty(value = "卡片类型")
  private String cardType;

  @ApiModelProperty(value = "卡片信息")
  private Object cardInfo;

  @ApiModelProperty(value = "卡片信息")
  private String message;

  public static TableCardInfoDTO createTableCard(String code, String cardName, String cardType,
      NewCardDTO cardDTO) {

    TableCardInfoDTO tableCard = new TableCardInfoDTO();
    tableCard.setCardCode(code);
    tableCard.setCardType(cardType);
    tableCard.setCardInfo(cardDTO);
    tableCard.setCardName(cardName);
    return tableCard;
  }
}
