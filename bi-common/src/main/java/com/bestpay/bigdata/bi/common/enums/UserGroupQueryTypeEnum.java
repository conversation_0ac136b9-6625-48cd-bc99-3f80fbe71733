package com.bestpay.bigdata.bi.common.enums;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 用户组查询类型枚举
 * @author: gaodingsong
 */
@Getter
@AllArgsConstructor
public enum UserGroupQueryTypeEnum {
    /**
     * 用户
     */
    USER("USER", "用户"),

    /**
     * 组织
     */
    ORGANIZATION("ORGANIZATION", "组织"),
    /**
     * 用户组
     */
    USER_GROUP("USER_GROUP", "用户组"),
    /**
     * 所有
     */
    ALL("ALL", "所有"),
    ;

    private final String code;
    private final String name;


    public static final List<String> USER_GROUP_AND_USER_LIST = Stream.of(USER.getCode(),USER_GROUP.getCode()).collect(Collectors.toList());
    public static UserGroupQueryTypeEnum getByCode(String code){
        for (UserGroupQueryTypeEnum value : UserGroupQueryTypeEnum.values()) {
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
