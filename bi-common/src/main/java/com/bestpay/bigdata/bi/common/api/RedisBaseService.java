package com.bestpay.bigdata.bi.common.api;

import com.bestpay.bigdata.bi.common.common.Constant;
import com.google.common.collect.Lists;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;

/**
 * @Author: dengyanwei
 * @CreateDate: 2021/3/10 10:44
 */
@Slf4j
@Service("redisService")
public class RedisBaseService implements RedisService {

    @Resource(name="biRedisRedisTemplate")
    private RedisTemplate redisTemplate;


    private final String KEY_PREFIX = Constant.REDIS_CACHE_NAME_SPACE + ":";
    private final String KEY_SUFFIX = "_new";
    private static final Long RELEASE_SUCCESS = 1L;

    /**
     * 删除缓存<br>
     * 根据key精确匹配删除
     *
     * @param key
     */
    @Override
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(KEY_PREFIX + key[0]);
            } else {
                List<String> list = Stream.of(key).map((e) -> KEY_PREFIX + e).collect(Collectors.toList());
                redisTemplate.delete(list);
            }
        }
    }

    /**
     * 获取缓存<br>
     * 注：基本数据类型(Character除外)，请直接使用get(String key, Class<T> clazz)取值
     *
     * @param key
     * @return
     */
    @Override
    public <T> Optional<T> getObj(String key) {
        Object obj = null;
        try {
            obj = redisTemplate.boundValueOps(KEY_PREFIX + key).get();
        } catch (Exception e) {
            log.error("从redis获取缓存信息失败:{}",e.getMessage(), e);
        }
        return Optional.ofNullable((T) obj);
    }

    /**
     * 获取Hash缓存<br>
     *
     * @param key
     * @param hashMapKey
     * @return
     */
    @Override
    public <T> Optional<T> getHashObj(String key, String hashMapKey) {
        Object obj = null;
        try {
            obj = redisTemplate.boundHashOps(KEY_PREFIX + key).get(hashMapKey);
        } catch (Exception e) {
            log.error("从redis获取缓存信息失败:{}",e.getMessage(), e);
        }
        return Optional.ofNullable((T) obj);
    }

    

    /**
     * 获取Hash缓存<br>
     *
     * @param key
     * @param key
     * @return
     */
    @Override
    public <T> Optional<T> getHashObj(String key) {
        Object obj = null;
        try {
            obj = redisTemplate.boundHashOps(KEY_PREFIX + key).entries();
        } catch (Exception e) {
            log.error("从redis获取缓存信息失败");
            log.error(e.getMessage(), e);
        }
        return Optional.ofNullable((T) obj);
    }

    /**
     * 获取Hash缓存 多个
     *
     * @param key
     * @param fields
     * @param <T>
     * @return
     */
    @Override
    public <T> Optional<List<T>> getHashObjects(String key, List<Object> fields) {
        List<T> list = null;
        try {
            list = (List<T>) redisTemplate.boundHashOps(KEY_PREFIX + key).multiGet(fields);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Optional.ofNullable(list);
    }

    @Override
    public <T,R> Optional<Map<T,R>> getHashObjects(String key) {
        Map<T,R> map = null;
        try {
            map = redisTemplate.boundHashOps(KEY_PREFIX + key).entries();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Optional.ofNullable(map);
    }

    /**
     * 获取Hash缓存<br>
     *
     * @param key
     * @param hashMapKey
     * @return
     */
    @Override
    public void delHashObj(String key, String hashMapKey) {
        try {
            redisTemplate.boundHashOps(KEY_PREFIX + key).delete(hashMapKey);
        } catch (Exception e) {
            log.error("从redis获取缓存信息失败");
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 放入缓存（带时间） 传入0或负数则不缓存
     *
     * @param key 键
     * @param obj 对象
     * @param expireTime 过期时间
     * @param timeUnit 单位
     * @return
     */
    @Override
    public boolean setObj(String key, Object obj, long expireTime, TimeUnit timeUnit) {
        if (obj == null) {
            log.info("无法将key:{}的对象存入redis", key);
            return false;
        }
        boolean flag = true;
        try {
            if (expireTime > 0) {
                //log.debug("key:{},expireTime:{},timeUnit:{}", key, expireTime, timeUnit);
                redisTemplate.opsForValue().set(KEY_PREFIX + key, obj, expireTime, timeUnit);
            } else {
                log.info("传入的时间非正整数，不进行缓存");
            }
        } catch (Exception e) {
            log.error("redis setObj value error",e);
            flag = false;
        }
        return flag;
    }

    /**
     * 放入缓存（永久）
     *
     * @param key 键
     * @param obj 对象
     * @return
     */
    @Override
    public boolean setObj(String key, Object obj) {
        boolean flag = true;
        try {
            redisTemplate.opsForValue().set(KEY_PREFIX + key, obj);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            flag = false;
        }
        return flag;
    }

    /**
     * 如果key不存在 则放入缓存（永久）
     *
     * @param key
     * @param obj
     * @return
     */
    @Override
    public boolean setNxObj(String key, Object obj) {
        boolean flag = true;
        try {
            redisTemplate.opsForValue().setIfAbsent(KEY_PREFIX + key, obj);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            flag = false;
        }
        return flag;
    }

    /**
     * 放入缓存HASH（永久）
     *
     * @param key 键
     * @param hashMap 对象
     * @return
     */
    @Override
    public boolean setHashObj(String key, Map hashMap) {
        if (MapUtils.isEmpty(hashMap)) {
            return false;
        }
        boolean flag = true;
        try {
            redisTemplate.opsForHash().putAll(KEY_PREFIX + key, hashMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            flag = false;
        }
        return flag;
    }

    /**
     * 获取缓存HASH
     *
     * @param key
     * @param <T>
     * @param <S>
     * @return
     */
    @Override
    public <T, S> Optional<Map<T, S>> entries(String key) {
        Map<T, S> entries = null;
        try {
            entries = (Map<T, S>) redisTemplate.opsForHash().entries(KEY_PREFIX + key);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Optional.ofNullable(entries);
    }

    /**
     * 放入缓存HASH（永久）
     *
     * @param key 键
     * @param field hash键
     * @param obj 对象
     * @return
     */
    @Override
    public boolean setHashObj(String key, String field, Object obj) {
        if (obj == null) {
            return false;
        }
        boolean flag = true;
        try {
            redisTemplate.opsForHash().put(KEY_PREFIX + key, field, obj);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            flag = false;
        }
        return flag;
    }

    /**
     * 放入缓存HASH（带时间）
     *
     * @param key 键
     * @param hashMap 对象
     * @param expireTime 过期时间
     * @param timeUnit 单位
     * @return
     */
    @Override
    public boolean setHashObj(String key, Map hashMap, long expireTime, TimeUnit timeUnit) {
        boolean flag = true;
        String newHashKey = KEY_PREFIX + key + KEY_SUFFIX;
        String oldHashKey = KEY_PREFIX + key;
        try {
            redisTemplate.opsForHash().putAll(newHashKey, hashMap);
            rename(newHashKey, oldHashKey, false);
            try {
                redisTemplate.expire(oldHashKey, expireTime, timeUnit);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                redisTemplate.expire(oldHashKey, expireTime, timeUnit);
            }
        } catch (Exception e) {
            redisTemplate.delete(newHashKey);
            log.error(e.getMessage(), e);
            flag = false;
        }
        return flag;
    }


    @Override
    public boolean setHashObj(String key, String innerKey, Object object, long expireTime, TimeUnit timeUnit) {
        boolean flag = true;
        try {
            redisTemplate.opsForHash().put(KEY_PREFIX + key, innerKey, object);
            redisTemplate.expire(KEY_PREFIX + key, expireTime, timeUnit);
        } catch (Exception e) {
            redisTemplate.delete(key);
            log.error("setHashObj fail",e);
            flag = false;
        }

        return flag;
    }


    @Override
    public List<Object> getValues(String key) {
        List<Object> objects = Lists.newArrayList();
        try {
            objects = redisTemplate.opsForHash().values(KEY_PREFIX + key);
        } catch (Exception e) {
            log.error("redis getValues fail",e);
        }

        return objects;
    }

    @Override
    public List<Object> getValuesByMultiKey(String key, List<String> hashKeys) {
        List<Object> objects = Lists.newArrayList();
        try {
            objects = redisTemplate.opsForHash().multiGet(key, hashKeys);
        } catch (Exception e) {
            log.error("redis getValuesByMultiKey fail",e);
        }

        return objects;
    }

    /**
     * key重命名
     *
     * @param oldKey
     * @param newKey
     * @param useKeyPrefix 是否增加keyPrefix
     */
    @Override
    public void rename(String oldKey, String newKey, boolean useKeyPrefix) {
        if (useKeyPrefix) {
            redisTemplate.boundValueOps(KEY_PREFIX + oldKey).rename(KEY_PREFIX + newKey);
        } else {
            redisTemplate.boundValueOps(oldKey).rename(newKey);
        }
    }

    /**
     * 递减操作
     *
     * @param key
     * @return
     */
    @Override
    public double decr(String key) {
        return redisTemplate.opsForValue().decrement(KEY_PREFIX + key);
    }
    /**
     * 递减操作
     *
     * @param key
     * @param by
     * @return
     */
    @Override
    public double decr(String key, double by) {
        return redisTemplate.opsForValue().increment(KEY_PREFIX + key, -by);
    }
    /**
     * 递增操作
     *
     * @param key
     * @param by
     * @return
     */
    @Override
    public double incr(String key, double by) {
        return redisTemplate.opsForValue().increment(KEY_PREFIX + key, by);
    }

    /**
     * 递增操作
     *
     * @param key
     * @return
     */
    @Override
    public Long getIncrementNum(String key) {
        RedisAtomicLong entityIdCounter =
            new RedisAtomicLong(KEY_PREFIX + key, redisTemplate.getConnectionFactory());
        Long current = entityIdCounter.get();
        if (current == 0) {
            return incr(key);
        }
        return entityIdCounter.incrementAndGet();
    }

    /**
     * @param key
     * @param value
     */
    @Override
    public void setIncr(String key, long value) {
        RedisAtomicLong counter =
            new RedisAtomicLong(KEY_PREFIX + key, redisTemplate.getConnectionFactory());
        counter.set(value);
    }

    /**
     * 递增操作
     *
     * @param key
     * @return
     */
    @Override
    public Long incr(String key) {
//        log.info(redisTemplate.opsForValue().toString());
        return redisTemplate.opsForValue().increment(KEY_PREFIX + key);
    }

    /**
     * 指定缓存的失效时间
     *
     * @param key 缓存KEY
     * @param time 失效时间(秒)
     * <AUTHOR>
     * @date 2016年8月14日
     */
    @Override
    public void expire(String key, long time, TimeUnit timeUnit) {
        if (time > 0) {
            redisTemplate.expire(KEY_PREFIX + key, time, timeUnit);
        }
    }



    /**
     * REDIS SET ADD操作
     *
     * @param key 键
     * @param value 对象
     * @return
     */
    @Override
    public boolean sAdd(String key, Object value) {
        if (key == null || value == null) {
            return false;
        }
        boolean flag = true;
        try {
            redisTemplate.boundSetOps(KEY_PREFIX + key).add(value);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            flag = false;
        }
        return flag;
    }

    @Override
    public Object sPop(String key) {
        if (key == null) {
            return null;
        }

        try {
            return redisTemplate.boundSetOps(KEY_PREFIX + key).pop();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * REDIS SET REMOVE操作
     *
     * @param key 键
     * @param value 对象
     * @return
     */
    @Override
    public boolean sRemove(String key, String value) {
        if (key == null || value == null) {
            return false;
        }
        boolean flag = true;
        try {
            redisTemplate.boundSetOps(KEY_PREFIX + key).remove(value);
        } catch (Exception e) {
            flag = false;
            log.error(e.getMessage(), e);
        }
        return flag;
    }

    /**
     * 返回是否是set中的成员
     *
     * @param key
     * @param value
     * @return
     */
    @Override
    public boolean sIsMember(String key, String value) {
        if (key == null || value == null) {
            return false;
        }
        try {
            return redisTemplate.boundSetOps(KEY_PREFIX + key).isMember(value);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * set 长度获取
     *
     * @param key
     * @return
     */
    @Override
    public Long sCard(String key) {
        if (key == null) {
            return Long.valueOf(0);
        }
        try {
            return redisTemplate.boundSetOps(KEY_PREFIX + key).size();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Long.valueOf(0);
        }
    }

    /**
     * 获取缓存<br>
     * 注：基本数据类型(Character除外)，请直接使用get(String key, Class<T> clazz)取值
     *
     * @param key
     * @return
     */
    @Override
    public <T> Object get(String key) {
        Object obj = null;
        Optional<Object> optional = getObj(key);
        if (optional != null && optional.isPresent()) {
            obj = optional.get();
        }
        return obj;
    }

    @Override
    public boolean hashKey(String key) {
        try {
            return redisTemplate.hasKey(KEY_PREFIX + key);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    @Override
    public Set<String> keys(String keyPrefix) {
        if (StringUtils.isEmpty(keyPrefix)) {
            return new HashSet<>();
        }

        Set<String> keys = (Set<String>) redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keysTmp = new HashSet<>();
            ScanOptions scanOptions = ScanOptions.scanOptions().match(KEY_PREFIX + keyPrefix + "*")
                    .count(100)
                    .build();
            Cursor<byte[]> cursor = connection.scan(scanOptions);
            while (cursor.hasNext()) {
                keysTmp.add(new String(cursor.next()));
            }
            return keysTmp;
        });

        return keys;
    }


    public boolean lock(String key, long expire) {
        String value = UUID.randomUUID().toString();
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        Boolean success = valueOperations.setIfAbsent(key, value, expire, TimeUnit.MILLISECONDS);
        return success != null && success;
    }

    public void unlock(String key) {
        redisTemplate.delete(key);
    }
}
