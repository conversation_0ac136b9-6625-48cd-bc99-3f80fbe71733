package com.bestpay.bigdata.bi.common.util;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/4/26 15:06
 */
@Slf4j
public class PageUtil {

  /**
   * 分页工具
   *
   * @param page        第几页
   * @param size        每页数量
   * @param list
   * @param columnNames 列名
   */
  public static List<Map<String, Object>> page(Integer page, Integer size,
      List<List<String>> list, String[] columnNames) {
    List<Map<String, Object>> data = Lists.newArrayList();
    int fromIndex = (page - 1) * size;
    int endIndex = fromIndex + size;
    for (int i = 0; i < list.size(); i++) {
      Map<String, Object> colMap = MapUtil.newHashMap();
      if (i >= fromIndex && i < endIndex) {
        for (int j = 0; j < list.get(i).size(); j++) {
          colMap.put(columnNames[j], list.get(i).get(j));
        }
        data.add(colMap);
      }
    }
    return data;
  }

  /**
   * 获取分页的 offset（偏移量）
   * @param pageNum 当前页码（从 1 开始）
   * @param pageSize 每页条数
   * @return offset 偏移量，从 0 开始计
   */
  public static int getOffset(int pageNum, int pageSize) {
    if (pageNum < 1) {
      pageNum = 1;
    }
    return (pageNum - 1) * pageSize;
  }

}
