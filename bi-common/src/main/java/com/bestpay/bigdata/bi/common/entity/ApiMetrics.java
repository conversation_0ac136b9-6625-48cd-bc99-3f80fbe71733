package com.bestpay.bigdata.bi.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2022-04-15-10:31
 */
@AllArgsConstructor
@Builder
@Data
@NoArgsConstructor
public class ApiMetrics implements Serializable {

    private static final long serialVersionUID = -6849194370764667710L;

    // 调用次数
    private long count = 0;

    // 接口调用累计耗时
    private long totalConsumeTime = 0;

    // 接口平均调用耗时
    private long avgConsumeTime = 0;

    // 最近一次调用耗时
    private long lastConsumeTime = 0;

    @Override
    public String toString() {
        return "ApiCallMetrics{" +
                "count=" + count +
                ", totalConsumeTime=" + totalConsumeTime +
                ", avgConsumeTime=" + avgConsumeTime +
                ", lastConsumeTime=" + lastConsumeTime +
                '}';
    }
}
