package com.bestpay.bigdata.bi.common.util;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.exception.HttpRequestException;
import java.util.Map;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;

/**
 * 发送http请求的工具类
 * <AUTHOR>
 * @date 2021/4/2 14:38
 **/
@Slf4j
public class HttpUtils {

    public static <T> T post(String url, Map<String, String> headers, Object data, Class<T> tClass) {
        return post(url, headers, data, s -> JSONUtil.toBean(s, tClass));
    }

    public static String post(String url, Map<String, String> headers, Object data) {
        return sendHttpRequest(Method.POST, url, headers, data);
    }

    public static <T> T post(String url, Map<String, String> headers, Object data, Function<String, T> mapper) {
        return sendHttpRequest(Method.POST, url, headers, data, mapper);
    }

    public static String get(String url, Map<String, String> headers, Object data) {
        return sendHttpRequest(Method.GET, url, headers, data);
    }

    public static <T> T get(String url, Map<String, String> headers, Object data, Function<String, T> mapper) {
        return sendHttpRequest(Method.GET, url, headers, data, mapper);
    }

    public static <T> T sendHttpRequest(Method method, String url, Map<String, String> headers, Object data, Function<String, T> mapper) {
        String body = sendHttpRequest(method, url, headers, data);
        return mapper.apply(body);
    }

    public static String sendHttpRequest(Method method, String url, Map<String, String> headers, Object data) {
        log.info("sendHttpRequest, method={}, url={}, headers={}, data={}",
                method, url, JSONUtil.toJsonStr(headers), JSONUtil.toJsonStr(data));

        HttpRequest httpRequest = HttpUtil.createRequest(method, url);
        if (MapUtil.isNotEmpty(headers)) {
            httpRequest.addHeaders(headers);
        }

        if (null != data) {
            httpRequest.body(JSONUtil.toJsonStr(data));
        }

        httpRequest.timeout(6000);
        HttpResponse httpResponse = null;

        try {
            httpResponse = httpRequest.execute();
            log.info(httpResponse.toString());
        } catch (Exception e) {
            log.info("execute httpRequest error{}", e);
            throw new HttpRequestException("接口调用异常, url="+url);
        }

        if (!httpResponse.isOk()) {
            throw new HttpRequestException("接口调用异常, url="+url+", msg："+httpResponse.body());
        }

        return httpResponse.body();
    }

    public static String sendHttpRequest(Method method, String url, Map<String, String> headers, Object data,int timeout) {
        log.info("sendHttpRequest, method={}, url={}, headers={}, data={}",
                method, url, JSONUtil.toJsonStr(headers), JSONUtil.toJsonStr(data));

        HttpRequest httpRequest = HttpUtil.createRequest(method, url);
        if (MapUtil.isNotEmpty(headers)) {
            httpRequest.addHeaders(headers);
        }

        if (null != data) {
            httpRequest.body(JSONUtil.toJsonStr(data));
        }

        httpRequest.timeout(timeout);
        HttpResponse httpResponse = null;

        try {
            httpResponse = httpRequest.execute();
            log.info(httpResponse.toString());
        } catch (Exception e) {
            log.info("execute httpRequest error{}", e);
            throw new HttpRequestException("接口调用异常, url="+url);
        }

        if (!httpResponse.isOk()) {
            throw new HttpRequestException("接口调用异常, url="+url+", msg："+httpResponse.body());
        }

        return httpResponse.body();
    }
}
