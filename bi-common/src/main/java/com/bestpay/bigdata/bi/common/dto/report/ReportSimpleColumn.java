package com.bestpay.bigdata.bi.common.dto.report;

import cn.hutool.core.collection.CollUtil;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.bestpay.bigdata.bi.common.exception.BusinessException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName: RollAndDown
 * Package: com.bestpay.bigdata.bi.report.bean.report
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/11/2 14:04
 * @Version 1.0
 */
@Data
@ApiModel("报表简单字段")
public class ReportSimpleColumn {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "type = 'field' name字段中文名称, type = 'layer' 层级名称")
    private String name;

    @ApiModelProperty(value = "展示类型名称")
    private String showTypeName;

    @ApiModelProperty(value = "日期组类型")
    private Integer dateGroupType;

    @ApiModelProperty(value = "英文字段名称")
    private String enName;

    /** field 非层级, layer 层级名称 */
    @ApiModelProperty(value = "字段类型")
    private String type;

    /** layer uuid */
    @ApiModelProperty(value = "uuid")
    private String uuid;

    @ApiModelProperty(value = "父ID")
    private String parentId;

    /** 标识层级下的child */
    private List<ReportSimpleColumn> childColumnList;

    /**
     * 重新设置uuid
     * @param reportStructureList
     * @param computerUuidMap
     */
    public static void setColumnUuid(List<ReportSimpleColumn> reportStructureList, Map<String, String> computerUuidMap) {

        if(CollUtil.isEmpty(reportStructureList)){
            return;
        }

        for (ReportSimpleColumn column : reportStructureList) {
            column.setUuid(getUuid(column.getUuid(),computerUuidMap));
            setColumnUuid(column.getChildColumnList(), computerUuidMap);
        }
    }


    private static String getUuid(String originUuid, Map<String, String> computerUuidMap){
        if(originUuid==null){
            return "";
        }
        if (!originUuid.startsWith(ReportUuidGenerateUtil.REPORT_COMPUTE_UUID_PREFIX)) {
            // 对于非报表里面的计算字段，无需生成uuid
            computerUuidMap.put(originUuid, originUuid);
            return originUuid;
        }

        String newUuid = computerUuidMap.get(originUuid);
        // 报表里面的计算字段需要重新生成
        if(newUuid==null){
            newUuid = ReportUuidGenerateUtil.generateReportComputeUuid();
            computerUuidMap.put(originUuid, newUuid);
        }
        return newUuid;
    }
}
