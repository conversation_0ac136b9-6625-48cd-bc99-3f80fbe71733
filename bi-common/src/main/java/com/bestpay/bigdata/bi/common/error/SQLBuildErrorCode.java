package com.bestpay.bigdata.bi.common.error;

public enum SQLBuildErrorCode implements ErrorCodeSupplier {

    UNKNOWN_DATE_FORMAT_TYPE("10001", ErrorType.USER_ERROR),
    UNKNOWN_SCENARIO("10002", ErrorType.USER_ERROR),
    INDEX_CONFIG_ERROR("10003", ErrorType.USER_ERROR),
    ROLL_AND_DOWN_ERROR("10004", ErrorType.USER_ERROR),
    LOD_FUNCTION_ERROR("10005", ErrorType.USER_ERROR),
    COMPUTE_FIELD_ERROR("10006", ErrorType.USER_ERROR),
    GRA<PERSON>AR_CHECK_ERROR("10007", ErrorType.USER_ERROR),

    ;

    private static final String PREFIX = "SQL_BUILD_";

    private final ErrorCode errorCode;

    SQLBuildErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
