package com.bestpay.bigdata.bi.common.config;

import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.jmx.ExceptionHappenCount;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.LogTraceIdGenerator;
import com.bestpay.bigdata.bi.common.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import javax.annotation.Priority;
import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;

/**
 * http call exception handler statics
 *
 * <AUTHOR>
 * @create 2022-04-13-16:50
 */
@Slf4j
@RestControllerAdvice
@Priority(1)
public class SpringExceptionHandler {

  private static ExceptionHappenCount exceptionHappenCount;

  {
    exceptionHappenCount = new ExceptionHappenCount();
    MBeanServer server = ManagementFactory.getPlatformMBeanServer();
    ObjectName backendExceptionCounter = null;
    try {
      backendExceptionCounter = new ObjectName("com.bestpay.bigdata.bi.exception:name=httpExceptionCounter");
      //create mbean and register mbean
      server.registerMBean(exceptionHappenCount, backendExceptionCounter);
    } catch (Exception e) {
      log.error("create mbean and register mbean error",e);
    }
  }

  @ExceptionHandler(value = NullPointerException.class)
  public Response<Void> handle(NullPointerException e) {

    log.error("springExceptionHandler handle fail {}", ExceptionUtils.getStackTrace(e));
    int count = exceptionHappenCount.getNullPointerExceptionCount();
    exceptionHappenCount.setNpCount(++count);
    exceptionHappenCount.incrementFiveMinuteNpCount();

    return Response.error(CodeEnum.DATA_ERROR.code(),
            CodeEnum.DATA_ERROR.message()+ MDC.get(LogTraceIdGenerator.TRACE_ID),
            LogUtil.getStackTrace(e));
  }


  @ExceptionHandler(value = OutOfMemoryError.class)
  public Response<Void> handle(OutOfMemoryError e) {
    log.error("springExceptionHandler handle fail",e);
    int count = exceptionHappenCount.getOutOfMemoryErrorCount();
    exceptionHappenCount.setOomCount(++count);
    exceptionHappenCount.incrementFiveMinuteOomCount();

    return Response.error(CodeEnum.DATA_ERROR.code(),
            CodeEnum.DATA_ERROR.message()+ MDC.get(LogTraceIdGenerator.TRACE_ID),
            LogUtil.getStackTrace(e));
  }
}
