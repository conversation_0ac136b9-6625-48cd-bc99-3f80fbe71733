package com.bestpay.bigdata.bi.common.dto.dataset;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(description = "数据集参数")
public class DatasetParamDTO {

    @ApiModelProperty(value = "数据集参数ID")
    private Long id;

    @ApiModelProperty(value = "参数中文名称")
    private String cnName;

    @ApiModelProperty(value = "参数英文名称")
    private String enName;

    @ApiModelProperty(value = "展示类型")
    private String showTypeName;

    @ApiModelProperty(value = "字段范围")
    private String filedRange;

    @ApiModelProperty(value = "参数值")
    private List<String> values;

    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    @ApiModelProperty(value = "日期筛选器参数")
    private String dateType;

    @ApiModelProperty(value = "日期筛选器参数-类型")
    private String filterType;

    @ApiModelProperty(value = "日期筛选器参数-时间类型")
    private String timeType;

    @ApiModelProperty(value = "日期筛选器参数-默认类型")
    private String defaultType;

    @ApiModelProperty(value = "日期筛选器参数-是否包含本周期")
    private Boolean includeCurrent;
}
