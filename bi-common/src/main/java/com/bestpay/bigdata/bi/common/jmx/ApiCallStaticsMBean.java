package com.bestpay.bigdata.bi.common.jmx;

/**
 * <AUTHOR>
 * @create 2022-04-14-14:50
 */
public interface ApiCallStaticsMBean {

    long getNormalCount();

    long getNormalTotalConsumeTime();

    long getNormalAvgConsumeTime();

    long getNormalLastConsumeTime();


    long getSuccessCount();

    long getSuccessTotalConsumeTime();

    long getSuccessAvgConsumeTime();

    long getSuccessLastConsumeTime();


    long getFailureCount();

    long getFailureTotalConsumeTime();

    long getFailureAvgConsumeTime();

    long getFailureLastConsumeTime();


    long getIntervalNormalCount();

    long getIntervalNormalTotalConsumeTime();

    long getIntervalNormalAvgConsumeTime();

    long getIntervalNormalLastConsumeTime();


    long getIntervalSuccessCount();

    long getIntervalSuccessTotalConsumeTime();

    long getIntervalSuccessAvgConsumeTime();

    long getIntervalSuccessLastConsumeTime();


    long getIntervalFailureCount();

    long getIntervalFailureTotalConsumeTime();

    long getIntervalFailureAvgConsumeTime();

    long getIntervalFailureLastConsumeTime();

}
