package com.bestpay.bigdata.bi.common.dto.dataset;

import com.bestpay.bigdata.bi.common.enums.DataSetFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Objects;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 维度实体类
 *
 * <AUTHOR>
 * @since 2021-12-10 15:33:04
 */
@Data
@ApiModel(value = "多维分析维度类")
public class Dimension implements Serializable {
    private static final long serialVersionUID = -55204504929317824L;


    /**
    * 数据集ID
    */
    @ApiModelProperty(name ="datasetId", value = "数据集ID",notes = "入参",required = true)
    private Long datasetId;
    /**
     * ID
     */
    @ApiModelProperty(value = "维度ID")
    private Long id;
    /**
    * 字段名称
    */
    @ApiModelProperty(value = "字段名称",notes = "入参")
    private String name;

    /**
    * 字段英文名称
    */
    @ApiModelProperty(value = "字段英文名称")
    private String enName;
    /**
     * 没有加转换函数的字段英文名称
     */
    @ApiModelProperty(value = "没有加转换函数的字段英文名称")
    private String originEnName;
    /**
     * 字段显示类型名称
     */
    @ApiModelProperty(value = "字段显示类型名称")
    private String showTypeName;
    /**
     * 类型名
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 显示度量或者维度
     */
    @ApiModelProperty(value = "显示度量或者维度")
    private String dimensionName;
    /**
     * 维度查询条件值
     */
    @ApiModelProperty(value = "维度查询条件值",notes = "入参")
    private Integer dimCondition;
    /**
     * 维度的查询条件字段
     */
    @ApiModelProperty(value = "是否是度量")
    private Integer isMeasure;
    /**
     * 是否是维度
     */
    @ApiModelProperty(value = "是否是维度")
    private Integer isDimenssion;


    private DataSetFieldTypeEnum fieldType;

    /**
     * uuid唯一标识
     */
    @ApiModelProperty(value = "uuid唯一标识")
    private String uuid;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Dimension dimension = (Dimension) o;
        return Objects.equals(datasetId, dimension.datasetId) &&
                Objects.equals(id, dimension.id) &&
                Objects.equals(name, dimension.name) &&
                Objects.equals(enName, dimension.enName) &&
                Objects.equals(originEnName, dimension.originEnName) &&
                Objects.equals(showTypeName, dimension.showTypeName) &&
                Objects.equals(typeName, dimension.typeName) &&
                Objects.equals(dimensionName, dimension.dimensionName) &&
                Objects.equals(dimCondition, dimension.dimCondition) &&
                Objects.equals(isMeasure, dimension.isMeasure) &&
                Objects.equals(isDimenssion, dimension.isDimenssion) &&
                Objects.equals(uuid, dimension.uuid) &&
                fieldType == dimension.fieldType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(datasetId, id, name, enName, originEnName, showTypeName, typeName, dimensionName, dimCondition, isMeasure, isDimenssion, fieldType, uuid);
    }

    public DatasetColumnConfigDTO getDatasetColumnConfigDTO(Dimension dimension) {
        DatasetColumnConfigDTO configDTO = new DatasetColumnConfigDTO();
        BeanUtils.copyProperties(dimension, configDTO);
        return configDTO;
    }
}