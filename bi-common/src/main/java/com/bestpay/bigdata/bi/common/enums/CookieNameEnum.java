package com.bestpay.bigdata.bi.common.enums;

/**
 * 响应码枚举
 *
 * <AUTHOR>
 * @date 2022/5/24 14:38
 **/
public enum CookieNameEnum {

    BIGDATA_AI_PLUS_USER_ID("bigdataAiPlusUserId", "cookie存储用户id"),;

    private final String code;

    private final String name;

    CookieNameEnum(String code, String message) {
        this.code = code;
        this.name = message;
    }

    public String code() {
        return this.code;
    }

    public String message() {
        return this.name;
    }
    public String message(String value) {
        return this.name+value;
    }

    @Override
    public String toString() {
        return "{" +
                "code='" + code + '\'' +
                ", message='" + name + '\'' +
                '}';
    }
}
