package com.bestpay.bigdata.bi.common.dto.report;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/4 16:24
 * @Description :
 **/
@Data
public class TotalFunction extends HighFunction {

    public static final String name = "total";

    /** if use with clause or view , logic table */
    private String logicTableName;

    /** function logic content as alise */
    private String logicFieldName;

    /** actual function logic content */
    private String functionLogicContent;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        TotalFunction that = (TotalFunction) o;
        return Objects.equals(logicTableName, that.logicTableName) && Objects.equals(logicFieldName, that.logicFieldName) && Objects.equals(functionLogicContent, that.functionLogicContent);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), logicTableName, logicFieldName, functionLogicContent);
    }
}
