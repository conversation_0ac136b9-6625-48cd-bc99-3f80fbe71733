package com.bestpay.bigdata.bi.common.dto.dataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 维度实体类
 *
 * <AUTHOR>
 * @since 2021-12-10 15:33:04
 */
@Data
@ApiModel(value = "查询维度条件类")
public class DimensionRequest implements Serializable {
    private static final long serialVersionUID = -55204504929317824L;
    

    /**
    * 数据集ID
    */
    //TODO 2022/1/4 validate isnull
    @ApiModelProperty(name ="datasetId", value = "数据集ID",notes = "入参",required = true)
    private Long datasetId;
    /**
    * 字段名称
    */
    @ApiModelProperty(value = "字段名称",notes = "入参")
    private String name;
    /**
     * 维度查询条件值
     */
    @ApiModelProperty(value = "维度查询条件值",notes = "入参")
    // 0 ALL  1 Dimension 2 measure
    private Integer dimCondition;
}