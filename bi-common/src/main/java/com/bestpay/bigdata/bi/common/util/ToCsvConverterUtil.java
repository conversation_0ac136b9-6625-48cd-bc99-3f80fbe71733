package com.bestpay.bigdata.bi.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import java.io.*;
import java.util.Iterator;

import static org.apache.poi.ss.usermodel.Row.MissingCellPolicy.CREATE_NULL_AS_BLANK;

/**
 * ClassName: ToCsvConverterUtil
 * Package: com.bestpay.bigdata.bi.common.util
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/9/26 13:47
 * @Version 1.0
 */
@Slf4j
public class ToCsvConverterUtil {


    public static InputStream convertExcelToCsv(InputStream excelInputStream) throws IOException {
        // 使用 XSSFWorkbook 加载 Excel 文件流
        // 通过文件流工厂方式来处理
        Workbook workbook = WorkbookFactory.create(excelInputStream);

        // 获取第一个工作表
        Sheet sheet = workbook.getSheetAt(0);

        // 创建写入 CSV 文件的 StringWriter
        StringWriter stringWriter = new StringWriter();
        PrintWriter csvWriter = new PrintWriter(stringWriter);

        // 使用迭代器遍历 Excel 行和单元格，并写入 CSV 文件
        Iterator<Row> rowIterator = sheet.iterator();

        boolean tableHeaderRow = false;
        int tableHeaderColNumbers = 0;
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();

            if (!tableHeaderRow) {
                tableHeaderRow = true;
                tableHeaderColNumbers = row.getPhysicalNumberOfCells(); // 表头列数
            }

            for (int i = 0; i < tableHeaderColNumbers; i++) {
                Cell cell = row.getCell(i, CREATE_NULL_AS_BLANK);
                String cellValue = getCellValueAsString(cell);
                csvWriter.print(cellValue);
                if (i < tableHeaderColNumbers - 1) {
                    csvWriter.print(",");
                }
            }

            csvWriter.println();
        }

        // 关闭流
        csvWriter.flush();
        csvWriter.close();
        workbook.close();

        // 将转换后的 CSV 数据写入 ByteArrayOutputStream
        ByteArrayOutputStream csvOutputStream = new ByteArrayOutputStream();
        csvOutputStream.write(stringWriter.toString().getBytes());

        // 获取 ByteArrayInputStream 作为 InputStream
        ByteArrayInputStream inputStream = new ByteArrayInputStream(csvOutputStream.toByteArray());
        return inputStream;
    }

    private static String getCellValueAsString(Cell cell) {
        cell.setCellType(CellType.STRING);

        if (cell.getCellType() == CellType.STRING) {
            return cell.getStringCellValue();
        } else if (cell.getCellType() == CellType.NUMERIC) {
            return cell.getStringCellValue();
        } else if (cell.getCellType() == CellType.BOOLEAN) {
            return String.valueOf(cell.getBooleanCellValue());
        } else {
            return "";
        }
    }


    /**
     * 功能：将输入流转换成 byte[]
     *
     * @param is
     * @return
     * @throws Exception
     */
    public static byte[] streamToByteArray(InputStream is) throws Exception {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();//创建输出流对象
        byte[] b = new byte[1024];
        int len;
        while ((len = is.read(b)) != -1) {
            bos.write(b, 0, len);
        }
        byte[] array = bos.toByteArray();
        bos.close();
        return array;
    }

}

