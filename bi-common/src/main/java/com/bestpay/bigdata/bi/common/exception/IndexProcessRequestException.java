package com.bestpay.bigdata.bi.common.exception;

import com.bestpay.bigdata.bi.common.enums.CodeEnum;

/**
 * 指标加工请求异常
 * <AUTHOR>
 */
public class IndexProcessRequestException extends BaseBiException {

    public IndexProcessRequestException(String code, String message) {
        super(code,message);
    }

    public IndexProcessRequestException(String message) {
        super(message);
    }

    public IndexProcessRequestException(CodeEnum codeEnum) {
        super(codeEnum);
    }
}