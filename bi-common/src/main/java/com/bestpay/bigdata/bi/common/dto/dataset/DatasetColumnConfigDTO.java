package com.bestpay.bigdata.bi.common.dto.dataset;

import cn.hutool.core.bean.BeanUtil;
import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.enums.DataSetFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 维度实体类
 *
 * <AUTHOR>
 * @since 2021-12-10 15:33:04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "多维分析维度类")
public class DatasetColumnConfigDTO implements Serializable {

    @ApiModelProperty(value = "数据集ID")
    private Long datasetId;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "字段名称")
    private String name;

    @ApiModelProperty(value = "字段英文名称")
    private String enName;

    @ApiModelProperty(value = "没有加转换函数的字段英文名称")
    private String originEnName;

    @ApiModelProperty(value = "字段显示类型名称")
    private String showTypeName;

    @ApiModelProperty(value = "类型名")
    private String typeName;

    @ApiModelProperty(value = "显示度量或者维度")
    private String dimensionName;

    @ApiModelProperty(value = "字段类型")
    private DataSetFieldTypeEnum fieldType;

    @ApiModelProperty(value = "字段值")
    private List<String> valueList;

    @ApiModelProperty(value = "字段范围")
    private String filedRange;

    @ApiModelProperty(value = "参数 默认值")
    private String defaultValue;

    @ApiModelProperty(value = "日期类型")
    private String dateType;

    @ApiModelProperty(value = "类型")
    private String filterType;

    @ApiModelProperty(value = "时间类型")
    private String timeType;

    @ApiModelProperty(value = "默认类型")
    private String defaultType;

    @ApiModelProperty(value = "是否包含本周期")
    private Boolean includeCurrent;

    @ApiModelProperty(value = "计算字段来源  1 仪表板  2 报表")
    private Byte computeSource;


    @ApiModelProperty(value = "中文-字段原名")
    private String originalName;

    @ApiModelProperty(value = "指标名称")
    private String indexName;

    @ApiModelProperty(value = "计算字段计算逻辑")
    private String fun;

    @ApiModelProperty(value = "报表字段")
    private String reportField;


    @ApiModelProperty(value = "是否统计小计")
    private Boolean showSubTotal;

    @ApiModelProperty(value = "是否是计算字段")
    private Boolean isComputeField = Boolean.FALSE;


    @ApiModelProperty(value = "日期组类型")
    private Integer dateGroupType;

    @ApiModelProperty(value = "uuid唯一标识")
    private String uuid;

    public ComputeComponentPropertyDTO coverDatasetColumnConfigDTO2ColumnProperty() {
        return BeanUtil.copyProperties(this, ComputeComponentPropertyDTO.class);
    }
}