package com.bestpay.bigdata.bi.common.error;

public enum SQLEngineErrorCode implements ErrorCodeSupplier {

    SYNTAX_ERROR("10001", ErrorType.SQL_ENGINE_ERROR),
    EXECUTE_ERROR("10002", ErrorType.SQL_ENGINE_ERROR),
    DATE_ERROR("10003", ErrorType.SQL_ENGINE_ERROR),
    FRIENDLY_TIPS("10004", ErrorType.SQL_ENGINE_ERROR),
    QUERY_ERROR("10005", ErrorType.SQL_ENGINE_ERROR),
    TRUNCATE_TABLE_ERROR("10006", ErrorType.SQL_ENGINE_ERROR),
    INSERT_ERROR("10007", ErrorType.SQL_ENGINE_ERROR),


    ;

    private static final String PREFIX = "SQL_ENGINE_";

    private final ErrorCode errorCode;

    SQLEngineErrorCode(String code, ErrorType type) {
        errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
