package com.bestpay.bigdata.bi.common.oss;

import com.bestpay.bigdata.bi.common.enums.FileSystemTypeEnum;

/**
 * ClassName: OssService
 * Package: com.bestpay.bigdata.bi.common.oss
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/11/29 14:40
 * @Version 1.0
 */
public interface OssService
{
    String upload(OssContext ossContext);

    byte[] download(OssContext ossContext);

    FileSystemTypeEnum getFileSystemType();

    String parseHttpUrl(OssContext ossContext);

    void delete(OssContext ossContext);

    boolean judgeResourceExists(OssContext ossContext);
}
