package com.bestpay.bigdata.bi.common.limit.jmx;

/**
 * <AUTHOR>
 * @date 2023/5/18
 */
public class SeatLimitCenterJmx implements SeatLimitCenterJmxMBean {
    private int currentQueueSize;
    private long threadWaitLongestTime;

    @Override
    public int getCurrentQueueSize() {
        return this.currentQueueSize;
    }

    @Override
    public void setCurrentQueueSize(int currentQueueSize) {
        this.currentQueueSize = currentQueueSize;
    }

    @Override
    public long getThreadWaitLongestTime() {
        return this.threadWaitLongestTime;
    }

    @Override
    public void setThreadWaitLongestTime(long threadWaitLongestTime) {
        this.threadWaitLongestTime = threadWaitLongestTime;
    }
}
