package com.bestpay.bigdata.bi.common.dto.analysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * @author: lcy
 * @date: 2021/12/21
 **/
@Data
@Builder
@ApiModel("执行单次执行返回数据")
public class ExecuteAnalysisResponse {

    @ApiModelProperty(value = "指标的query id")
    private String queryId;
    @ApiModelProperty(value = "指标名称")
    private String indexName;
    @ApiModelProperty(value = "显示数据的方式（图|表）")
    private String showMode;
    @ApiModelProperty(value = "查询的指标类型（0:单指标|1:多维指标）")
    private Integer indexType;
    @ApiModelProperty(value = "维度名称(英文->中文)")
    private Map<String,String> dimensionName;
    @ApiModelProperty(value = "聚合方式")
    private String polymerization;
}
