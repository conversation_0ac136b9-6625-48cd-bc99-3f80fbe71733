package com.bestpay.bigdata.bi.common.error;

public enum FileErrorCode implements ErrorCodeSupplier {
    FILE_NOT_EXIST("00001",ErrorType.BUSINESS_ERROR),
    FILE_PROCESSING_ERROR("00002",ErrorType.BUSINESS_ERROR),


    FILE_GENERATED_ERROR("20001",ErrorType.INTERNAL_ERROR),
    FILE_DOWNLOAD_ERROR("20002",ErrorType.INTERNAL_ERROR),
    FILE_UPLOAD_ERROR("20003",ErrorType.INTERNAL_ERROR),
    FILE_COMPRESS_FILE_ERROR("20004",ErrorType.INTERNAL_ERROR),
    FILE_DOWNLOAD_SCENE_ERROR("20005",ErrorType.INTERNAL_ERROR),

    ;

    private static final String PREFIX = "FILE_";

    private final ErrorCode errorCode;

    FileErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
