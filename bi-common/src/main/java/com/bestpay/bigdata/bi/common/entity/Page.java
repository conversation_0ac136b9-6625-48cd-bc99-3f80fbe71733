package com.bestpay.bigdata.bi.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("分页类")
public class Page {

    /**当前页数*/
    @ApiModelProperty(value = "当前页数",required = true)
    private Integer pageNum;
    /**每页条数*/
    @ApiModelProperty(value = "每页条数",required = true)
    private Integer pageSize;
    /**总页数*/
    @ApiModelProperty(value = "总页数")
    private Integer pageTotal;
    /**总条数*/
    @ApiModelProperty(value = "总条数")
    private Integer totalCounts;
}
