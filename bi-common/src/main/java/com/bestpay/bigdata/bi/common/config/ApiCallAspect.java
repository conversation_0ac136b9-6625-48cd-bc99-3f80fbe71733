package com.bestpay.bigdata.bi.common.config;

import com.bestpay.bigdata.bi.common.entity.ApiState;
import com.bestpay.bigdata.bi.common.jmx.AllApiCallStatics;
import com.bestpay.bigdata.bi.common.jmx.ApiCallStatics;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import javax.servlet.http.HttpServletRequest;
import java.lang.management.ManagementFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * http dubbo call metrics statics
 * <AUTHOR>
 * @create 2022-04-14-14:42
 */
@Aspect
@Component
@Slf4j
public class ApiCallAspect {

    //统计请求的处理时间
    ThreadLocal<Long> startTime = new ThreadLocal<>();

    private static AllApiCallStatics allApiCallStatics = new AllApiCallStatics();
    private Set<String> apiNameSet = new CopyOnWriteArraySet<>();

    @Pointcut("execution(* com.bestpay.bigdata.bi.*.controller.*Controller.*(..)) "
            + "&& !execution(* com.bestpay.bigdata.bi.*.controller.HeartBeatController.*(..))"
            + "|| execution(* com.bestpay.bigdata.bi.*.api.*Service*.*(..))")
    public void apiCallAspect() {}

    @Before("apiCallAspect()")
    public void beginCall(JoinPoint joinPoint) {
        String key = joinPoint.getSignature().getDeclaringTypeName().concat(".").concat(joinPoint.getSignature().getName());
        if(!apiNameSet.contains(key)){
            apiNameSet.add(key);
            MBeanServer server = ManagementFactory.getPlatformMBeanServer();
            ObjectName apiStatics = null;
            try {
                String objName = joinPoint.getSignature().getDeclaringTypeName().concat(":name=").concat(joinPoint.getSignature().getName());
                apiStatics = new ObjectName(objName);
                //create mbean and register mbean
                server.registerMBean(new ApiCallStatics(allApiCallStatics,key), apiStatics);
            } catch (Exception e) {
                log.info("create mbean and register mbean error", e);
            }
        }
        startTime.set(System.currentTimeMillis());
        allApiCallStatics.updateCountStatics(key,0L, ApiState.normal);
        allApiCallStatics.updateIntervalStatics(key,0L,ApiState.normal);
    }

    @AfterReturning("apiCallAspect()")
    public void afterCall(JoinPoint joinPoint){
        String key = joinPoint.getSignature().getDeclaringTypeName().concat(".").concat(joinPoint.getSignature().getName());
        Long consumeTime = System.currentTimeMillis() - startTime.get();
        allApiCallStatics.updateCountStatics(key,consumeTime,ApiState.success);
        allApiCallStatics.updateIntervalStatics(key,consumeTime,ApiState.success);
        allApiCallStatics.updateIntervalStatics(key,consumeTime,ApiState.normal);
        allApiCallStatics.updateCountStatics(key,consumeTime,ApiState.normal);
    }

    @AfterThrowing("apiCallAspect()")
    public void exceptionCall(JoinPoint joinPoint){
        String key = joinPoint.getSignature().getDeclaringTypeName().concat(".").concat(joinPoint.getSignature().getName());
        Long consumeTime = System.currentTimeMillis() - startTime.get();
        allApiCallStatics.updateCountStatics(key,consumeTime,ApiState.failure);
        allApiCallStatics.updateIntervalStatics(key,consumeTime,ApiState.failure);
        allApiCallStatics.updateIntervalStatics(key,consumeTime,ApiState.normal);
        allApiCallStatics.updateCountStatics(key,consumeTime,ApiState.normal);
    }

}
