package com.bestpay.bigdata.bi.common.dto.datascreen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("组件信息")
public class ComponentInfo {
    @ApiModelProperty(value = "组件code")
    private String componentCode;
    @ApiModelProperty(value = "组件名称")
    private String componentName;
    @ApiModelProperty(value = "组件类型")
    private String componentType;
    @ApiModelProperty(value = "是否显示")
    private Boolean isShow;
    @ApiModelProperty(value = "排序")
    private Integer orderNo;

    @ApiModelProperty(value = "组件信息")
    private List<ComponentInfo> componentInfoList;
    @ApiModelProperty(value = "组件位置")
    private DataScreenComponentLocationDTO location;
    /**
     * 是否锁定
     */
    @ApiModelProperty(value = "是否锁定")
    private Boolean isLock;
}
