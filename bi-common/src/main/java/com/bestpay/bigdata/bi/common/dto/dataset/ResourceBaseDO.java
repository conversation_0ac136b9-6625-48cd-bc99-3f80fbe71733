package com.bestpay.bigdata.bi.common.dto.dataset;

import lombok.Data;

/**
 * @Author：Song
 * @Date：2024/12/6 15:11
 * @Desc:
 */
@Data
public class ResourceBaseDO {


    /**
     * 资源ID
     */
    private Long resId;

    /**
     * 资源类型
     */
    private String resType;
    /**
     *责任人
     */
    private String ownerCn;


    private String ownerName;
    /**
     *资源名称
     */
    private String resName;

    /**
     * 创建时间
     */
    private String createdAt;


    /**
     * 最近一次访问时间
     */
    private String lastAccessDt;

    /**
     * 近90天的访问量
     */
    private String accessCnt;


    /**
     * 数据大屏跳转的时候需要
     */
    private String dataScreenUUID;


    /**
     * 数据大屏跳转的时候需要
     */
    private String versionType;
}
