package com.bestpay.bigdata.bi.common.request;

import com.bestpay.bigdata.bi.common.dto.dashboard.DashboardCardLocationDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.DashboardPageSettingModel;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "卡片发布参数")
public class TableCardPublishRequest{
    @ApiModelProperty(value = "仪表板ID")
    private Long dashboardId;
    @ApiModelProperty(value = "是否发布PC端")
    private Integer isPublishMobile;
    @ApiModelProperty(value = "卡片信息")
    private List<CardInfo> data;

    @ApiModelProperty(value = "仪表板页面设置model")
    private DashboardPageSettingModel dashboardPageSettingModel;

    @Data
    @ApiModel(description = "卡片信息")
    public static class CardInfo {
        @ApiModelProperty(value = "卡片code")
        private String cardCode;
        @ApiModelProperty(value = "卡片名称")
        private String cardName;
        @ApiModelProperty(value = "卡片类型")
        private String cardType;
        @ApiModelProperty(value = "排序")
        private Integer orderNo;
        @ApiModelProperty(value = "是否显示")
        private Boolean isShow;
        @ApiModelProperty(value = "卡片位置")
        private DashboardCardLocationDTO pcLocation;
        @ApiModelProperty(value = "卡片位置")
        private DashboardCardLocationDTO mobileLocation;
        @ApiModelProperty(value = "卡片信息")
        private List<CardInfo> cardList;
    }
}
