package com.bestpay.bigdata.bi.common.config;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.scope.refresh.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SpringBootApolloRefreshConfig {

  @Resource
  private  AuthConfig authConfig;
  // TODO: 2022/3/31 dyw 抽取sftp链接为热更新
  @Resource
  private  DataSourceConfig dataSourceConfig;
  @Resource
  private  ApolloRefreshConfig apolloRefreshConfig;
  @Resource
  private RefreshScope refreshScope;

  @ApolloConfigChangeListener(value={"application","dubbo","logging","redis.yml",
      "auth-config.yml","mysql","dataSource","dynamic_refresh"})
  public void onChange(ConfigChangeEvent changeEvent) {
    log.info("before refresh authConfig:{},dataSourceConfig:{},apolloRefreshConfig{}",
        authConfig,dataSourceConfig,apolloRefreshConfig
        );
    refreshScope.refresh("authConfig");
    refreshScope.refresh("dataSourceConfig");
    refreshScope.refresh("apolloRefreshConfig");
    refreshScope.refreshAll();
    log.info("after refresh authConfig:{},dataSourceConfig:{},apolloRefreshConfig{}",
        authConfig,dataSourceConfig,apolloRefreshConfig
    );
  }
}
