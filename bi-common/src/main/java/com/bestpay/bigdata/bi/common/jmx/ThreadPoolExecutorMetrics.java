package com.bestpay.bigdata.bi.common.jmx;


import com.facebook.presto.jdbc.internal.io.airlift.units.Duration;

import java.util.concurrent.ThreadPoolExecutor;

import static java.util.Objects.requireNonNull;
import static java.util.concurrent.TimeUnit.NANOSECONDS;

/**
 * <AUTHOR>
 * @create 2022-04-08-16:21
 */
public class ThreadPoolExecutorMetrics implements ThreadPoolExecutorMetricsMBean {

    private final ThreadPoolExecutor threadPoolExecutor;

    public ThreadPoolExecutorMetrics(ThreadPoolExecutor threadPoolExecutor)
    {
        this.threadPoolExecutor = requireNonNull(threadPoolExecutor, "threadPoolExecutor is null");
    }

    @Override
    public boolean isShutdown()
    {
        return threadPoolExecutor.isShutdown();
    }

    @Override
    public boolean isTerminating()
    {
        return threadPoolExecutor.isTerminating();
    }

    @Override
    public boolean isTerminated()
    {
        return threadPoolExecutor.isTerminated();
    }

    @Override
    public String getRejectedExecutionHandler()
    {
        return threadPoolExecutor.getRejectedExecutionHandler().getClass().getName();
    }

    @Override
    public int getCorePoolSize()
    {
        return threadPoolExecutor.getCorePoolSize();
    }

    @Override
    public void setCorePoolSize(int corePoolSize)
    {
        threadPoolExecutor.setCorePoolSize(corePoolSize);
    }

    @Override
    public int getMaximumPoolSize()
    {
        return threadPoolExecutor.getMaximumPoolSize();
    }

    @Override
    public void setMaximumPoolSize(int maximumPoolSize)
    {
        threadPoolExecutor.setMaximumPoolSize(maximumPoolSize);
    }

    @Override
    public int getPoolSize()
    {
        return threadPoolExecutor.getPoolSize();
    }

    @Override
    public int getActiveCount()
    {
        return threadPoolExecutor.getActiveCount();
    }

    @Override
    public int getLargestPoolSize()
    {
        return threadPoolExecutor.getLargestPoolSize();
    }

    @Override
    public String getKeepAliveTime()
    {
        return new Duration(threadPoolExecutor.getKeepAliveTime(NANOSECONDS), NANOSECONDS)
                .convertToMostSuccinctTimeUnit()
                .toString();
    }

    @Override
    public void setKeepAliveTime(String duration)
    {
        requireNonNull(duration, "duration is null");
        threadPoolExecutor.setKeepAliveTime(Duration.valueOf(duration).roundTo(NANOSECONDS), NANOSECONDS);
    }

    @Override
    public boolean isAllowCoreThreadTimeOut()
    {
        return threadPoolExecutor.allowsCoreThreadTimeOut();
    }

    @Override
    public void setAllowCoreThreadTimeOut(boolean allowsCoreThreadTimeOut)
    {
        threadPoolExecutor.allowCoreThreadTimeOut(allowsCoreThreadTimeOut);
    }

    @Override
    public long getTaskCount()
    {
        return threadPoolExecutor.getTaskCount();
    }

    @Override
    public long getCompletedTaskCount()
    {
        return threadPoolExecutor.getCompletedTaskCount();
    }

    @Override
    public int getQueuedTaskCount()
    {
        return threadPoolExecutor.getQueue().size();
    }
}
