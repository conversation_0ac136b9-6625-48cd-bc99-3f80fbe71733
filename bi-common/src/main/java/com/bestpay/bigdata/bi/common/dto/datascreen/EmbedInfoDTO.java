package com.bestpay.bigdata.bi.common.dto.datascreen;

import lombok.Data;

@Data
public class EmbedInfoDTO {
    /** 0桌面端 1移动端 */
    private Integer isPublishMobile;

    /**
     * 网络环境 0：内部网络 1：外部网络
     */
    private Integer networkType;

    /**
     * 嵌入对象id
     */
    private Long embedObjectId;

    /**
     * 嵌入类型
     */
    private String embedType;

    /**
     * 嵌入对象类型
     */
    private String embedObjectName;
    /**
     * 对接平台
     */
    private String platform;

}
