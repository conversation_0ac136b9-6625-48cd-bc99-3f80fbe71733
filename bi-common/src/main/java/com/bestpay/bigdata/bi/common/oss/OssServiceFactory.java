package com.bestpay.bigdata.bi.common.oss;

import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.enums.FileSystemTypeEnum;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * ClassName: OssServiceFactory
 * Package: com.bestpay.bigdata.bi.common.oss
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/11/29 16:33
 * @Version 1.0
 */
@Component
@Slf4j
public class OssServiceFactory implements InitializingBean, ApplicationContextAware {
    @Resource
    private ApolloRefreshConfig apolloRefreshConfig;

    private ApplicationContext applicationContext;
    private static Map<FileSystemTypeEnum, AbstractOssService> ossServiceHashMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        log.info("OssServiceFactory:loadDataSourceHandler");
        Map<String, AbstractOssService> beanMap = applicationContext.getBeansOfType(AbstractOssService.class);

        for (AbstractOssService impl : beanMap.values()) {
            log.info("AbstractOssService impl : {}", impl.getFileSystemType());
            ossServiceHashMap.put(impl.getFileSystemType(), impl);
        }
    }

    public OssService getOssServiceByType() {
        String fileSystemType = apolloRefreshConfig.getFileSystem();
        if (fileSystemType.equalsIgnoreCase(FileSystemTypeEnum.MINIIO.getCode())) {
            return getHandlerByEngine(FileSystemTypeEnum.MINIIO);
        } else if (fileSystemType.equalsIgnoreCase(FileSystemTypeEnum.MINIO_ORIGINAL.getCode())) {
            return getHandlerByEngine(FileSystemTypeEnum.MINIO_ORIGINAL);
        }
        return null;
    }

    public FileSystemTypeEnum getCurrentSystemUseFileSystemType() {
        String fileSystemType = apolloRefreshConfig.getFileSystem();
        if (fileSystemType.equalsIgnoreCase(FileSystemTypeEnum.MINIIO.getCode())) {
            return FileSystemTypeEnum.MINIIO;
        } else if (fileSystemType.equalsIgnoreCase(FileSystemTypeEnum.MINIO_ORIGINAL.getCode())) {
            return FileSystemTypeEnum.MINIO_ORIGINAL;
        }
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public AbstractOssService getHandlerByEngine(FileSystemTypeEnum typeEnum) {
        return ossServiceHashMap.get(typeEnum);
    }
}
