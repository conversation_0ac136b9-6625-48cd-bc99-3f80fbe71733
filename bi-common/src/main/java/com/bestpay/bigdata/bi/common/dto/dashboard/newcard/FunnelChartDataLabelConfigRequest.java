package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FunnelChartDataLabelConfigRequest extends LabelBaseConfigRequest{
    /**
     * 是否展示数值
     */
    private Boolean showValue;
    /**
     * 数值字体大小
     */
    private String valueFontSize;
    /**
     * 数值字体颜色
     */
    private String valueFontColor;

    /**
     * 是否展示类别
     */
    private Boolean showCategory;
    /**
     * 类别字体大小
     */
    private String categoryFontSize;
    /**
     * 类别字体颜色
     */
    private String categoryFontColor;

    /**
     * 是否展示上步转化率
     */
    private Boolean showPreviousConversionRate;
    /**
     * 上步转化率字体大小
     */
    private String previousConversionRateFontSize;
    /**
     * 上步转化率字体颜色
     */
    private String previousConversionRateFontColor;

    /**
     * 是否展示整体转化率
     */
    private Boolean showOverallConversionRate;
    /**
     * 整体转化率字体大小
     */
    private String overallConversionRateFontSize;
    /**
     * 整体转化率字体颜色
     */
    private String overallConversionRateFontColor;
}
