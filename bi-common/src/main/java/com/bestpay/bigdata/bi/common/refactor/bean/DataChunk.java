package com.bestpay.bigdata.bi.common.refactor.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.function.Supplier;

@Data
public class DataChunk implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询ID，用于标识本次查询
     */
    private String queryId;
    
    /**
     * 当前分块序号
     */
    private int chunkIndex;
    
    /**
     * 当前分块的起始行号
     */
    private long startRowNum;
    
    /**
     * 当前分块行数
     */
    private long chunkRowCount;
    
    /**
     * 总行数（用于分页计算）
     */
    private long totalRowCount;
    
    /**
     * 当前分块的原始数据
     * List<List<String>> 格式，外层List代表行，内层List代表列
     */
    private transient Supplier<List<List<String>>> dataSupplier;

    /**
     * 用于存储数据句柄，Redis Key
     */
    private String dataHandle;
    
    /**
     * 是否为最后一个分块
     */
    private boolean isLastChunk;

    public List<List<String>> getData() {
        if (this.dataSupplier == null) {
            throw new IllegalStateException("Data supplier has not been initialized. Cannot fetch data.");
        }

        return this.dataSupplier.get();
    }
}
