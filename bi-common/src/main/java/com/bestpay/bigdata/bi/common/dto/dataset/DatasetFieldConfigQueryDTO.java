package com.bestpay.bigdata.bi.common.dto.dataset;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class DatasetFieldConfigQueryDTO implements Serializable {


    private static final long serialVersionUID = 3975118968469415433L;

    /**dataset code*/
    private String datasetCode;

    /**element code @DatasetElement 唯一code*/
    private String elementCode;

    /**field id @DatasetField 主键id*/
    private Long fieldId;

    private Set<Long> idList;

    /**状态*/
    private Integer statusCode;

    private List<String> datasetCodes;
}
