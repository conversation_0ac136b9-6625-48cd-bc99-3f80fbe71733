package com.bestpay.bigdata.bi.common.dto.datascreen;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("数据大屏信息")
public class DataScreenInfo {

  @ApiModelProperty(value = "大屏名称")
  @Length(max = 50,message = "大屏名称不能超过50个字符")
  @NotBlank(message = "大屏名称不能为空")
  private String name;
  @ApiModelProperty(value = "大屏主题")
  private Integer theme;
  @ApiModelProperty(value = "大屏字体")
  private Integer font;
  @ApiModelProperty(value = "大屏高度")
  private Integer height;
  @ApiModelProperty(value = "大屏宽度")
  private Integer width;
  @ApiModelProperty(value = "大屏背景颜色")
  private String bgColor;
  @ApiModelProperty(value = "大屏背景图片")
  private String bgImageUrl;
  @ApiModelProperty(value = "大屏缩放类型")
  private String scaleType;
  @ApiModelProperty(value = "大屏状态")
  private String statusCode;
  @ApiModelProperty(value = "大屏版本")
  private String versionType;
  /**
   * 大屏状态
   */
  @ApiModelProperty(value = "大屏状态")
  private String status;

  /**
   * 屏幕大小
   */
  @ApiModelProperty(value = "屏幕大小")
  private int size;
  /**
   * 刷新间隔
   */
  @ApiModelProperty(value = "刷新间隔")
  private int refreshTime;

}
