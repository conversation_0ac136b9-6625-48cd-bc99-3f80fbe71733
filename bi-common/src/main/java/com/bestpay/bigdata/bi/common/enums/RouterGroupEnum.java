package com.bestpay.bigdata.bi.common.enums;

/**
 * <AUTHOR>
 */
public enum RouterGroupEnum {

    ADHOC("adhoc","adhoc"),
    BATCH("batch","batch"),;

    private final String code;

    private final String message;

    RouterGroupEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String code() {
        return this.code;
    }

    public String message() {
        return this.message;
    }
    public String message(String value) {
        return this.message+value;
    }

}
