package com.bestpay.bigdata.bi.common.dto.common;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DatePickerDTO {

  private String uuid;
  private Long id;

  /**
   * 日期类型
   */
  private String dateType;

  /**
   * 筛选类型
   */
  private String filterType;

  /**
   * 时间类型
   */
  private String timeType;

  /**
   * 默认类型
   */
  private String defaultType;

  /**
   * 默认值
   */
  private List<String> defaultValues;

  /**
   * 是否包含本周期
   */
  private Boolean includeCurrent;
}
