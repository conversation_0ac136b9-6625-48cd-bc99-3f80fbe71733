package com.bestpay.bigdata.bi.common.enums;

import lombok.Getter;

/**
 * <AUTHOR> @Description: 查询类型
 * @date 2022/01/17
 */
public enum QueryType {
    /**
     * 多维分析
     */
    QUERY_MULTI_ANALYSIS(0, "多维分析"),
    /**
     * 数据探查
     */
    QUERY_PROBE(1, "数据探查"),
    /**
     *
     */
    QUERY_REPORT(2, "报表中心"),
    /**
     * open api
     */
    OPEN_API(3, "openapi")
    ;


    @Getter
    private final int code;

    @Getter
    private final String msg;

    QueryType(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getNameByCode(Integer code){
        if(code==null){
            return "";
        }

        for (QueryType value : QueryType.values()) {
            if(value.getCode()==code){
                return value.getMsg();
            }
        }

        return "";
    }

}
