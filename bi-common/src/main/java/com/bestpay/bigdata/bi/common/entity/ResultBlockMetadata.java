package com.bestpay.bigdata.bi.common.entity;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2023-02-13-17:47
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ResultBlockMetadata implements Serializable {
    private static final long serialVersionUID = -1;
    private int number;
    private String queryId;
    private long rowCount;
    private Boolean isEmpty;
}
