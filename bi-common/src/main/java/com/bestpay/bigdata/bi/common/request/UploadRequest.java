package com.bestpay.bigdata.bi.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/7
 */
@Data
public class UploadRequest implements Serializable {
    private String tableName;
    private String username;
    @ApiModelProperty(value = "查询引擎",required = true,allowableValues ="presto,hive,kylin,spark_sql,clickhouse")
    private String sqlEngine;
    @ApiModelProperty(value = "数据源",required = true,allowableValues ="hive_11,hive_36,ck_1,ck_2,ck_3,ck_4,ck_5")
    private String databaseSource;
    @ApiModelProperty(value = "访问系统",required = true,allowableValues ="bi,fengkong,edm")
    private String requestSystem;
}
