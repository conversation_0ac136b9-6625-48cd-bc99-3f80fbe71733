package com.bestpay.bigdata.bi.common.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.zip.CRC32;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName UserCodeGenerator
 * @description 单例模式的用户Code生成器 格式: {时间戳缩短}_{节点ID}_{计数器}_{CRC校验}
 * @date 2025/7/2
 */

@Component
public class UserCodeGenerator {

  // 时间戳格式(yyMMddHHmm)
  private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyMMddHHmm");

  // 随机字符集
  private static final String RANDOM_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

  // 节点ID(自动生成)
  private final String nodeId;
  // 锁，保证同一毫秒内生成的Code唯一
  private final ReentrantLock lock = new ReentrantLock();
  // 同一时间部分的计数器
  private final AtomicInteger counter = new AtomicInteger(0);
  // 上一次生成的时间部分
  private String lastTimePart = "";

  /**
   * 公共构造函数（Spring管理）
   */
  public UserCodeGenerator() {
    // 自动生成节点ID
    this.nodeId = generateNodeId();
  }

  /**
   * 生成用户Code
   *
   * @param prefix
   * @return
   */
  public String generateUserCode(String prefix) {
    return generate(prefix);
  }

  /**
   * 验证用户Code格式
   */
  public boolean validateUserCode(String perfix, String userCode) {
    if (userCode == null || userCode.length() != 20) {
      return false;
    }

    if (!userCode.startsWith(perfix)) {
      return false;
    }

    // 验证时间部分格式
    String timePart = userCode.substring(3, 13);
    try {
      LocalDateTime.parse(timePart, TIME_FORMATTER);
    } catch (Exception e) {
      return false;
    }

    // 验证节点ID格式

    String nodeIdPart = userCode.substring(13, 15);
    for (char c : nodeIdPart.toCharArray()) {
      if (!Character.isLetterOrDigit(c)) {
        return false;
      }
    }

    // 验证计数器部分格式
    String counterPart = userCode.substring(15, 17);
    try {
      Integer.parseInt(counterPart);
    } catch (NumberFormatException e) {
      return false;
    }

    // 验证CRC校验
    String baseCode = userCode.substring(0, 18);
    String crcPart = userCode.substring(18);
    return crcPart.equals(calculateCrc(baseCode));
  }

  /**
   * 生成节点ID(2字符)
   */
  private String generateNodeId() {
    try {
      // 1. 尝试获取主机名
      String hostname = java.net.InetAddress.getLocalHost().getHostName();
      // 取主机名后两位并转换为字母数字
      if (hostname.length() >= 2) {
        return convertToValidChars(hostname.substring(hostname.length() - 2));
      }

      // 2. 尝试获取IP地址
      String ip = java.net.InetAddress.getLocalHost().getHostAddress();
      String[] parts = ip.split("\\.");
      if (parts.length >= 2) {
        // 取最后两段IP并转换
        int part1 = Integer.parseInt(parts[parts.length - 2]);
        int part2 = Integer.parseInt(parts[parts.length - 1]);
        return String.format("%c%c", RANDOM_CHARS.charAt(part1 % 36), RANDOM_CHARS.charAt(part2 % 36));
      }

      // 3. 使用随机ID
      return generateRandomPart();
    } catch (Exception e) {
      // 出错时使用随机ID
      return generateRandomPart();
    }
  }

  /**
   * 转换为有效字符
   */
  private String convertToValidChars(String str) {
    StringBuilder sb = new StringBuilder();
    for (char c : str.toCharArray()) {
      if (Character.isLetterOrDigit(c)) {
        sb.append(c);
      } else {
        sb.append(RANDOM_CHARS.charAt(Math.abs(c) % 36));
      }
    }
    // 不足两位时补随机字符
    while (sb.length() < 2) {
      sb.append(RANDOM_CHARS.charAt((int) (Math.random() * 36)));
    }
    return sb.toString().toUpperCase();
  }

  /**
   * 生成随机数部分(2字符)
   */
  private String generateRandomPart() {
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < 2; i++) {
      int index = (int) (Math.random() * RANDOM_CHARS.length());
      sb.append(RANDOM_CHARS.charAt(index));
    }
    return sb.toString();
  }

  /**
   * 计算CRC校验部分(2字符)
   */
  private String calculateCrc(String baseCode) {
    CRC32 crc32 = new CRC32();
    crc32.update(baseCode.getBytes());
    long crcValue = crc32.getValue();

    // 转换为两位16进制字符串
    return String.format("%02X", (int) (crcValue % 256));
  }

  /**
   * 实际生成逻辑
   * 同一分钟内最多生成100个不同的Code
   */
  private String generate(String prefix) {
    lock.lock();
    try {
      // 获取当前时间部分
      String currentTimePart = getCurrentTimePart();

      // 如果时间部分与上次相同，计数器递增
      if (currentTimePart.equals(lastTimePart)) {
        int count = counter.incrementAndGet();
        // 同一分钟内最多生成99个不同的Code
        if (count > 99) {
          // 等待下一毫秒
          Thread.sleep(10);
          currentTimePart = getCurrentTimePart();
          counter.set(0);
        }
      } else {
        // 时间部分变化，重置计数器
        lastTimePart = currentTimePart;
        counter.set(0);
      }

      // 生成计数器部分（2位，不足补零）
      String counterPart = String.format("%02d", counter.get());

      // 组装前17位
      String baseCode = currentTimePart + nodeId + counterPart;

      // 计算CRC校验部分
      String crcPart = calculateCrc(baseCode);

      // 返回完整的20位用户Code
      return prefix + baseCode + crcPart;
    } catch (Exception e) {
      throw new RuntimeException("生成用户Code失败", e);
    } finally {
      lock.unlock();
    }
  }

  /**
   * 获取当前时间部分(yyMMddHHmm)
   */
  private String getCurrentTimePart() {
    return LocalDateTime.now().format(TIME_FORMATTER);
  }
}
