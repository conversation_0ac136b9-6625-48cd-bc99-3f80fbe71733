package com.bestpay.bigdata.bi.common.util;

import com.bestpay.bigdata.bi.common.exception.BusinessException;

/**
 * @author: lcy
 * @date: 2022/1/6
 **/
public class ExceptionUtil {

    public static void throwBusiness(String msg){
        throw new BusinessException(msg);
    }

    public static void throwBusinessIncludeExceptionInfo(String title,Exception e){
        throw new BusinessException(getExceptionInfo(title,e));
    }

    public static String getExceptionInfo(String title, Exception e) {
        return cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e);
    }
}
