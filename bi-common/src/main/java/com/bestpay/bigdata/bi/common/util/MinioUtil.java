package com.bestpay.bigdata.bi.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.drip.oss.proxy.client.HttpSender;
import com.bestpay.drip.oss.proxy.common.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class MinioUtil {
    @Autowired
    private OssProxyTemplateImpl ossProxyTemplate;
    @Autowired
    private ApolloRefreshConfig apolloRefreshConfig;

    private static final String BI_LOGIC_NAME = "bigdata-bi-oss";
    private static final String BI_TABLE_NAME = "bigdata-bi";
    private static final String BI_APP_CODE = "8J52";
    private static final String SUCCESS_CODE = "200";
    private static final int DEFAULT_TTL_DAYS = 7;

    @Autowired
    private HttpSender sender;

    /**
     * 上传至公共桶（公网可访问，有cdn加速）
     * @param inputStream 文件流
     * @param ttlDays 留存时间（天）
     * @param fileExtName 文件后缀
     * <AUTHOR>
     * @date 2023/7/3 11:14
     * @return com.bestpay.drip.oss.proxy.common.UploadResult
     */
    public String uploadPublicMinio(InputStream inputStream, int ttlDays, String fileExtName){
        return uploadMinio(inputStream, ttlDays, fileExtName, Constants.ACCESSED_BY_UNSIGNED_URL);
    }

    public String uploadPublicMinio(InputStream inputStream, String fileExtName){
        return uploadPublicMinio(inputStream, DEFAULT_TTL_DAYS, fileExtName);
    }

    /**
     * 上传至内部桶（只能内网应用使用，无法公网访问）
     * @param inputStream 文件流
     * @param ttlDays 留存时间（天）
     * @param fileExtName 文件后缀
     * <AUTHOR>
     * @date 2023/7/3 11:14
     * @return com.bestpay.drip.oss.proxy.common.UploadResult
     */
    public String uploadPrivateMinio(InputStream inputStream, int ttlDays, String fileExtName){
        return uploadMinio(inputStream, ttlDays, fileExtName, Constants.NEVER_ACCESSED_BY_URL);
    }

    public String uploadPrivateMinio(InputStream inputStream, String fileExtName){
        return uploadPrivateMinio(inputStream, DEFAULT_TTL_DAYS, fileExtName);
    }

    // 上传
    private String uploadMinio(InputStream inputStream, int ttlDays, String fileExtName, int webURLMode){
        if (inputStream == null){
            throw new BusinessException("uploadMinio, inputStream is null");
        }

        UploadRequest request = new UploadRequest();
        request.setStream(inputStream);
        // 当前应用自己的appcode，不清楚的可以咨询维护方——业务架构组，新老世界应用都有。
        request.setAppCode(BI_APP_CODE);
        // minio逻辑表名,业务方可以根据业务类型自定义，主要是用于区分不同业务的文件
        request.setTableName(BI_TABLE_NAME);
        // 分片位，通过这个值的hash值与100取模来选择物理库，对应drip-oss注解中的@OSS.IpRIdPath，start为0，end为值的长度。（强烈建议随机uuid）
        request.setShardingValue(UUID.randomUUID().toString());
        // 逻辑库名，应用根据drip-oss规范在nacos上添加的逻辑库，一般格式为 应用名-oss
        request.setLogicName(BI_LOGIC_NAME);
        // 公共桶 ACCESSED_BY_UNSIGNED_URL
        // 公共桶特性：可以被公网直接访问，无需访问条件，且有cdn加速的
        // 私有桶(要安全审批，擅自使用研发自行负责) ACCESSED_BY_SIGNED_URL
        // 私有桶特性：生成限时失效连接，可以被公网访问
        // 内部桶 NEVER_ACCESSED_BY_URL
        // 内部桶特性：只能内网应用使用，无法公网访问
        request.setWebURLMode(webURLMode);
        // 文件过期失效时间,值为-1表示永不过期,单位为天。失效不代表删除。
        request.setTtlDays(ttlDays);
        // 文件上传超时时间(非必须，默认为1000)。大文件可以酌情设置。
        request.setTimeoutMills(apolloRefreshConfig.getMinioUploadTimeoutMills());
        // 文件扩展名
        request.setFileExtName(Optional.ofNullable(fileExtName).orElse(""));

        if (StringUtil.isNotEmpty(fileExtName) && fileExtName.equals("jpg")) {
            request.setWebContentType("image/jpeg");
        } else if (StringUtil.isNotEmpty(fileExtName) && fileExtName.equalsIgnoreCase("zip")) {
            request.setWebContentType("application/zip");
        } else if (StringUtil.isNotEmpty(fileExtName) && fileExtName.equalsIgnoreCase("xlsx")) {
            request.setWebContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        } else if (StringUtil.isNotEmpty(fileExtName) && fileExtName.equalsIgnoreCase("csv")) {
            request.setWebContentType("text/csv");
        }

        // 上传文件
        UploadResult uploadResult = ossProxyTemplate.toOss(request);

        log.info("uploadMinioResult: "+uploadResult.toString());
        return uploadResultToPath(uploadResult);
    }

    /**
     * 下载
     * @param logicName
     * @param bucket
     * @param objectName
     * <AUTHOR>
     * @date 2023/7/3 11:15
     * @return java.io.InputStream
     */
    public byte[] downloadMinio(String logicName, String bucket, String objectName) {
        if (logicName==null || bucket==null || objectName==null){
            throw new BusinessException("downloadMinio, logicName==null || bucket==null || objectName==null");
        }

        // 构建请求参数
        DownloadRequest request = new DownloadRequest(logicName,bucket,objectName,apolloRefreshConfig.getMinioDownloadTimeoutMills());
        // 获取下载结果
        BaseResult<InputStream> streamBaseResult = ossProxyTemplate.streamFromOss(request);
        log.info("downloadMinioResult logicName:{}, bucket:{}, objectName:{}, Detail:{}",logicName, bucket, objectName, streamBaseResult.getDetail());
        byte[] fileBytes = new byte[0];
        try {
            fileBytes = inputStreamToByteArray(streamBaseResult.getData());
        } catch (IOException e) {
            log.error("downloadMinio inputStreamToByteArray error",e);
            throw new BusinessException("downloadMinio inputStreamToByteArray error");
        }

        return fileBytes;
    }

    // download by mini_io path
    // path example: miniio://logicName=xxx,bucket=xxx,objectName=xxx
    public byte[] downloadMinio(String miniIoPath) {
        Pattern pattern = Pattern.compile("^miniio://logicName=(?<logicName>[^,]+),bucket=(?<bucket>[^,]+),objectName=(?<objectName>.+)$");
        Matcher matcher = pattern.matcher(miniIoPath);

        if (!matcher.matches()) {
            throw new BusinessException("Invalid miniio url format: " + miniIoPath);
        }

        return downloadMinio(matcher.group("logicName"), matcher.group("bucket"), matcher.group("objectName"));
    }

    public static void main(String[] args)
    {
        MinioUtil minioUtil = new MinioUtil();
        minioUtil.parsePath("miniio://logicName=123,bucket=456,objectName=dgdfv");

    }

    public Map<String, String> parsePath(String miniIoPath) {
        Pattern pattern = Pattern.compile("^miniio://logicName=(?<logicName>[^,]+),bucket=(?<bucket>[^,]+),objectName=(?<objectName>.+)$");
        Matcher matcher = pattern.matcher(miniIoPath);

        if (!matcher.matches()) {
            throw new BusinessException("Invalid miniio url format: " + miniIoPath);
        }

        Map<String, String> map = new HashMap<>();
        map.put("logicName", matcher.group("logicName"));
        map.put("bucket", matcher.group("bucket"));
        map.put("objectName", matcher.group("objectName"));

        return map;
    }


    public String generatePreSignUrl(String miniIoPath) {
        Map<String, String> map = parsePath(miniIoPath);

        BaseResult<String> baseResult = ossProxyTemplate.preSign(map.get("logicName"), map.get("bucket"), map.get("objectName"), -1);
        return baseResult.getData();
    }


    public BaseResult<Boolean> existsMinio(String miniIoPath) {
        Map<String, String> map = parsePath(miniIoPath);

        Map<String, String> params = new HashMap();
        params.put("logicName", map.get("logicName"));
        params.put("bucket", map.get("bucket"));
        params.put("objectName", map.get("objectName"));
        params.put("appName", "bigdata-bi");
        BaseResult<Boolean> result = null;

        try {
            String res = this.sender.sendPost("/existsMinio", params, 0);
            result = (BaseResult) JSON.parseObject(res, new TypeReference<BaseResult<Boolean>>() {
            }, new Feature[0]);
            return result;
        } catch (IOException var7) {
            log.error("判断minio上是否存在文件失败", var7);
            return BaseResult.netError();
        }
    }


    public void delete(String path) {
        Pattern pattern = Pattern.compile("^miniio://logicName=(?<logicName>[^,]+),bucket=(?<bucket>[^,]+),objectName=(?<objectName>.+)$");
        Matcher matcher = pattern.matcher(path);

        if (!matcher.matches()) {
            throw new BusinessException("Invalid miniio url format: " + path);
        }

        deleteFileByMinio(matcher.group("logicName"), matcher.group("bucket"), matcher.group("objectName"));
    }

    // 删除
    public boolean deleteFileByMinio(String logicName, String bucket, String objectName) {
        if (logicName==null || bucket==null || objectName==null){
            throw new BusinessException("deleteFileByMinio, logicName==null || bucket==null || objectName==null");
        }

        BaseResult result = ossProxyTemplate.deleteOss(logicName, bucket, objectName);
        return SUCCESS_CODE.equals(result.getCode());
    }

    // 获取bi的logicname
    public static String getBiLogicName(){
        return BI_LOGIC_NAME;
    }

    private byte[] inputStreamToByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        byte[] buffer = new byte[1024];
        int bytesRead;

        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }

        return byteArrayOutputStream.toByteArray();
    }

    private String uploadResultToPath(UploadResult uploadResult){
        String bucket = uploadResult.getBucket();
        String objectName = uploadResult.getObjectName();
        if (bucket == null || objectName == null){
            throw new BusinessException("upload miniio false, bucket="+bucket+",objectName="+objectName);
        }
        return String.format("miniio://logicName=%s,bucket=%s,objectName=%s", BI_LOGIC_NAME, bucket, objectName);
    }
}