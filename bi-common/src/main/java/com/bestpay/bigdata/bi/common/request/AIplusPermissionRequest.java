package com.bestpay.bigdata.bi.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-03-01-16:20
 */
@Data
@ApiModel(value = "请求智加平台权限类")
public class AIplusPermissionRequest {

    /**
     * 平台类型：BIGDATABI
     */
    @ApiModelProperty(value = "平台类型",required = true)
    private String systype;
}
