package com.bestpay.bigdata.bi.common.dto;

import com.bestpay.bigdata.bi.common.enums.JavaFieldTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
public class TableDataBlock implements Serializable {
    private static final long serialVersionUID = -1;

    private final String databaseName;
    private final String tableName;
    private final List<Column> columns;
    private final List<List<String>> rowsData;

    public TableDataBlock(String databaseName, String tableName, List<Column> columns, List<List<String>> rowsData){
        this.databaseName = databaseName;
        this.tableName = tableName;
        this.columns = columns;
        this.rowsData = rowsData;
    }

    // 增加列至columns末尾（已有行数据的该列会使用默认值）
    public void addColumn(Column column){
        columns.add(column);
    }

    // 调用方自行与列顺序对齐，缺省列数据会使用默认值数据
    public void addRow(List<String> row){
        rowsData.add(row);
    }

    /**
     * 删除列，会连同该列数据一起软删除，后续该列数据无法获取到。
     * 该删除方法不会破坏列顺序，可放心使用。
     * @param columnName
     * <AUTHOR>
     * @date 2023/7/12 20:12
     * @return boolean true：删除成功
     */
    public boolean removeColumn(String columnName){
        boolean res = false;
        for (Column column : columns) {
            if (column.getColumnName().equals(columnName)){
                res = true;
                column.setDestroy(true);
                break;
            }
        }
        return res;
    }

    public List<Column> getActiveColumns() {
        return columns.stream()
                .filter(column -> !column.isDestroy())
                .collect(Collectors.toList());
    }

    public String getTableName() {
        return tableName;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    /**
     * 获取数据块迭代器。
     * 该迭代器可保证每个List<String>内数据顺序和个数，与当前“存活”columns完全一致
     * <AUTHOR>
     * @date 2023/7/12 20:25
     * @return java.util.Iterator<java.util.List<java.lang.String>>
     */
    public Iterator<List<String>> getRowsData(){
        return new Iterator<List<String>>() {
            private int currentIndex = 0;

            @Override
            public boolean hasNext() {
                return currentIndex < rowsData.size();
            }

            @Override
            public List<String> next() {
                List<String> rowData = rowsData.get(currentIndex);
                List<String> newRowData = new ArrayList<>();

                int i = 0;
                for (Column column : columns) {
                    if ( !column.isDestroy()) {
                        if (i >= rowData.size()) {
                            newRowData.add(column.getDefaultValue());
                        } else {
                            newRowData.add(rowData.get(i));
                        }
                    }
                    i++;
                }

                currentIndex++;
                return newRowData;
            }
        };
    }

    @Data
    public static class Column implements Serializable{
        private static final long serialVersionUID = -1;

        private String columnName;
        private JavaFieldTypeEnum columnType;
        private String defaultValue = "";
        private boolean destroy;

        public Column(String columnName, JavaFieldTypeEnum columnType){
            this.columnName = columnName;
            this.columnType = columnType;
        }
    }
}
