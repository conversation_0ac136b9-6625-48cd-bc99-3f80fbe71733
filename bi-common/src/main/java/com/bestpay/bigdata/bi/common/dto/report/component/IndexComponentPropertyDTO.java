package com.bestpay.bigdata.bi.common.dto.report.component;

import cn.hutool.core.util.NumberUtil;
import com.bestpay.bigdata.bi.common.dto.report.AdvancedComputing;
import com.bestpay.bigdata.bi.common.enums.DataTypeEnum;
import com.bestpay.bigdata.bi.common.enums.FormatUnitEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.List;
import java.util.Objects;

import lombok.Data;

/**
 * 字段信息
 * <AUTHOR>
 */
@Data
@ApiModel(description = "指标组件属性")
public class IndexComponentPropertyDTO extends CommonComponentPropertyDTO {

  @ApiModelProperty(value = "字段昵称")
  private String nickName;

  @ApiModelProperty(value = "是否是计算字段")
  private boolean isComputeField;

  /**
   * 字段显示类型名称
   */
  @ApiModelProperty(value = "字段显示类型名称")
  private String showTypeName;

  /**
   * 聚合方式
   */
  @ApiModelProperty(value = "聚合方式")
  private String polymerization;

  /**
   * 高级计算
   */
  @ApiModelProperty(value = "高级计算")
  private AdvancedComputing advancedComputing;

  /**
   * 数据格式中的度量单位据类型
   */
  @ApiModelProperty(value = "数据格式中的度量单位数据类型")
  private String dataType;
  /**
   * 数据格式中的度量单位小数位数
   */
  @ApiModelProperty(value = "数据格式中的度量单位小数位数")
  private Integer decimaCarry;
  /**
   * 数据格式中的度量单位
   */
  @ApiModelProperty(value = "数据格式中的度量单位")
  private Integer unit;

  /**
   * 是否隐藏 数据格式中的度量单位, 默认展示单位
   */
  @ApiModelProperty(value = "是否隐藏 数据格式中的度量单位, 默认展示单位")
  private Boolean isShowUnit = true;

  /**
   * 数据格式中的是否显示千分位
   */
  @ApiModelProperty(value = "数据格式中的是否显示千分位")
  private Boolean showThousandth;

  @ApiModelProperty(value = "数据格式中的值列表")
  private List<String> valueList;

  /**
   * 计算字段解析后的逻辑
   */
  @ApiModelProperty(value = "计算字段解析后的逻辑")
  private String computeFieldLogic;

  /**
   * 是否隐藏
   */
  @ApiModelProperty(value = "是否隐藏")
  private Boolean isHide = false;

  /**
   * 数据格式化，百分比 or 数值
   */
  public String format(String data,Integer fileFormatType ) {

    if (!NumberUtil.isNumber(data)) {
      return data;
    }

    // 数值类型
    if (DataTypeEnum.number.getType().equals(this.getDataType())) {
      return formatByNumber(data, this,fileFormatType);
    }

    // 百分比
    if (DataTypeEnum.percent.getType().equals(this.getDataType())) {
      return formatByPercent(data, this);
    }

    return data;
  }

  /**
   * 数据格式 - 数值类型
   */
  private static String formatByNumber(String data, IndexComponentPropertyDTO index,Integer fileFormatType) {
    // 是否显示千分为
    NumberFormat formatter = NumberFormat.getInstance();
    if (Boolean.TRUE.equals(index.getShowThousandth())) {
      formatter.setGroupingUsed(true);
    }else{
      formatter.setGroupingUsed(false);
    }

    formatter.setMinimumFractionDigits(index.getDecimaCarry()==null ? 2:index.getDecimaCarry());
    formatter.setMaximumFractionDigits(index.getDecimaCarry()==null ? 2:index.getDecimaCarry());
    formatter.setRoundingMode(java.math.RoundingMode.HALF_UP);

    // 保留N位小数，并根据fileFormatType决定是否显示显示单位
    String format = formatter.format(new BigDecimal(data)
        .divide(new BigDecimal(index.getUnit() == null ? 1 : index.getUnit())));
    //当需要展示单位数据并且该字段需要展示单位时，添加单位到数值后
    if (fileFormatType == 1 && index.getIsShowUnit()){
      format = format + FormatUnitEnum.getUnitByNum(index.getUnit());
    }
    return format;
  }

  /**
   * 数据格式 - 百分比
   */
  private static String formatByPercent(String data, IndexComponentPropertyDTO index) {
    // 是否显示千分为
    NumberFormat formatter = NumberFormat.getInstance();
    if (Boolean.TRUE.equals(index.getShowThousandth())) {
      formatter.setGroupingUsed(true);
    }else{
      formatter.setGroupingUsed(false);
    }

    formatter.setMinimumFractionDigits(index.getDecimaCarry());
    formatter.setMaximumFractionDigits(index.getDecimaCarry());
    formatter.setRoundingMode(java.math.RoundingMode.HALF_UP);

    // 保留N位小数，并显示单位
    return formatter.format(new BigDecimal(data)
        .multiply(new BigDecimal("100")))
        + "%";
  }
}
