package com.bestpay.bigdata.bi.common.enums;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 数据安全等级
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/5/23 10:07
 */
public enum SecurityLevelEnum {

  /**
   * L1
   */
  LEVEL_1("L1"),
  /**
   * L2
   */
  LEVEL_2("L2"),
  /**
   * L3
   */
  LEVEL_3("L3"),
  /**
   * L4
   */
  LEVEL_4("L4"),
  /**
   * 无敏感等级
   */
  NO_LEVEL("-1");

  String code;

  SecurityLevelEnum(String code) {
    this.code = code;
  }

  public String getCode() {
    return code;
  }

  public static String getName(String code) {
    SecurityLevelEnum[] chartTypeEnums = SecurityLevelEnum.values();
    for (SecurityLevelEnum chartTypeEnum : chartTypeEnums) {
      if (Objects.equals(chartTypeEnum.getCode(), code)) {
        return chartTypeEnum.name();
      }
    }
    return null;
  }


  /**
   * 所有有效的等级
   */
  public static final List<String> ALL_VALID_LEVEL = Stream.of("1", "2", "3", "4", "-1").collect(Collectors.toList());

  public static final List<String> LOW_LEVEL = Stream.of("1", "2").collect(Collectors.toList());


  /**
   * 不需要走JIRA工单的枚举
   */
  public static final List<String> NOT_NEED_JIRA_PROCESS = Stream.of(SecurityLevelEnum.LEVEL_1.getCode(),SecurityLevelEnum.LEVEL_2.getCode()).collect(Collectors.toList());

}
