package com.bestpay.bigdata.bi.common.response;

/**
 * 接口处理模板
 * <AUTHOR>
 * @date 2021/3/11 15:11
 **/
public abstract class BaseProcessor<T> {

    /**
     * 前置处理
     */
    protected void preHandler(){};

    /**
     * 参数校验
     */
    protected void check(){

    };

    /**
     * 接口业务处理逻辑
     * @return  返回数据
     */
    protected T process(){
        return null;
    };

    /**
     * 后置处理
     */
    protected void postHandler(){};

    /**
     * 处理流
     * @return
     */
    public T execute() {
        try {
            preHandler();
            check();
            return process();
        } finally {
            postHandler();
        }
    }
}
