package com.bestpay.bigdata.bi.common.dto.common;

import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.ColumnHeaderStyle;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.RowHeaderStyle;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.TableDataStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "表格字体样式")
public class TableFontStyleRequest {

    @ApiModelProperty(value = "表格数据样式")
    private TableDataStyle tableDataStyle;

    @ApiModelProperty(value = "行表头样式")
    private RowHeaderStyle rowHeaderStyle;

    @ApiModelProperty(value = "列表头样式")
    private ColumnHeaderStyle columnHeaderStyle;

    @ApiModelProperty(value = "来源类型")
    private String resourceType;

    @ApiModelProperty(value = "组件类型")
    private String componentType;
}
