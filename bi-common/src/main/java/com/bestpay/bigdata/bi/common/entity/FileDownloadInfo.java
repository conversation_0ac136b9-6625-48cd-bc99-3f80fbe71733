package com.bestpay.bigdata.bi.common.entity;

import java.io.Serializable;
import java.util.List;

/**
 * @author: laiyao
 * @date: 2022/05/17
 */
public class FileDownloadInfo implements Serializable{

    private String filePath;

    private List<String> comments;

    private List<String> headerRow;

    private List<List<String>> dataRow;

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public List<String> getComments() {
        return comments;
    }

    public void setComments(List<String> comments) {
        this.comments = comments;
    }

    public List<String> getHeaderRow() {
        return headerRow;
    }

    public void setHeaderRow(List<String> headerRow) {
        this.headerRow = headerRow;
    }

    public List<List<String>> getDataRow() {
        return dataRow;
    }

    public void setDataRow(List<List<String>> dataRow) {
        this.dataRow = dataRow;
    }
}
