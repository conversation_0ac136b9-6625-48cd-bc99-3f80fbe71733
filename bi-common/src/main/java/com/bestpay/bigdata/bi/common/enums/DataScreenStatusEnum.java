package com.bestpay.bigdata.bi.common.enums;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024-08-28
 */
public enum DataScreenStatusEnum {
  PUBLISHED("published", "已发布"),
  SAVED("saved", "已保存"),
  OFFLINE("offline", "待上线"),
  DELETE("delete", "删除");



  public static List<String> OFFLINE_AND_DELETE = Stream.of(OFFLINE.getCode(),DELETE.getCode()).collect(Collectors.toList());

  private final String code;
  private final String message;

  DataScreenStatusEnum(String code, String message) {
    this.code = code;
    this.message = message;
  }

  public String getCode() {
    return code;
  }

  public String getMessage() {
    return message;
  }


  public static DataScreenStatusEnum getByCode(String code) {
    for (DataScreenStatusEnum ds : values()) {
      if (ds.code.equals(code)) {
        return ds;
      }
    }
    return null;
  }
}
