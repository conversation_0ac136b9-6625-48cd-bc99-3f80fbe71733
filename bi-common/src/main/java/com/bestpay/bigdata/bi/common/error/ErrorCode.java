package com.bestpay.bigdata.bi.common.error;

import lombok.Getter;

import static java.util.Objects.requireNonNull;

@Getter
public final class ErrorCode {

    private final String code;
    private final String name;
    private final ErrorType type;

    public ErrorCode(String code, String name, ErrorType type) {
        this.code = requireNonNull(code, "code is null");
        this.name = requireNonNull(name, "name is null");
        this.type = requireNonNull(type, "type is null");
    }
}
