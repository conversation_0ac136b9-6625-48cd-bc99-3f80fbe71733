package com.bestpay.bigdata.bi.common.jmx;

import com.bestpay.bigdata.bi.common.entity.ApiMetrics;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022-04-15-16:13
 */
public class ApiCallStatics implements ApiCallStaticsMBean {

    private AllApiCallStatics allApiCallStatics;
    private String apiName;

    public ApiCallStatics(AllApiCallStatics allApiCallStatics, String apiName) {
        this.allApiCallStatics = allApiCallStatics;
        this.apiName = apiName;
    }

    @Override
    public long getNormalCount() {
        ApiMetrics apiMetrics = allApiCallStatics.getTotalMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getCount():0;
    }

    @Override
    public long getNormalTotalConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getTotalMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getTotalConsumeTime():0;
    }

    @Override
    public long getNormalAvgConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getTotalMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getAvgConsumeTime():0;
    }

    @Override
    public long getNormalLastConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getTotalMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getLastConsumeTime():0;
    }

    @Override
    public long getSuccessCount() {
        ApiMetrics apiMetrics = allApiCallStatics.getSuccessMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getCount():0;
    }

    @Override
    public long getSuccessTotalConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getSuccessMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getTotalConsumeTime():0;
    }

    @Override
    public long getSuccessAvgConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getSuccessMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getAvgConsumeTime():0;
    }

    @Override
    public long getSuccessLastConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getSuccessMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getLastConsumeTime():0;
    }

    @Override
    public long getFailureCount() {
        ApiMetrics apiMetrics = allApiCallStatics.getFailureMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getCount():0;
    }

    @Override
    public long getFailureTotalConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getFailureMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getTotalConsumeTime():0;
    }

    @Override
    public long getFailureAvgConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getFailureMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getAvgConsumeTime():0;
    }

    @Override
    public long getFailureLastConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getFailureMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getLastConsumeTime():0;
    }


    @Override
    public long getIntervalNormalCount() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalTotalMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getCount():0;
    }

    @Override
    public long getIntervalNormalTotalConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalTotalMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getTotalConsumeTime():0;
    }

    @Override
    public long getIntervalNormalAvgConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalTotalMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getAvgConsumeTime():0;
    }

    @Override
    public long getIntervalNormalLastConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalTotalMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getLastConsumeTime():0;
    }

    @Override
    public long getIntervalSuccessCount() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalSuccessMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getCount():0;
    }

    @Override
    public long getIntervalSuccessTotalConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalSuccessMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getTotalConsumeTime():0;
    }

    @Override
    public long getIntervalSuccessAvgConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalSuccessMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getAvgConsumeTime():0;
    }

    @Override
    public long getIntervalSuccessLastConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalSuccessMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getLastConsumeTime():0;
    }

    @Override
    public long getIntervalFailureCount() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalFailureMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getCount():0;
    }

    @Override
    public long getIntervalFailureTotalConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalFailureMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getTotalConsumeTime():0;
    }

    @Override
    public long getIntervalFailureAvgConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalFailureMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getAvgConsumeTime():0;
    }

    @Override
    public long getIntervalFailureLastConsumeTime() {
        ApiMetrics apiMetrics = allApiCallStatics.getIntervalFailureMetricsMap().get(apiName);
        return Objects.nonNull(apiMetrics)?apiMetrics.getLastConsumeTime():0;
    }
}
