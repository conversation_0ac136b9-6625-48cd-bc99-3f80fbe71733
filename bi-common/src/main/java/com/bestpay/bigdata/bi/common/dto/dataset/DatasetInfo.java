package com.bestpay.bigdata.bi.common.dto.dataset;

import com.bestpay.bigdata.bi.common.enums.SQLEngine;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 数据集实体类
 *
 * <AUTHOR>
 * @since 2021-12-09 10:27:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("数据集实体类")
public class DatasetInfo {


  @ApiModelProperty(value = "数据集Id")
  private Long datasetId;

  @ApiModelProperty(value = "数据集类型")
  private Integer dataSourceTypeCode;

  @ApiModelProperty(value = "数据集名称")
  private String datasetName;

  @ApiModelProperty(value = "数据源名称")
  private String dataSourceName;

  @ApiModelProperty(value = "数据库名称")
  private String dataBaseName;

  @ApiModelProperty(value = "表名称")
  private String tableName;

  @ApiModelProperty(value = "分区名称")
  private String partitionName;

  @ApiModelProperty(value = "更新方式")
  private Integer updateMethod;

  @ApiModelProperty(value = "数据源类型:hive;clickhouse;")
  private String dataSourceType;

  @ApiModelProperty(value = "数据日期")
  private String dataDate;

  @ApiModelProperty(value = "数据集名称")
  private String name;

/*  *//**
   * for backward compatible dataset
   *
   * @return sqlEngine
   */
  public SQLEngine getSqlEngine() {
    if (StringUtils.isBlank(dataSourceType)) {
      return SQLEngine.CLICKHOUSE;
    }

    try {
      return SQLEngine.valueOf(dataSourceType.toUpperCase());
    }catch (Exception e) {
      throw new BusinessException("dataSourceType: "+dataSourceType+" is illegal");
    }
  }
}
