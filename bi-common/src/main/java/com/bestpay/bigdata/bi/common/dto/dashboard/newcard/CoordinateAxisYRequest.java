package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 坐标轴Y轴
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CoordinateAxisYRequest extends BaseFontRequest {

    /**
     * 标签颜色
     */
    private String labelColor;

    @ApiModelProperty(value = "轴范围开始")
    private Integer axisRangeStart;

    @ApiModelProperty(value = "轴范围结束")
    private Integer axisRangeEnd;

    //  范围 0-10
    @ApiModelProperty(value = "数据步长")
    private Integer dataStepSize;

    @ApiModelProperty(value = "是否展示网格线")
    private Boolean showGridlines;

    /**
     * 是否展示Y轴
     */
    private Boolean showYAxis;

    public Boolean getShowYAxis() {
        if(this.showYAxis==null){
            return true;
        }

        return showYAxis;
    }

    public void setShowYAxis(Boolean showYAxis) {
        if(showYAxis==null){
            this.showYAxis = true;
            return;
        }

        this.showYAxis = showYAxis;
    }
}
