package com.bestpay.bigdata.bi.common.dto.datascreen.component;

import com.bestpay.bigdata.bi.common.dto.common.TableFontStyleRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.CommonCoordinateAxisConfigRequest;
import com.bestpay.bigdata.bi.common.dto.datascreen.LayerEventDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.report.ReportSimpleColumn;
import com.bestpay.bigdata.bi.common.dto.report.TableConfiguration;
import com.bestpay.bigdata.bi.common.dto.report.TotalDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ConditionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ContrastComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.DimensionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.FilterComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.KeywordComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.OrderComponentDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ReportComponentInfoDTO extends BaseComponentInfo {

  private Long reportId; // 报表id

  @ApiModelProperty(value = "图表属性",required = true)
  private Object chartFieldObj;

  @ApiModelProperty(value = "图表类型；0-列表；1-柱形图；2-折线图；3-环形图；",required = true)
  private Integer chartType;

  @ApiModelProperty(value = "计算字段")
  private List<ComputeComponentPropertyDTO> computeColumnList;

  @ApiModelProperty(value = "查询控件",required = true)
  @NotNull(message = "查询控件不能为空！")
  private List<ConditionComponentPropertyDTO> conditionList;

  @ApiModelProperty(value = "对比字段")
  private List<ContrastComponentPropertyDTO> contrastColumnList;

  /**
   * 创建人组织机构代码
   */
  @ApiModelProperty(value = "创建人组织机构代码",required = true)
  private String createdByOrg;

  @ApiModelProperty(value = "数据权限",required = true)
  private String dataAuth;

  @ApiModelProperty(value = "数据集信息",required = true)
  @NotNull(message = "数据集信息不能为空！")
  private List<DatasetInfo> datasetInfoList;

  @ApiModelProperty(value = "数据是否包含敏感信息: 0: false 1:true",required = true)
  private Integer fileContainSensitiveInfo;

  @ApiModelProperty(value = "过滤条件")
  private List<FilterComponentPropertyDTO> filterColumnList;

  @ApiModelProperty(value = "指标字段",required = true)
  private List<IndexComponentPropertyDTO> indexColumnList;

  @ApiModelProperty(value = "关键字",required = true)
  @NotNull(message = "关键字不能为空！")
  private List<KeywordComponentPropertyDTO> keywordList;

  @ApiModelProperty(value = "查询总行数")
  private String maxRows;

  @ApiModelProperty(value = "排序控件")
  private List<OrderComponentDTO> orderColumnList;

  @ApiModelProperty(value = "组织权限",required = true)
  private String orgAuth;

/*  @ApiModelProperty(value = "归属组织编码",required = true)
  @NotNull(message = "归属组织编码不能为空！")
  private String orgCode;*/

  @ApiModelProperty(value = "选择组织编码",required = true)
  @NotNull(message = "选择组织编码不能为空！")
  private String orgSelected;

  @ApiModelProperty(value = "责任人（中文名称）",required = true)
  private String ownerNameCh;

  @ApiModelProperty(value = "报表说明",required = true)
  //@NotNull(message = "报表说明不能为空！")
  private String reportDesc;

  /*@ApiModelProperty(value = "报表名称",required = true)
  @NotNull(message = "报表名称不能为空！")
  private String reportName;*/

  @ApiModelProperty(value = "报表结构, 描述顺序信息 JSON List ReportSimpleColumn")
  private List<ReportSimpleColumn> reportStructureList;

  @ApiModelProperty(value = "报表样式配置树形 json")
  private TableConfiguration tableConfigurationObj;


  /**
   * 此字段前端不传递给后端，后续不在使用，当前设默认值为1
   */
  @ApiModelProperty(value = "报表类型（0：明细表  1：聚合表 ）",required = true)
  @NotNull(message = "报表类型不能为空！")
  private Integer reportType = 1;

  @ApiModelProperty(value = "展示字段/维度",required = true)
  @NotNull(message = "展示字段不能为空！")
  private List<DimensionComponentPropertyDTO> showColumnList;

  @ApiModelProperty(value = "是否显示指标总计")
  private TotalDTO showIndexTotalObj;

  @ApiModelProperty(value = "状态（0：上线  1：待发布  9：删除）",required = true)
//  @NotNull(message = "状态不能为空！")
  private Integer statusCode;

  @ApiModelProperty(value = "是否开启上卷下钻 0关闭 1开启, 默认值为0")
  private Integer rollupDown;

  @ApiModelProperty(value = "敏感字段")
  private String sensitiveFields;

  private TableFontStyleRequest tableFontStyle;

  // 仪表板报表坐标轴配置
  private CommonCoordinateAxisConfigRequest coordinateAxisConfigRequest;

  //特殊排序 0.常规 1.堆积总和asc 2.堆积总和desc
  private Integer orderType;

  @ApiModelProperty(value = "图层事件配置")
  private LayerEventDTO componentLayerEvent;
}
