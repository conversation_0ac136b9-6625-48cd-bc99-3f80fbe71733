package com.bestpay.bigdata.bi.common.request;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("卡片详情请求参数")
public class TableCardInfoRequest {
    @ApiModelProperty(value = "仪表板ID")
    private Long dashboardId;
    @ApiModelProperty(value = "卡片编码列表")
    private List<CardRequest> cardCodeList;
    @ApiModelProperty(value = "用户来源")
    private String userSource;

    @Data
    @ApiModel(description = "卡片请求参数")
    public static class CardRequest{
        @ApiModelProperty(value = "卡片code")
        private String cardCode;
        @ApiModelProperty(value = "卡片类型")
        private String cardType;
    }
}
