package com.bestpay.bigdata.bi.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

public enum DashboardTypeEnum {
    /**
     * 桌面端
     */
    PC(0,"桌面端"),
    /**
     * 移动端
     */
    MOBILE(1,"移动端"),;

    @Getter
    private final Integer code;
    @Getter
    private final String msg;

    DashboardTypeEnum(int code, String msg) {
        this.code = code;
        this.msg=msg;
    }

    public static DashboardTypeEnum getByCode(Integer code){
        if(code==null){
            return null;
        }

        for (DashboardTypeEnum value : DashboardTypeEnum.values()) {
            if(value.getCode().equals(code)){
                return value;
            }
        }

        return null;
    }
}
