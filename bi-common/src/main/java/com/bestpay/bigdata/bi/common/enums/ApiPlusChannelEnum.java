package com.bestpay.bigdata.bi.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName ApiPlusChannelEnum
 * @description 智加渠道类型枚举
 * @date 2025/7/29
 */
@Getter
public enum ApiPlusChannelEnum {

  /**
   * 钉钉
   */
  DINGTALK("dingtalk","钉钉"),

  /**
   * 4A
   */
  IAM("4A","4A"),

  /**
   * 省集约
   */
  PROVINCE("province","省集约"),

  /**
   * 翼支付
   */
  BESTPAY("bestpay","翼支付"),


  ;

  private String channel;

  private String channelName;

  ApiPlusChannelEnum(String channel, String channelName) {
    this.channel = channel;
    this.channelName = channelName;
  }


}
