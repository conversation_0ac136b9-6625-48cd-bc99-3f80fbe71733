package com.bestpay.bigdata.bi.common.response;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName: OpenapiQueryResult
 * Package: com.bestpay.bigdata.bi.common.response
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/7/31 15:43
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class OpenapiQueryResult implements Serializable {
    private List<String> columnNames;

    private List<List<String>> data;

    private Double queryTimeInSecond;

    private Integer total;
}
