package com.bestpay.bigdata.bi.common.bean;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.api.CachedResult;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.entity.ResultBlockMetadata;
import com.bestpay.bigdata.bi.common.enums.QueryType;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@Slf4j
public class CachedQueryResult implements CachedResult, Serializable {

    private String[] columnNames;

    private String[] columnTypes;

    private List<List<String>> results;

    private long resultRowsNum;

    private long resultLength;

    private boolean isEmpty;

    private String cacheKey;

    private String sizeOverflowColumn;

    private List<ResultBlockMetadata> blockMetadataList;

    private String resultType;

    /** miniIO result save path */
    private String resultPath;

    @Override
    public CachedQueryResult dataToBeCached() {
        return this;
    }

    public CachedQueryResult(QueryContext queryContext) {
        this.columnNames=queryContext.getColumnNames();
        this.columnTypes=queryContext.getColumnTypes();
        this.results=queryContext.getResults();
        this.resultRowsNum=queryContext.getResultRowsNum();
        this.resultLength=queryContext.getResultLength();
        this.isEmpty=queryContext.isEmpty();
        this.sizeOverflowColumn=queryContext.getSizeOverflowColumn();
        this.resultType = queryContext.getResultType();
        ArrayList<Object> cacheKeyArgs = Lists.newArrayList(
                queryContext.getUserOrg(),
                //queryContext.getUsername(),
                queryContext.getDataSource(),
                queryContext.getEngineName(),
                queryContext.getSql(),
            queryContext.getBatchSql());
        if(QueryType.QUERY_REPORT.getCode()==queryContext.getTypeCode()){
            cacheKeyArgs.add(queryContext.getOperateTypeCode());
        }
        log.debug("cacheKeyArgs : {}", JSONUtil.toJsonStr(cacheKeyArgs));
        this.cacheKey=generateCacheKey(cacheKeyArgs);
        log.debug("cacheKey : {}", cacheKey);

        if (queryContext.getBlockMetadataList() != null && queryContext.getBlockMetadataList().size() > 0) {
            log.info("ready to cache QueryContext, BlockMetadataList size:{}", queryContext.getBlockMetadataList().size());
            this.blockMetadataList = new ArrayList<>(queryContext.getBlockMetadataList());
        }
    }
}
