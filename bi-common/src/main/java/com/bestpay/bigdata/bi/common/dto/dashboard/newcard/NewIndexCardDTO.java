package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.FilterComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class NewIndexCardDTO extends NewCardDTO {
    private IndexInfo indexInfo;                     // 指标文本信息，名称，组织信息等
    private Object datasetInfo;                      // 数据集信息
    private List<IndexComponentPropertyDTO> dragResult;         // 指标列
    private List<FilterComponentPropertyDTO> filterdragResult;   // 过滤列
    private List<ComputeComponentPropertyDTO> countFiledList;     // 计算字段
    private CardStyleConfig cardStyleConfig; // 卡片设置

    @Data
    public static class IndexInfo{
        private Boolean showFiled;
        private Integer styleGroup;
        private Integer type;
        private Integer reportType;
        private String name;
        private String orgSelected;
        private Long dataSet;
        private String desc;
    }
}
