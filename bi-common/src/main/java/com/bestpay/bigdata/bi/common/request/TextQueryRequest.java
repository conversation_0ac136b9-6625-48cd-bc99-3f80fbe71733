package com.bestpay.bigdata.bi.common.request;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/9/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextQueryRequest implements Serializable {

  private String sql;
  private String username;
  @ApiModelProperty(value = "查询引擎",required = true,allowableValues ="presto,hive,kylin,spark_sql,clickhouse")
  private String sqlEngine;
  @ApiModelProperty(value = "数据源",required = true,allowableValues ="hive_11,hive_36,ck_1,ck_2,ck_3,ck_4,ck_5")
  private String databaseSource;

  @ApiModelProperty(value = "数据源类型",required = false)
  private String datasourceType;

  @ApiModelProperty(value = "访问系统",required = true,allowableValues ="bi,fengkong,DECISION,LABEL")
  private String requestSystem;

  private Integer skip = 0;
  private Integer limit = 20;

  @ApiModelProperty(value = "请求来源",required = false)
  private String source;

  private Map<String, String> properties;
}
