package com.bestpay.bigdata.bi.common.controller;

import com.bestpay.bigdata.product.profile.ProductProfileClient;
import com.bestpay.bigdata.product.profile.bean.Condition;
import com.bestpay.bigdata.product.profile.reponse.ProductFeatureVO;
import com.bestpay.bigdata.product.profile.reponse.Response;
import com.bestpay.bigdata.product.profile.request.ProductProfileRequest;
import com.bestpay.bigdata.product.profile.service.ProductProfileService;
import java.util.List;
import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping({"/biReport/product/profile"})
@Api(value = "profile控制", tags = "profile控制")
public class BiProductProfileController {

  @Resource
  private ProductProfileClient productProfileClient;
  @Resource
  private ProductProfileService profileService;

  public BiProductProfileController() {
  }

  @PostMapping({"/boolValue"})
  @ApiOperation(httpMethod = "POST", value = "是否有权限", notes = "是否有权限")
  public Response<Boolean> completeAppEmbed(@RequestBody ProductProfileRequest ppRequest) {
    Condition condition = Condition.builder().build();
    BeanUtils.copyProperties(ppRequest, condition);
    return Response.ok(this.productProfileClient.boolValue(condition));
  }

  @PostMapping({"/queryAll"})
  @ApiOperation(httpMethod = "POST", value = "profile查询全部", notes = "profile查询全部")
  public Response<List<ProductFeatureVO>> queryAll() {
    return Response.ok(this.profileService.queryAll());
  }
}
