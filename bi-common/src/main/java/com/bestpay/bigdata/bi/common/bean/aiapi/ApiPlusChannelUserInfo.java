package com.bestpay.bigdata.bi.common.bean.aiapi;

import com.bestpay.bigdata.bi.common.entity.PermissionInfo;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName ApiPlusUserInfo
 * @description 新用户信息
 * @date 2025/7/4
 */
@Data
@Accessors(chain = true)
public class ApiPlusChannelUserInfo implements Serializable {

  private static final Long serialVersionUID = 2801228316243892607L;

  /**
   * 渠道账户名
   */
  private String account;

  /**
   * 状态正常-1-冻结-2-注销-3
   */
  private Integer status;

  /**
   * 账户信息列表
   */
  private List<AccountInfo> accountInfoList;

}
