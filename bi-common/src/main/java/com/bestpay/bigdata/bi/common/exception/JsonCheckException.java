package com.bestpay.bigdata.bi.common.exception;

import com.bestpay.bigdata.bi.common.enums.CodeEnum;

/**
 * <AUTHOR>
 * @date: 2021/12/15
 */
public class JsonCheckException extends BaseBiException {


    public JsonCheckException(String code, String message) {
        super(code,message);
    }

    public JsonCheckException(String message) {
        super(message);
    }

    public JsonCheckException(CodeEnum codeEnum) {
        super(codeEnum.message());
    }

}
