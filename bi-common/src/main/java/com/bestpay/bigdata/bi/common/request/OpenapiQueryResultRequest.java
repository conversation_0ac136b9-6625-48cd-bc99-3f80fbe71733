package com.bestpay.bigdata.bi.common.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * ClassName: OpenapiQueryResultRequest
 * Package: com.bestpay.bigdata.bi.common.request
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/7/31 15:45
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class OpenapiQueryResultRequest implements Serializable {

    private String queryId;

    private String username;

    private String requestSystem;

    private Integer skip = 0;

    private Integer limit = 20;
}
