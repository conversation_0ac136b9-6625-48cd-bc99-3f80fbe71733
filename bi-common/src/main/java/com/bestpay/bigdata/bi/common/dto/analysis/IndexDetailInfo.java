package com.bestpay.bigdata.bi.common.dto.analysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2021-04-07 15:33:04
 */
@Data
@ApiModel("指标详情结果集")
public class IndexDetailInfo implements Serializable {

    @ApiModelProperty(value = "指标Id")
    private Long id;
    /**
     * 指标编码
     */
    @ApiModelProperty(value = "指标编码")
    private String indexCode;
    /**
    * 指标分类
    */
    @ApiModelProperty(value = "指标分类Id")
    private Long businessDomainId;
    @ApiModelProperty(value = "指标分类名称")
    private String businessDomainName;
    /**
    * 数据域
    */
    @ApiModelProperty(value = "数据域Id")
    private Long dataDomainId;
    @ApiModelProperty(value = "数据域名称")
    private String dataDomainName;
    /**
    * 中文名称
    */
    @ApiModelProperty(value = "指标中文名称")
    private String name;
    /**
    * 英文名称
    */
    @ApiModelProperty(value = "指标英文名称")
    private String enName;
    /**
    * 数据集
    */
    @ApiModelProperty(value = "数据集Id")
    private Long datasetId;
    @ApiModelProperty(value = "数据集名称")
    private String datasetName;
    @ApiModelProperty(value = "数据集英文名称")
    private String datasetEnName;
    /**
    * 源字段
    */
    @ApiModelProperty(value = "源字段Id")
    private Long datasetColumnId;
    @ApiModelProperty(value = "源字段名称")
    private String  datasetColumnName;

    /**
     * 数据源
     */
    @ApiModelProperty(value = "数据源")
    private String dataSourceName;
    /**
     * 数据库
     */
    @ApiModelProperty(value = "数据库")
    private String dataBaseName;
    /**
     * 源表名称
     */
    @ApiModelProperty(value = "来源表名称")
    private String sourceTableName;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    /**
    * 描述
    */
    @ApiModelProperty(value = "务业口径")
    private String indexDesc;
    /**
    * 数据类型
    */
    @ApiModelProperty(value = "衍生原子指标数据类型编码")
    private Integer dataTypeCode;
    @ApiModelProperty(value = "衍生原子指标数据类型名称")
    private String dataTypeName;
    /**
    * 指标类型
    */
    @ApiModelProperty(value = "指标类型编码")
    private Integer indexTypeCode;
    @ApiModelProperty(value = "指标类型名称")
    private String indexTypeName;
    /**
    * 是否累加
    */
    @ApiModelProperty(value = "是否累加",required = false)
    private Integer isSum;
    /**
    * 逻辑
    */
    @ApiModelProperty(value = "计算逻辑")
    private String calculateLogic;
    /**
    * 状态
    */
    @ApiModelProperty(value = "状态编码")
    private Integer statusCode;
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    /**
     * 度量集合
     */
    @ApiModelProperty(value = "度量集合")
    private List<IndexMeasureInfo> indexMeasureList;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

}