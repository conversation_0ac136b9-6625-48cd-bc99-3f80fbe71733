package com.bestpay.bigdata.bi.common.oss;

import com.bestpay.bigdata.bi.common.enums.FileSystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * ClassName: UploadContext
 * Package: com.bestpay.bigdata.bi.common.oss
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/11/29 15:20
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OssContext implements Serializable
{
    private FileSystemTypeEnum fileSystemType;
}
