package com.bestpay.bigdata.bi.common.error;

public enum AppEmbedErrorCode implements ErrorCodeSupplier {
    APP_EMBED_TASK_CODE_ERROR("00001",ErrorType.BUSINESS_ERROR),
    APP_EMBED_PARAM_ERROR("00002",ErrorType.BUSINESS_ERROR),
    APP_URL_NOT_COMPLETE_ERROR("00003",ErrorType.BUSINESS_ERROR),
    APP_DECRY_ERROR("00004",ErrorType.BUSINESS_ERROR),
    APP_SERVER_INTERNAL_ERROR("00005",ErrorType.BUSINESS_ERROR),
    APP_EMBED_APP_KEY_ERROR("00006",ErrorType.BUSINESS_ERROR),
    APP_VERIFY_IDENTITY_ERROR("00007",ErrorType.BUSINESS_ERROR),
    APP_EMBED_CONF_ERROR("00008",ErrorType.BUSINESS_ERROR),

    ;

    private static final String PREFIX = "APP_EMBED_";

    private final ErrorCode errorCode;

    AppEmbedErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
