package com.bestpay.bigdata.bi.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: den<PERSON><PERSON><PERSON>
 * @CreateDate: 2021/12/21 10:55
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("权限信息")
public class PermissionInfo implements Serializable {

  @ApiModelProperty(value = "页面资源")
  private String resource;

  @ApiModelProperty(value = "页面权限等级（0：浏览，1：修改，2：管理）")
  private String permissionLevel;

  @ApiModelProperty(value = "按钮资源")
  private List<String> buttons;
}
