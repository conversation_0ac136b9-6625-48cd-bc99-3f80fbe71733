package com.bestpay.bigdata.bi.common.util;

import cn.hutool.json.JSONUtil;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.MDC;

public class LogUtil {


    public static void error(Logger log, String logContent, Object... arguments) {
        log.error(logContent, arguments);
    }


    public static Object[] convertObject2String(Object... arguments) {
        Object[] args = new Object[arguments.length];
        for (int i = 0; i < arguments.length; i++) {
            Object arg = arguments[i];
            if (arg != null && !arg.getClass().isPrimitive()) {
                arg = JSONUtil.toJsonStr(arg);
            }
            args[i] = arg;
        }
        return args;
    }

    public static void initTraceLogId() {
        String traceLogId = UUID.randomUUID().toString().replace("-", "");
        MDC.put("Trace-Log-Id", traceLogId);
    }

    public static String getStackTrace(Throwable t) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw, true);
        t.printStackTrace(pw);
        pw.flush();
        sw.flush();
        return sw.toString();
    }

    /**
     * 去除异常中堆栈的信息，只返回主要信息
     * @param message
     * @return
     */
    public static String getMainMessage(String message) {
        StringBuilder sbMsg = new StringBuilder();
        String newLineCharacter = "new_line_character";
        message=message.replace("\r", newLineCharacter).replace("\n",newLineCharacter);
        String[] msgs = message.split(newLineCharacter);
        for (String msg : msgs) {
            if (msg.length() > 3) {
                String p = "at\\s.*?(\\..*?)*\\(.*\\)";
                if (msg.trim().matches(p)) {
                    continue;
                }
                sbMsg.append(msg).append("\r\n");
            }
        }

        return sbMsg.toString();

    }


}
