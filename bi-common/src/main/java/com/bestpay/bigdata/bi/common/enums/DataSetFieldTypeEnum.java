package com.bestpay.bigdata.bi.common.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023-05-24-10:40
 */
public enum DataSetFieldTypeEnum {
    PARAM,
    DIMENSION,
    MEASURE,
    COMPUTE,
    ALL;

    public static DataSetFieldTypeEnum getNameByCode(String filedType){
        for (DataSetFieldTypeEnum value : DataSetFieldTypeEnum.values()) {
            if(Objects.equals(value.name(), filedType)){
                return value;
            }
        }
        return null;
    }
}
