package com.bestpay.bigdata.bi.common.dto.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName: TotalDTO
 * Package: com.bestpay.bigdata.bi.report.bean.report
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/10/23 17:16
 * @Version 1.0
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TotalDTO {

    /** no / left / right */
    private String rowTotal;

    /** no / up / down */
    private String colTotal;

    /** no / up / down */
    private String subTotal;
}
