package com.bestpay.bigdata.bi.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2022-03-01-15:45
 */
@Data
@ApiModel(value = "智加当前用户实体信息类")
public class AIplusUserInfo implements Serializable {

    @ApiModelProperty(value = "账号")
    private String account;

    @ApiModelProperty(value = "中文名")
    private String nickName;

    @ApiModelProperty(value = "地市code")
    private String cityCode;

    @ApiModelProperty(value = "组织code")
    private String orgCode;

    @ApiModelProperty(value = "省份code")
    private String provinceCode;
}
