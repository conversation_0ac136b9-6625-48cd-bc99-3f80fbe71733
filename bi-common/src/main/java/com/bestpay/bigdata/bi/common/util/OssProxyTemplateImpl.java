package com.bestpay.bigdata.bi.common.util;

import com.bestpay.drip.oss.proxy.client.HttpSender;
import com.bestpay.drip.oss.proxy.client.OssProxyTemplate;
import com.bestpay.drip.oss.proxy.common.UploadRequest;
import com.bestpay.drip.oss.proxy.common.UploadResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/7/23
 */
@Service
public class OssProxyTemplateImpl extends OssProxyTemplate {
    @Autowired
    private HttpSender sender;

    public UploadResult toOss(UploadRequest request) {
        // 设置应用名
        request.setAppName("bigdata-bi");
        return sender.sendUpload(request);
    }
}
