package com.bestpay.bigdata.bi.common.dto.dashboard;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "水印设置")
public class WatermarkSetting {

    @ApiModelProperty(value = "是否展示水印，true：展示水印，false:不展示水印")
    @NotNull(message = "是否展示水印不能为空")
    private Boolean showWatermark;


    @ApiModelProperty(value = "水印字体大小")
    private Integer fontSize;


    @ApiModelProperty(value = "水印颜色   颜色中包含 透明度")
    private String fontColor;

    public static void main(String[] args) {
        WatermarkSetting watermarkSetting = new WatermarkSetting();
//        watermarkSetting.setShowWatermark(true);
//        watermarkSetting.setFontSize(12);
//        watermarkSetting.setFontColor("rgb(256, 256, 256,0.75)");
//        System.out.println(JSONUtil.toJsonStr(watermarkSetting));
//        System.out.println(JSONUtil.toJsonStr(watermarkSetting).length());

        watermarkSetting.setShowWatermark(Boolean.FALSE);
        System.out.println(JSONUtil.toJsonStr(watermarkSetting));

    }


}
