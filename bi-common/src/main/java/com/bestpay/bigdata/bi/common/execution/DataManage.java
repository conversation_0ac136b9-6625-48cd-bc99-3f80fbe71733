package com.bestpay.bigdata.bi.common.execution;

import com.bestpay.bigdata.bi.common.entity.ResultBlock;
import com.bestpay.bigdata.bi.common.entity.ResultBlockMetadata;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-02-13-17:27
 */
public interface DataManage {

    void writeResultBlock(ResultBlock resultBlock);

    ResultBlock readResultBlock(ResultBlockMetadata metadata);

    void expireResultBlockList(List<ResultBlockMetadata> resultBlockMetadataList);
}
