package com.bestpay.bigdata.bi.common.dto.report.function;

import com.bestpay.bigdata.bi.common.dto.report.HighFunction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/8 15:40
 * @Description :
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TotalFunctionInfo extends FunctionInfo{

    private List<HighFunction> highFunctionList;
}
