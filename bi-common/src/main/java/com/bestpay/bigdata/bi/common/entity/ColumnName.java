package com.bestpay.bigdata.bi.common.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: dengyanwei
 * @CreateDate: 2021/12/21 10:55
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "列名")
public class ColumnName implements Serializable {

  @ApiModelProperty(value = "id")
  private Long id;

  @ApiModelProperty(value = "列名英文名")
  private String enName;

  @ApiModelProperty(value = "列名中英文名")
  private String uuid;

  @ApiModelProperty(value = "列名英文名")
  private Boolean isHide = false;

  @ApiModelProperty(value = "聚合")
  private String polymerization;

  @ApiModelProperty(value = "列名英文名")
  private String prop;

  @ApiModelProperty(value = "列名中文名")
  private String label;

  @ApiModelProperty(value = "类型名称：指标-index；字段-field；")
  private String reportField;
  /**
   * 数据格式中的度量单位据类型
   */
  @ApiModelProperty(value = "数据格式中的度量单位数据类型")
  private String dataType;
  /**
   * 数据格式中的度量单位小数位数
   */
  @ApiModelProperty(value = "数据格式中的度量单位小数位数")
  private Integer decimaCarry;
  /**
   * 数据格式中的度量单位
   */
  @ApiModelProperty(value = "数据格式中的度量单位")
  private Integer unit;

  /**
   * 是否隐藏 数据格式中的度量单位, 默认展示单位
   */
  private Boolean isShowUnit;


  /**
   * 数据格式中的是否显示千分位
   */
  @ApiModelProperty(value = "数据格式中的是否显示千分位")
  private Boolean showThousandth;

  @ApiModelProperty(value = "指标别名")
  private String nickName;

  @ApiModelProperty(value = "是否统计小计")
  private Boolean showSubtotal=false;

  @ApiModelProperty(value = "展示类型")
  private String showTypeName;

  @ApiModelProperty(value = "子类")
  private List<ColumnName> children;

  private List<FatherHeaderInfo> father;

  @ApiModelProperty(value = "日期分组类型")
  private Integer dateGroupType;

  @ApiModelProperty(value = "是否最新展示列")
  private Boolean isLastShowColumn = false;

  /**是否是行总计*/
  @ApiModelProperty(value = "是否是行总计")
  private Boolean isRowTotal = false;


  @ApiModelProperty(value = "选中样式")
  private String styleSelection;

  @ApiModelProperty(value = "列宽")
  private String columnConfig;

  @ApiModelProperty(value = "表示所有数据列中的最大值，用于前端雷达图最大轴顶点默认值")
  private BigDecimal maxValue;

  @ApiModelProperty(value = "表示所有数据列中的最小值，用于前端雷达图最大轴顶点默认值")
  private BigDecimal minValue;

//  public ColumnName(String prop, String label,
  public ColumnName(String enName,
                    String prop, String label,
      List<ColumnName> children,
      String showTypeName,
      Integer dateGroupType,
      String uuid,
      Long id) {
    this.enName = enName;
    this.prop = prop;
    this.label = label;
    this.children = children;
    this.showTypeName = showTypeName;
    this.dateGroupType = dateGroupType;
    this.uuid = uuid;
    this.id = id;
  }

  public ColumnName(String prop, String label, List<ColumnName> children) {
    this.prop = prop;
    this.label = label;
    this.children = children;
  }

  public ColumnName(String prop,
      String label,
      String reportField,
      List<ColumnName> children,
      String showTypeName,
      String uuid, Long id,
      Integer dateGroupType) {
    this.prop = prop;
    this.label = label;
    this.reportField = reportField;
    this.children = children;
    this.showTypeName = showTypeName;
    this.dateGroupType = dateGroupType;
    this.uuid = uuid;
    this.id = id;
  }
}
