package com.bestpay.bigdata.bi.common.dto.report;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * ClassName: HighFunction
 * Package: com.bestpay.bigdata.bi.parse.entity
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/11/8 14:11
 * @Version 1.0
 */
@Data
@ApiModel(description = "高级函数")
public class HighFunction {

    /** high function name */
    @ApiModelProperty(value = "函数名称")
    private String functionName;

    @ApiModelProperty(value = "高级函数列表")
    public final static List<String> highFunctionList = Lists.newArrayList("total", "fixed");
}
