package com.bestpay.bigdata.bi.common.request.metaData;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2023-07-12-10:25
 */
@Data
@Builder
public class MetaDataQueryDatabaseRequest implements Serializable {

    /**
     * 数据源类型
     */
    private String datasourceType;

    /**
     * 数据源使用类型（仅支持：read、write）目前只有 Mysql使用
     */
    private String datasourceUseType;

    /**
     * 数据源名称
     */
    private String datasourceName;

    /**
     * 库名称
     */
    private String dbName;
}
