package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author:gaodingsong
 * @description: 列表头样式
 * @createTime:2024/5/10 18:13
 * @version:1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "列表头样式")
public class ColumnHeaderStyle extends TableDataStyle {

    @ApiModelProperty(value = "行高")
    private Integer rowHeight;

    @ApiModelProperty(value = "是否显示列表头")
    private Boolean showColumnHeader;
}
