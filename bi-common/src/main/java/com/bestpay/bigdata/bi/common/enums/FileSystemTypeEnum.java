package com.bestpay.bigdata.bi.common.enums;
import lombok.Getter;

/**
 * <AUTHOR>
 */

public enum FileSystemTypeEnum {
    /**
     * mini_io: 技术部miniio，被包了一层服务，通过中间服务访问miniio，可以外网访问
     */
    MINIIO("mini_io"),
    /**
     * minio_original：直连miniio组件
     */
    MINIO_ORIGINAL("minio_original"),;

    @Getter
    private final String code;

    FileSystemTypeEnum(String code) {
        this.code = code;
    }
}
