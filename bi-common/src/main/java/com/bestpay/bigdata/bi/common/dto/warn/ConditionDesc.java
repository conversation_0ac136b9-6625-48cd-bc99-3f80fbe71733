package com.bestpay.bigdata.bi.common.dto.warn;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName: ConditionDesc
 * Package: com.bestpay.bigdata.bi.report.bean
 * Description: 预警规则描述 单个条件 model
 *
 * <AUTHOR>
 * @Create 2023/8/4 16:33
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("预警规则描述")
public class ConditionDesc {

    @ApiModelProperty(value = "报表中配置数据源的唯一标识")
    private String uuid;

    @ApiModelProperty(value = "报表配置的唯一标识")
    private String configUuid;

    @ApiModelProperty(value = "'field' / 'contrast' 标识维度 'index' / 'overlayIndex' 标识指标")
    private String reportField;

    @ApiModelProperty(value = "日期类型")
    private String dateType;

    @ApiModelProperty(value = "比较 @ScopeFilterTypeEnum")
    private String scopeFilterType;

    @ApiModelProperty(value = "字符串值")
    private String stringValue;

    @ApiModelProperty(value = "对应的值")
    private List<Object> values;

    @ApiModelProperty(value = "字段显示类型")
    private String showTypeName;

    @ApiModelProperty(value = "日期类型维度统计类型：1-日；2-月；3-年；4-周；5-季度；6-年月日 时分秒")
    private Integer dateGroupType;

    @ApiModelProperty(value = "字段名称")
    private String name;

    @ApiModelProperty(value = "日期字段ID")
    private Long datePickerId;

    @ApiModelProperty(value = "筛选类型")
    private String filterType;

    @ApiModelProperty(value = "时间类型")
    private String timeType;

    @ApiModelProperty(value = "默认类型")
    private String defaultType;

    @ApiModelProperty(value = "默认值")
    private List<String> defaultValues;

    @ApiModelProperty(value = "是否包含当前")
    private Boolean includeCurrent;

}
