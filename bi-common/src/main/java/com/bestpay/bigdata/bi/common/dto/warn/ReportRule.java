package com.bestpay.bigdata.bi.common.dto.warn;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName: ReportRule
 * Package: com.bestpay.bigdata.bi.report.bean
 * Description: 描述一个预警规则
 *
 * <AUTHOR>
 * @Create 2023/8/4 16:30
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("预警规则")
public class ReportRule{

    /**多个条件*/
    @ApiModelProperty(value = "多个条件")
    private List<ConditionDesc> conditionDescList;

    /**期望*/
    @ApiModelProperty(value = "期望")
    private ExpectationDesc expectationDesc;
}
