package com.bestpay.bigdata.bi.common.dto.report.function;

import com.bestpay.bigdata.bi.common.dto.report.component.DimensionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/8 15:41
 * @Description :
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "Lod高级函数信息")
public class LodFunctionInfo extends FunctionInfo {

    /** salary / include(province, city: sum(salary))  */


    @ApiModelProperty(value = "附属指标uuid")
    private String relatedUuid;

    @ApiModelProperty(value = "Fixed dimension : (province, city) 可能是uuid")
    List<DimensionComponentPropertyDTO> dimensionList;

    /** Measure list : sum(salary) */
    @ApiModelProperty(value = "指标组件")
    IndexComponentPropertyDTO coreMeasure;

    /** * - / + no */
    @ApiModelProperty(value = "操作符")
    private String operator;

    /** otherMeasure position:  left or right */
    @ApiModelProperty(value = "位置")
    private String position;

    /** salary : 计算字段 */
    /** other dimension: logic little report, dimension: [origin view dimension] measure: [ measure ]*/
    @ApiModelProperty(value = "指标组件")
    private IndexComponentPropertyDTO otherMeasure;
}
