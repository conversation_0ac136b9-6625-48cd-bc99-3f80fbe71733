package com.bestpay.bigdata.bi.common.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: den<PERSON><PERSON><PERSON>
 * @CreateDate: 2021/2/25 14:59
 */
@Data
@ApiModel("分页参数")
public class PageVo {
    /**当前页数*/
    @ApiModelProperty("当前页数")
    private Integer pageNum;
    /**每页条数*/
    @ApiModelProperty("每页条数")
    private Integer pageSize;
}