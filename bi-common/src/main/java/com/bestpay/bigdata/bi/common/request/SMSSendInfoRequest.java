package com.bestpay.bigdata.bi.common.request;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SMSSendInfoRequest implements Serializable {

    /**
     * 告警处罚时间
     */
    private String triggerTime;

    /**
     * 服务应用名称
     */
    private String application;

    /**
     * 短信内容
     */
    private String issue;

    /**
     * SMS 短信 LX 蓝信
     */
    private String notifyType;

    /**
     * 机房
     */
    private String dc;

    /**
     * zabbix监控告警:全链路监控告警;
     * OB监控告警:
     * redis监控告警:等等
     */
    private String type;

    /**
     * 设备
     */
    private String device;

    /**
     * 地址
     */
    private String ipAddress;

    /**
     *
     */
    private String level;

    /**
     * 监控项
     */
    private String monitorItem;


    /**
     * 通知手机号  多个手机号  逗号分割  和 notifyGroup 2选一即可
     */
    private String notifyList;

    private String notifyGroup;


    private String sign;


}
