package com.bestpay.bigdata.bi.common.common;

import java.util.Objects;

/**
 * 图表类型
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/5/23 10:07
 */
public enum ChartTypeEnum {

  /**
   * 列表
   */
  LIST_TABLE(0),
  /**
   * 柱形图
   */
  COLUMN_CHART(1),
  /**
   * 折线图
   */
  LINE_CHART(2),
  /**
   * 环形图
   */
  DONUT_CHART(3),
  /**
   * 条形图
   */
  BAR_CHART(4),
  /**
   * 簇状图
   */
  CLUSTER_AND_LINE_CHART(5),
  /**
   * 堆积图
   */
  STACK_CHART(6),
  /**
   * 堆积折线图
   */
  STACK_AND_LINE_CHART(7),
  /**
   * 面积图
   */
  AREA_CHART(8),
  /**
   * 地图
   */
  MAP_CHART(9),
  /**
   * 矩形树图
   */
  RECTANGLE_CHART(10),
  /**
   * 百分比堆积图
   */
  PERCENTAGE_CHART(11),
  /**
   * 条形堆积图
   */
  BAR_STACKING_CHART(12),

  /**
   * 桑基图
   */
  SAN_KEY_CHART(100),

  /**
   * 散点图
   */
  SCATTER_CHART(101),

  /**
   * 饼图
   */
  PIE_CHART(102),

  /**
   * 漏斗图
   */
  FUNNEL_CHART(103),

  /**
   * 雷达图
   */
  RADAR_CHART(104),

  /**
   * 瀑布图
   */
  WATER_FALL_CHART(105),

  /**
   * 指标
   */
  INDEX(13);

  Integer code;

  ChartTypeEnum(Integer code) {
    this.code = code;
  }

  public Integer getCode() {
    return code;
  }

  public static String getName(Integer code) {
    ChartTypeEnum[] chartTypeEnums = ChartTypeEnum.values();
    for (ChartTypeEnum chartTypeEnum : chartTypeEnums) {
      if (Objects.equals(chartTypeEnum.getCode(), code)) {
        return chartTypeEnum.name();
      }
    }
    return null;
  }

  public static ChartTypeEnum getChartType(Integer code) {
    ChartTypeEnum[] chartTypeEnums = ChartTypeEnum.values();
    for (ChartTypeEnum chartTypeEnum : chartTypeEnums) {
      if (Objects.equals(chartTypeEnum.getCode(), code)) {
        return chartTypeEnum;
      }
    }
    return null;
  }

  /**
   * 判断是否为堆积图类型
   *
   * @param type
   * @return
   */
  public static boolean isStackTypeChart(ChartTypeEnum type) {
    return STACK_CHART.equals(type) || STACK_AND_LINE_CHART.equals(type) || BAR_STACKING_CHART.equals(type);
  }

}
