package com.bestpay.bigdata.bi.common.enums;

public enum ComponentTypeEnum {

  REPORT("report", "图表"),
  INDEX("index", "指标"),
  TEXT("text", "文本"),

  IMAGE("image", "图片"),
  TIME("time","时钟"),
  MATERIAL("material","素材"),




  TAB("tab", "tab页"),
  //本次不涉及
  /*VIDEO("video", "视频"),
  TAB("tab","tab组件")*/
  ;

  private final String code;
  private final String message;

  ComponentTypeEnum(String code, String message) {
    this.code = code;
    this.message = message;
  }

  public String getCode() {
    return code;
  }

  public String getMessage() {
    return message;
  }

  public static ComponentTypeEnum getByCode(String code) {
    for (ComponentTypeEnum componentType : values()) {
      if (componentType.code.equals(code)) {
        return componentType;
      }
    }

    return null; // Code not found
  }
}
