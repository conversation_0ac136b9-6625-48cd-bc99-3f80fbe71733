package com.bestpay.bigdata.bi.common.enums;

import com.google.common.collect.Maps;
import java.util.Map;

public enum FormatUnitEnum {

    thousand(1000,"K"),
    million(1000000,"M"),
    ten_thousand(10000,"万"),
    ten_million(10000000,"千万"),
    hundred_million(100000000,"亿"),;

    private int num;
    private String unit;

    private static Map<Integer, String> numUnitMap = Maps.newHashMap();
    static {
        for (FormatUnitEnum value : FormatUnitEnum.values()) {
            numUnitMap.put(value.num,value.unit);
        }
    }

    FormatUnitEnum(int num, String unit) {
        this.num=num;
        this.unit=unit;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public static String getUnitByNum(Integer num){
        if(num==null){
            return "";
        }

        if(numUnitMap.get(num)!=null){
            return numUnitMap.get(num);
        }

        return "";
    }

}
