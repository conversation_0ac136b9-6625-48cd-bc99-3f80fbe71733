package com.bestpay.bigdata.bi.common.error;

/**
 * 智加错误code
 */
public enum ZhiJiaErrorCode implements ErrorCodeSupplier {

    REQUEST_CURRENT_USER_ERROR("00001", ErrorType.BUSINESS_ERROR),
    INVOKE_ZHIJIA_ERROR("00002", ErrorType.BUSINESS_ERROR);

    private static final String PREFIX = "ZHIJIA_";

    private final ErrorCode errorCode;

    ZhiJiaErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
