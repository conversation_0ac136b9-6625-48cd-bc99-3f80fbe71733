package com.bestpay.bigdata.bi.common.error;

public enum DataScreenErrorCode implements ErrorCodeSupplier {

    DATA_SCREEN_INFO_ERROR("00001", ErrorType.BUSINESS_ERROR),
    DATA_SCREEN_COMPONENT_INFO_ERROR("00002", ErrorType.BUSINESS_ERROR),

    DATA_SCREEN_COMPONENT_LAYER_EVENT_ERROR("10001", ErrorType.USER_ERROR),
    DATA_SCREEN_PERMISSIONS("10001", ErrorType.USER_ERROR),
    ;

    private static final String PREFIX = "DATA_SCREEN_";

    private final ErrorCode errorCode;

    DataScreenErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
