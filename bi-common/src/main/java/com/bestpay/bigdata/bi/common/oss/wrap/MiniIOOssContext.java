package com.bestpay.bigdata.bi.common.oss.wrap;

import com.bestpay.bigdata.bi.common.enums.FileSystemTypeEnum;
import com.bestpay.bigdata.bi.common.oss.OssContext;
import lombok.Data;

import java.io.InputStream;

/**
 * ClassName: MiniIOUploadContext
 * Package: com.bestpay.bigdata.bi.common.oss
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/11/29 15:22
 * @Version 1.0
 */
@Data
public class MiniIOOssContext
        extends OssContext
{
    /** upload */
    private InputStream inputStream;
    private Integer ttlDays;
    private String fileExtName; // jpg ...
    private Integer webURLMode;

    /** download */
    private String miniIoPath;
    private Long fileSize;

    public MiniIOOssContext(FileSystemTypeEnum fileSystemType, InputStream inputStream, Integer ttlDays, String fileExtName, Integer webURLMode)
    {
        super(fileSystemType);
        this.inputStream = inputStream;
        this.ttlDays = ttlDays;
        this.fileExtName = fileExtName;
        this.webURLMode = webURLMode;
    }

    public MiniIOOssContext(FileSystemTypeEnum fileSystemType, InputStream inputStream, Integer ttlDays, String fileExtName, Integer webURLMode, Long fileSize)
    {
        super(fileSystemType);
        this.inputStream = inputStream;
        this.ttlDays = ttlDays;
        this.fileExtName = fileExtName;
        this.webURLMode = webURLMode;
        this.fileSize = fileSize;
    }

    public MiniIOOssContext(FileSystemTypeEnum fileSystemType, InputStream inputStream, String fileExtName)
    {
        super(fileSystemType);
        this.inputStream = inputStream;
        this.fileExtName = fileExtName;
    }

    public MiniIOOssContext(FileSystemTypeEnum fileSystemType, String miniIoPath)
    {
        super(fileSystemType);
        this.miniIoPath = miniIoPath;
    }
}
