package com.bestpay.bigdata.bi.common.refactor.bean;

import cn.hutool.core.util.BooleanUtil;
import com.bestpay.bigdata.bi.common.common.Constant;
import com.bestpay.bigdata.bi.common.common.ResultTypeEnum;
import com.bestpay.bigdata.bi.common.dto.TableDataBlock;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.entity.ResultBlockMetadata;
import com.bestpay.bigdata.bi.common.enums.FileType;
import com.bestpay.bigdata.bi.common.enums.ReportModuleEnum;
import com.bestpay.bigdata.bi.common.enums.Status;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "数据探查取消查询请求类")
public class IQueryContext implements Serializable {

    private static final long serialVersionUID = -1;

    private String querySource ;

    //查询信息
    /**
     * 查询用户
     */
    protected String username;
    /**
     * 请求系统
     */
    protected String requestSystem;

    /**
     * 模块分组
     */
    protected String routerGroup;

    /**
     * 查询用户所属组织
     */
    protected String userOrg;
    /**
     * 查询sql
     */
    protected String sql;

    protected List<String> batchSql;
    /**
     * 查詢引擎類型
     **/
    protected String engineName;
    /**
     * 集群标识
     **/
    protected String clusterName;

    /**
     * 数据源类型
     */
    private String datasourceType;

    /**
     * 数据源
     **/
    protected String dataSource;
    /**
     * 查詢語句類型
     **/
    protected int sqlType;

    //查询时间信息
    /**
     * 查询uuid
     */
    @ApiModelProperty(value = "查询后产生的queryId",required = true)
    protected String queryId;


    /**
     * engine side query id
     */
    protected String engineQueryId;

    /**
     * 查询开始时间
     */
    protected long queryStartMillis;
    /**
     * 查询结束时间
     */
    protected long queryFinishMillis;
    /**
     * 查询时长
     */
    protected double queryTimeSecond;
    /**
     * 查询状态
     */
    protected volatile Status status;


    protected boolean isUseCache;

    // 查询结果信息
    /**
     * 列名称
     */
    protected String[] columnNames;
    /**
     * 列名字段类型
     */
    protected String[] columnTypes;
    /**
     * 查詢結果
     * List<Map<String, Object>>
     *   --->List<List<String>>
     **/
    protected List<List<String>> results;

    /** 结果数据保存在miniIO */
    protected String resultPath;


    /**
     * result block meta data
     */
    protected List<ResultBlockMetadata> blockMetadataList = new ArrayList<>();


    protected String resultCacheKey;

    private String resultType = ResultTypeEnum.DATA.getCode();

    /**
     * 是否有结果
     */
    protected boolean isEmpty;
    /**
     * 查詢結果的行数
     **/
    protected long resultRowsNum;
    /**
     * 查询结果大小
     */
    protected long resultLength;

    /**
     * 查詢系统類型
     **/
    protected int typeCode;

    /**
     * 来源
     **/
    protected String source;

    /**
     * 操作類型(0：查询 1：下载)
     **/
    protected int operateTypeCode;

    /**
     * 文件路径
     **/
    protected String filePath;

    protected String message;

    protected String shortMessage;

    protected int timeOutSeconds = Constant.STATEMENT_QUERY_TIMEOUT_SECONDS;

    /**
     * 最大查询行数: -1表示不限，默认是0，限制Constant.QUERY_RESULT_RECORD_COUNT
     */
    protected long maxQueryRows;

    /**
     *当前页
     */
    protected long pageNum;

    /**
     * 每页多少条
     */
    protected long pageSize;
    /**
     * 下载文件类型
     */
    protected FileType fileType;

    /**
     * 不需要压缩
     * （默认需要压缩）
     */
    protected  boolean notZip;

    protected String sizeOverflowColumn;

    protected String uploadTableName;

    /**
     * 数据是否包含敏感信息: 0: false 1:true
     */
    protected Integer fileContainSensitiveInfo;

    protected TableDataBlock tableDataBlock;

    /**
     * 敏感字段
     */
    protected String sensitiveFields;

    protected Boolean queryWithTimeout=true;

    protected byte[] downloadFileBytes;

    protected String downloadFileName;

    protected Boolean isTyzd;

    public IQueryContext(String sql, String engineName, String dataSource, String username,
                        int statementType) {
        this.sql = sql;
        this.isEmpty = true;
        this.dataSource = dataSource;
        this.engineName = engineName;
        this.username = username;
        this.sqlType = statementType;
    }

    public IQueryContext(String sql, String engineName, String username) {
        this.sql = sql;
        this.username = username;
        this.engineName = engineName;
    }

    public IQueryContext(String sql, String engineName, String username, int statementType) {
        this.sql = sql;
        this.username = username;
        this.engineName = engineName;
        this.sqlType = statementType;
    }

    @Override
    public QueryContext clone() {
        QueryContext qc = new QueryContext(this.sql, this.engineName, this.dataSource, this.username, this.sqlType);
        qc.setQueryFinishMillis(this.queryFinishMillis);
        qc.setQueryTimeSecond(this.queryTimeSecond);
        qc.setQueryId(this.queryId);
        qc.setQueryStartMillis(this.queryStartMillis);
        qc.setStatus(this.status);
        qc.setEmpty(this.isEmpty);
        qc.setResultRowsNum(this.resultRowsNum);
        qc.setColumnNames(this.columnNames);
        qc.setColumnTypes(this.columnTypes);
        qc.setMessage(this.message);
        qc.setShortMessage(this.shortMessage);
        qc.setResultLength(this.resultLength);
        qc.setResultType(this.resultType);
        return qc;
    }

    public void setUseCache(boolean useCache) {
        this.isUseCache =  useCache;
    }

    public void setUseCache(String fromFlag, Boolean isRealtime) {
        // 默认走缓存
        this.isUseCache =  true;

        // 1、前端直接控制是否走缓存
        if(BooleanUtil.isTrue(isRealtime)){
            this.isUseCache = false;
        }

        // 2、按场景判断是否走缓存
        if (!Objects.nonNull(fromFlag)) {
            return;
        }

        if (fromFlag.equals(ReportModuleEnum.DATA_SCREEN.name())
                || fromFlag.equals(ReportModuleEnum.DASHBOARD_REFRESH_BUTTON.name())) {
            this.isUseCache = false;
        }
    }
}
