package com.bestpay.bigdata.bi.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * todo 历史存的是report
 */
public enum DatePickerSourceEnum {
  REPORT("report","报表"),
  REPORT_CONDITION("report_condition","报表筛选器"),
  REPORT_FILTER("report_filter","报表过滤"),
  DASHBOARD_REPORT_CONDITION("dashboard_report_condition","仪表板报表筛选器"),
  DASHBOARD_REPORT_FILTER("dashboard_report_filter","仪表板报表过滤"),
  WARN("warn","预警"),
  REPORT_SUBSCRIBE("report_subscribe","报表订阅");

  @Getter
  private final String code;
  @Getter
  private final String msg;


  DatePickerSourceEnum(String code, String msg) {
    this.code = code;
    this.msg=msg;
  }
}
