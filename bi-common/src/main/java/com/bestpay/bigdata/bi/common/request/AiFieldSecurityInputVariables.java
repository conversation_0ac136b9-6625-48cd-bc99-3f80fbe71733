package com.bestpay.bigdata.bi.common.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;



public class AiFieldSecurityInputVariables {
    private List<String> fieldInfoList =new ArrayList<>();
    private String query;


    public void addFieldInfo(String fieldEng, String fieldChinese, String fieldCommon, String data) {
        String fieldInfo = String.format("{\"字段英文名\":\"%s\",\"字段中文名\":\"%s\",\"字段描述\":\"%s\",\"数据分级\":null,\"数据样例\":[\"%s\"]}"
                , fieldEng, fieldChinese, fieldCommon, data);
        fieldInfoList.add(fieldInfo);
    }

    public String getQuery() {
        String fieldInfo = String.join(",", fieldInfoList);
        query= String.format("{\"表字段信息\":[%s]}", fieldInfo);
        return query;
    }

    public int getFieldSize(){
        return fieldInfoList.size();
    }

}
