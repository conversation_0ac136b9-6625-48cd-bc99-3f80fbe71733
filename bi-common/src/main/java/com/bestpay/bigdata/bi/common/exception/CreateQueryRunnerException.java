package com.bestpay.bigdata.bi.common.exception;

import com.bestpay.bigdata.bi.common.enums.CodeEnum;

/**
 * <AUTHOR>
 * @date 2021/12/7 14:35
 **/
public class CreateQueryRunnerException extends QueryExecutionException{
    private CodeEnum codeEnum;

    public CreateQueryRunnerException(String code, String message) {
        super(code,message);
    }

    public CreateQueryRunnerException(CodeEnum codeEnum) {
        super(codeEnum);
    }
}
