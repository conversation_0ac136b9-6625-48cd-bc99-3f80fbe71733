package com.bestpay.bigdata.bi.common.error;

public enum SftpErrorCode implements ErrorCodeSupplier {
    UPLOAD_ERROR("00001",ErrorType.BUSINESS_ERROR),
    DOWNLOAD_ERROR("00002",ErrorType.BUSINESS_ERROR),

    ;

    private static final String PREFIX = "SFTP_";

    private final ErrorCode errorCode;

    SftpErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
