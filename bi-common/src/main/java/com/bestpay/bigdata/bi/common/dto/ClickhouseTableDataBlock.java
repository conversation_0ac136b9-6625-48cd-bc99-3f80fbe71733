package com.bestpay.bigdata.bi.common.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/13
 */
public class ClickhouseTableDataBlock extends TableDataBlock{
    private String cluster;
    private String orderColumnName;

    public ClickhouseTableDataBlock(String databaseName, String tableName, List<Column> columns, List<List<String>> rowsData, String cluster, String orderColumnName) {
        super(databaseName, tableName, columns, rowsData);
        this.cluster = cluster;
        this.orderColumnName = orderColumnName;
    }

    public String getCluster() {
        return cluster;
    }

    public void setCluster(String cluster) {
        this.cluster = cluster;
    }

    public String getOrderColumnName() {
        return orderColumnName;
    }

    public void setOrderColumnName(String orderColumnName) {
        this.orderColumnName = orderColumnName;
    }
}
