package com.bestpay.bigdata.bi.common.dto.report.component;

import com.bestpay.bigdata.bi.common.dto.report.ReportScreeningConditionDTO;
import com.bestpay.bigdata.bi.common.enums.ScopeFilterTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "查询字段信息")
public class ConditionComponentPropertyDTO extends CommonComponentPropertyDTO {

  @ApiModelProperty(value = "聚合方式")
  private String polymerization;

  @ApiModelProperty(value = "字段值")
  private String stringValue;

  @ApiModelProperty(value = "筛选默认值")
  private ReportScreeningConditionDTO screeningCondition;

  @ApiModelProperty(value = "字段值列表")
  private List<String> valueList;

  @ApiModelProperty(value = "范围过滤类型",required = true,allowableValues = ScopeFilterTypeEnum.ALL_VALUE)
  private String scopeFilterType;
}
