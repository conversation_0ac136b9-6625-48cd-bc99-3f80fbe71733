package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "卡片样式配置")
public class CardStyleConfig {
  @ApiModelProperty(value = "是否展示标题")
  private Boolean showTitle;
  @ApiModelProperty(value = "上边距")
  private Integer paddingTop;
  @ApiModelProperty(value = "下边距")
  private Integer paddingBottom;
  @ApiModelProperty(value = "左边距")
  private Integer paddingLeft;

  @ApiModelProperty(value = "右 边距")
  private Integer paddingRight;
  @ApiModelProperty(value = "圆角")
  private Integer borderRadius;
  @ApiModelProperty(value = "边框颜色")
  private String borderColor;
  @ApiModelProperty(value = "样式")
  private Integer layoutStyle;
  @ApiModelProperty(value = "边框宽度")
  private Integer borderWidth;
}
