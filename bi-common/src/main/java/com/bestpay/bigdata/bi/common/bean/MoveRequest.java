package com.bestpay.bigdata.bi.common.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2023-06-05-16:51
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("仪表盘位置移动请求参数")
public class MoveRequest {


    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "顺序")
    private Long orderNum;

    @ApiModelProperty(value = "跨目录移动需要")
    private Long dirId;
}
