package com.bestpay.bigdata.bi.common.util;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintStream;
import java.net.InetSocketAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.bestpay.bigdata.bi.common.config.FastDFSFactory;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.pool2.ObjectPool;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.csource.fastdfs.ClientGlobal;
import org.csource.fastdfs.FileInfo;
import org.csource.fastdfs.StorageClient;
import org.csource.fastdfs.TrackerServer;
import org.springframework.stereotype.Component;

/**
 * FastDFS 操作类
 *
 * <AUTHOR>
 * @version 1.0.0 createTime: 15/3/24 下午13:02
 * @since 1.6
 */

@Slf4j
@Component
public class FastDFSPoolUtil {

    /**
     * fastdfs 配置文件路径
     */
    private final static String DFS_CONFIG_FILE_PATH = "fastdfs/client.conf";

    private final static String DFS_CONFIG_FILE = "client.conf";

    private ObjectPool<TrackerServer> pool;

    private static Properties prop = null;

    private static Properties propCom = null;

    public static String fastDfsUrl = "fastdfs://remoteFileName=%s,groupName=%s";

    private static String MATCHER_FORMAT = "^fastdfs://remoteFileName=(?<remoteFileName>[^,]+),groupName=(?<groupName>.+)$";

    /**
     * 初始化DFS配置
     */

    public void init() throws IOException {
        getInstatnce(DFS_CONFIG_FILE_PATH);
        try {
            ClientGlobal.init(propCom.getProperty("configName"));
            InetSocketAddress[] addresses = ClientGlobal.g_tracker_group.tracker_servers;
            log.info("length=" + addresses.length);
            log.info("add=" + addresses[0]);
            initPool();
            log.info("DFS init finished success");
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 初始化DFS配置
     */

    public void init(String adress) {
        try {
//            getInstatnce(DFS_CONFIG_FILE_PATH);
            ClientGlobal.initByProperties(buildFastDFSProperty(adress));
            InetSocketAddress[] addresses = ClientGlobal.g_tracker_group.tracker_servers;
            log.info("length=" + addresses.length);
            log.info("add=" + addresses[0]);
            initPool();
            log.info("DFS init finished success");
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
        }
    }
    private Properties buildFastDFSProperty(String adress){
        Properties properties=new Properties();
        properties.setProperty("fastdfs.tracker_servers",adress);
        return properties;
    }

    public void getInstatnce(String confPath) {
        if(propCom == null) {
            propCom = new Properties();
            propCom.setProperty("configName", DFS_CONFIG_FILE);
            try {
                prop = new Properties();
                InputStream in = FastDFSPoolUtil.class.getClassLoader().getResourceAsStream(confPath);
                prop.load(in);
                in.close();
                PrintStream printStream = new PrintStream(new File(DFS_CONFIG_FILE));
                prop.list(printStream);
                printStream.close();
            } catch (Exception e) {
                log.error(Throwables.getStackTraceAsString(e));
            }
        }
    }


    /**
     * 初始化连接池
     */
    public void initPool() {
        PooledObjectFactory<TrackerServer> factory = new FastDFSFactory();
        GenericObjectPoolConfig config = new GenericObjectPoolConfig();
        config.setMaxTotal(5);
        config.setMaxIdle(5);
        config.setMinIdle(1);
        pool = new GenericObjectPool<TrackerServer>(factory, config);
    }

    public void delete(String path) {
        Pattern pattern = Pattern.compile(MATCHER_FORMAT);
        Matcher matcher = pattern.matcher(fastDfsUrl);

        if (!matcher.matches()) {
            throw new BusinessException("Invalid miniio url format: " + fastDfsUrl);
        }
        delete(matcher.group("groupName"), matcher.group("remoteFileName"));
    }

    /**
     * 删除文件
     *
     * @param groupName 组名
     * @param remoteFilename 文件名
     * @return 执行结果
     */
    public int delete(String groupName, String remoteFilename) {
        int result = -1;
        TrackerServer trackerServer = null;
        try {
            trackerServer = pool.borrowObject();
            StorageClient storageClient = new StorageClient(trackerServer, null);
            // 删除文件
            groupName = StringUtils.isEmpty(groupName) ? "group1" : groupName;
            result = storageClient.delete_file(groupName, remoteFilename);
        } catch (Exception e) {
            log.error("删除文件失败， remoteFilename:" + remoteFilename);
            log.error(Throwables.getStackTraceAsString(e));
        } finally {
            closeTrackerServer(trackerServer);
        }
        return result;
    }

    /**
     * 上传文件到FastDFS服务器
     * @return 远程返回的文件名称和group名称
     */
    public Map<String, String> uploadByStream(byte[] fileBytes, String fileName) {
        Map<String, String> dfsMap = new HashMap<>();
        TrackerServer trackerServer = null;
        try {
            trackerServer = pool.borrowObject();
            StorageClient storageClient = new StorageClient(trackerServer, null);
            String[] results = storageClient.upload_file(fileBytes, fileName, null);
            if (results == null) {
                log.info("FastDFS文件上传失败,Error Code:" + storageClient.getErrorCode());
            } else {
                log.info("文件上传成功;GROUP_NAME:" + results[0] + " REMOTE_FILE_NAME:" + results[1]);
                // 远程返回的文件名称
                dfsMap.put("GROUP_NAME", results[0]);
                // 文件的groupId
                dfsMap.put("REMOTE_FILE_NAME", results[1]);
            }
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
        } finally {
            closeTrackerServer(trackerServer);
        }
        return dfsMap;
    }

    /**
     * 上传文件到FastDFS服务器
     * @return 远程返回的文件名称和group名称
     */
    public String uploadByStreamStr(byte[] fileBytes, String fileName) {
        Map<String, String> result = uploadByStream(fileBytes,fileName);
        String path = String.format(fastDfsUrl, result.get("REMOTE_FILE_NAME"), result.get("GROUP_NAME"));
        return path;
    }


    /**
     * 上传文件到FastDFS服务器
     *
     * @param localFilePath 文件路径
     * @return 远程返回的文件名称和group名称
     */
    public Map<String, String> uploadByPath(String localFilePath) {
        Map<String, String> dfsMap = new HashMap<String, String>();
        TrackerServer trackerServer = null;
        try {
            trackerServer = pool.borrowObject();
            StorageClient storageClient = new StorageClient(trackerServer, null);
            String[] results = storageClient.upload_file(localFilePath, null, null);
            if (results == null) {
                log.info("FastDFS文件上传失败,Error Code:" + storageClient.getErrorCode());
            } else {
                log.info("SUCCESS T0 UPLOAD, localFilePath:" + localFilePath + " GROUP_NAME:" + results[0] + " REMOTE_FILE_NAME:" + results[1]);
                // 返回组名
                dfsMap.put("GROUP_NAME", results[0]);
                // 返回文件id
                dfsMap.put("REMOTE_FILE_NAME", results[1]);
                FileInfo fileInfo = storageClient.get_file_info(results[0], results[1]);
                log.info(fileInfo.getSourceIpAddr());
                log.info(String.valueOf(fileInfo.getCreateTimestamp()));
            }
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
        } finally {
            closeTrackerServer(trackerServer);
        }
        return dfsMap;
    }

    /**
     * 下载文件
     */
    public byte[] download(String path) {
        Map<String,String> fileInfo = parseParam(path);
        String groupName = fileInfo.get("GROUP_NAME");
        String remoteFilename = fileInfo.get("REMOTE_FILE_NAME");

        byte[] result = null;
        TrackerServer trackerServer = null;
        try {
            trackerServer = pool.borrowObject();
            StorageClient storageClient = new StorageClient(trackerServer, null);
            // 下载文件
            groupName = StringUtils.isEmpty(groupName) ? "group1" : groupName;
            result = storageClient.download_file(groupName, remoteFilename);
            log.info("download result: group_name:" + groupName + " remoteFilename:" + remoteFilename);
        } catch (Exception e) {
            log.error("下载文件失败， remoteFilename:" + remoteFilename);
            log.error(Throwables.getStackTraceAsString(e));
        } finally {
            closeTrackerServer(trackerServer);
        }
        return result;
    }


    public Map<String, String> parseParam(String fastDfsUrl) {
        Pattern pattern = Pattern.compile(MATCHER_FORMAT);
        Matcher matcher = pattern.matcher(fastDfsUrl);

        if (!matcher.matches()) {
            throw new BusinessException("Invalid miniio url format: " + fastDfsUrl);
        }

        Map<String,String> param = Maps.newHashMap();
        param.put("REMOTE_FILE_NAME", matcher.group("remoteFileName"));
        param.put("GROUP_NAME", matcher.group("groupName"));

        return param;
    }


    /**
     * 关闭队列服务
     *
     * @param trackerServer trackerServer队列服务
     */
    public void closeTrackerServer(TrackerServer trackerServer) {
        // 退出前,一定要将队列服务关闭
        try {
            if (trackerServer != null) {
                pool.returnObject(trackerServer);
            }
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
        }
    }

}
