package com.bestpay.bigdata.bi.common.common;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/3/10 10:14
 */
public class Constant {


    /************ BI redis key ***********/
    public static final String REDIS_CACHE_NAME_SPACE = "bigdata-bi";
    public static final int REDIS_BIGDATA_BI_QUERY_EXPIRE_UNIT = 15;

    public static final String REDIS_BIGDATA_BI_QUERY_CONTEXT = "query-context";
    public static final String REDIS_BIGDATA_BI_QUERY_STATUS = "query-status:";
    public static final String REDIS_BIGDATA_BI_QUERY_START_TIME = "query-start-time:";
    public static final String REDIS_BIGDATA_BI_QUERY_PROGRESS_INFO_MAP_KEY = "query-progress-stats-info-map";
    public static final String REDIS_TASK_CONCURRENT_COUNT = "bi-probe:task-concurrent-count:";
    public static final String REDIS_BIGDATA_BI_DOWNLOAD_PATH = "download-path:";
    // key中包含文件类型与是否压缩，同一个queryId根据以上内容不同，可缓存多份文件
    public static final String REDIS_BIGDATA_BI_DOWNLOAD_SPECIAL_PATH = "download-path-special:";
    public static final String REDIS_BIGDATA_BI_PROBE_QUERY_ID = "probe-query-id:";

    public static final String REPORT_NULL_REPLACE = "bibestpay_report_null_value";

    /**
     * 查询超时时间,6*60秒+30秒的gap
     **/
    public static final int STATEMENT_QUERY_TIMEOUT_SECONDS = 60 * 6 + 30;

    /**
     * 报表下载刷新时间,1000微秒
     **/
    public static final int REPORT_DOWNLOAD_REFRESH_MICROSECOND = 1000;
    /**
     * 查询结果总数
     */
    public static final int QUERY_RESULT_RECORD_COUNT = 10000;

    /**
     * 查询脚本字符数
     */
    public static final int QUERY_SCRIPT_CHAR_LENGTH = 10000;


    /**
     * 脚本名称长度限制字符数
     */
    public static final int SAVE_SCRIPT_CHAR_LENGTH = 30;


    /**
     * 单个用户能够保存的脚本数量
     */
    public static final int SAVE_SCRIPT_COUNT_NUM = 100;


    /**
     * sql permission operator
     */
    public static final String SQL_PERMISSION_DROP = "Drop";
    public static final String SQL_PERMISSION_CREATE = "Create";
    public static final String SQL_PERMISSION_SELECT = "Select";
    public static final String SQL_PERMISSION_ALTER = "Alter";
    public static final String SQL_PERMISSION_INSERT = "Insert";
    public static final String SQL_PERMISSION_SHOW = "Show";
    public static final String SQL_PERMISSION_DELETE = "Delete";
    public static final String SQL_PERMISSION_TRUNCATE = "Truncate";
    public static final String SQL_PERMISSION_SET = "Set";
    public static final String SQL_PERMISSION_OTHER = "Other";
    public static final String SQL_PERMISSION_UDF = "USE_UDF";


    /** 数据探查 数据集支持的 上传文件格式 */
    public static final List<String> supportFileFormat = Lists.newArrayList("xlsx", "xls", "csv");


    /**
     * 查询语句
     */
    public static final int SQL_DQL_TYPE = 0;
    /**
     * 非查询语句
     */
    public static final int SQL_NONE_DQL_TYPE = 1;
    /**
     * 插入语句
     */
    public static final int SQL_INSERT_TYPE = 2;
    /**
     * 建表语句
     */
    public static final int SQL_CREATE_TYPE = 3;

    /**
     * Exception
     */
    public static final String EXCEPTION_BI_QUERY_CONTEXT_PRESENT_FALSE = "query context没有值！";

    public static final String QUERY_TIMEOUT_EXCEPTION_MESSAGE = "查询超时！本次任务执行时长已超过规定最大运行时间（{}min），请进行脚本优化";
    /**
     * Json check
     */
    public static final int JSON_CHECK_INDEX_DECIMAL_VALUES_LENGTH = 4;
    public static final int JSON_CHECK_INDEX_DIMENSION_COUNTS = 2;
    public static final int JSON_CHECK_FILTER_VALUES_LENGTH = 2;
    public static final int JSON_CHECK_FILTER_COUNTS = 10;
    public static final int JSON_CHECK_INDEX_TOP_MAX = 20;
    public static final int JSON_CHECK_INDEX_COUNTS = 12;
    public static final String JSON_CHECK_FILTER_DECIMAL_REGEXP = "^(\\-)?\\d+(\\.\\d{1,6})?$";
    public static final String JSON_CHECK_DATASET_TABLE_SPLIT = "\\.";
    public static final int JSON_CHECK_DATASET_TABLE_SPLIT_LENGTH = 3;

    /**
     * clickhouse 相关配置
     */

    public static final String SQL_FIELD_HANDLE_ALIA_PREFIX = "handle_";

    /**
     * 多维分析返回結果中的常量
     */
    public static final String ASCENDING = "ascending";
    public static final String DESCENDING = "descending";
    public static final String NAME = "name";
    public static final String DATA = "data";
    public static final String OVER_ABOVE = "及以上";
    public static final String UNDER = "以下";

    /**
     * Mysql config
     */
    public static final String DATA_SOURCE_DRIVER_CLASS_NAME_DEFAULT = "com.mysql.cj.jdbc.Driver";
    public static final int DATA_SOURCE_CONNECT_TIMEOUT_DEFAULT = 60000;
    public static final int DATA_SOURCE_MIN_IDLE_DEFAULT = 0;
    public static final int DATA_SOURCE_MAX_POOL_SIZE_DEFAULT = 250;
    public static final String DATA_SOURCE_CONNECT_TEST_QUERY_DEFAULT = "select 1";
    public static final long DATA_SOURCE_VALIDATION_TIMEOUT_DEFAULT = 1000L;

    /**
     * AI PLUS SYSTEM TYPE
     */
    public static final String BI_SYSTEM_TYPE = "BI";
    /**
     * cookie
     */
    public static final String COOKIE = "cookie";

    /**
     * 数据大屏
     */
    public static final String DATA_SCREEN_COOKIE = "data_screen_cookie";
    public static final String DATA_SCREEN_EMAIL = "<EMAIL>";
    public static final String DATA_SCREEN_NAME = "数据大屏";
    public static final String DATA_SCREEN_ADMIN = "dataScreenAdmin";
    /**
     * 报表查询智加平台资源标识名称
     */
    public static final String REPORT_QUEYY_RESOURCE_EN_NAME = "reportFormQuery";

    /**
     * data auth
     */
    public static final String DATA_AUTH_UNLIMITED = "-1";

    /**
     * page query
     */
    public static final Long PAGE_QUERY_UNLIMITED = -1L;

    /**
     * 最大行数限制，用于防止查询结果过多导致性能问题
     */
    public static final Integer MAX_ROWS = 10000;

    /**
     * 报表加工计算总数SQL
     */
    public static final String TOTAL_ROW_SQL = "SELECT COUNT(1) as totalRow FROM ( %s ) tmp";
    public static final String LIMIT_SQL_TEMPLATE = "select * from (%s) as qwer_tmp limit %s";

    public static final String OR_RELATION = " or ";
    public static final String AND_RELATION = " and ";
    public static final String APP_EMBED_CODE_FORMAT="%06d";
    /**
     * 报表查询统计格式转换
     */
    public static final String CK_FORMATDATETIME="formatDateTime";
    public static final String CK_DATE_ADD="dateAdd";
    public static final String CK_TOYEARWEEK="toYearWeek";
    public static final Integer CK_TOYEARWEEK_MODEL=9;
    public static final String SQL_DATE_ADD_YEAR="YEAR";
    public static final String SQL_DATE_ADD_MONTH="MONTH";
    public static final String SQL_DATE_ADD_DAY="DAY";
    public static final String SQL_DATE_ADD_QUARTER="QUARTER";
    public static final String SQL_DATE_ADD_WEEK="WEEK";
    public static final String CK_DATE_ADD_1="1";
    public static final String MYSQL_DATE_FORMAT="date_format";
    public static final String MYSQL_DATE_ADD="date_add";
    public static final String MYSQL_DATE_ADD_1=" interval 1 ";
    public static final String PRESTO_DATE_FORMAT="format_datetime";
    public static final String PRESTO_DATE_PARSE="date_parse";
    public static final String PRESTO_CAST="cast";
    public static final String PRESTO_AS_VARCHAR=" as varchar";
    public static final String QUARTER="quarter";
    public static final String CONCAT="CONCAT";
    public static final String YEAR="year";
    public static final String WEEK="week";
    //同比、环比对应的当前值
    public static final String SQL_DISTINCT=" distinct ";
    public static final String SQL_FORMAT_FUFFIX="_format";
    public static final String SQL_EQUALS=" = ";
    public static final String YYYYMMDD="'%Y%m%d'";
    public static final String PRESTO_YYYYMMDD="YYYYMMDD";
    public static final String PRESTO_YYYYMM="YYYYMM";
    public static final String PRESTO_YYYY="YYYY";
    public static final String PRESTO_YYYYMMDDHHSS="%Y-%m-%d %H:%i:%s";
    public static final String YYYYMM="'%Y%m'";
    public static final String YYYY="'%Y'";
    public static final String YYYYWW="'%Y%V'";
    public static final String YYYYQQ="'%Y%Q'";
    public static final String AS=" as ";
    public static final String SPACE=" ";
    public static final String COMMA=" ,";
    public static final String INDEX_TOTAL_TIP="当前数据仅为{}条数据的总计";
    public static final String SEPARATORS_CHAR = "/";

    /**
     * 最大递归深度
     */
    public static final int RECURSION_DEPTH = 50;

    /**
     * csv文件path type
     */
    public static final String CSV_PATH_MINIIO = "mini_io";
    public static final String CSV_PATH_MINIO_ORIGINAL = "minio_original";
    public static final String CSV_PATH_FASTDFS = "fast_dfs";

    public static final String IMAGE_ROOT_PATH = "/data/bi/dataScreen/";

    private static final String QUERY_SOURCE_SHARE = "SHARE";


}
