package com.bestpay.bigdata.bi.common.response;

import com.bestpay.bigdata.bi.common.dto.common.TableFontStyleRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.WatermarkSetting;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.CardStyleConfig;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.CommonCoordinateAxisConfigRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.GraphicDisplayDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.report.ReportSimpleColumn;
import com.bestpay.bigdata.bi.common.dto.report.TableConfiguration;
import com.bestpay.bigdata.bi.common.dto.report.TotalDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ConditionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ContrastComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.DimensionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.FilterComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.KeywordComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.OrderComponentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 报表详情VO
 * <AUTHOR>
 */
@Data
@ApiModel("报表详情VO")
public class ReportDetailVO{
  @ApiModelProperty(value = "主键ID")
  private Long id;

  @ApiModelProperty(value = "选择组织编码",required = true)
  @NotNull(message = "选择组织编码不能为空！")
  private String orgSelected;

  @ApiModelProperty(value = "归属组织编码",required = true)
  @NotNull(message = "归属组织编码不能为空！")
  private String orgCode;

  @ApiModelProperty(value = "归属目录ID",required = true)
  @NotNull(message = "归属目录ID不能为空！")
  private Long dirId;

  @ApiModelProperty(value = "归属目录名称")
  private String dirName;

  @ApiModelProperty(value = "报表名称",required = true)
  @NotNull(message = "报表名称不能为空！")
  private String reportName;

  @ApiModelProperty(value = "组织权限",required = true)
  private String orgAuth;

  @ApiModelProperty(value = "报表说明",required = true)
  @NotNull(message = "报表说明不能为空！")
  private String reportDesc;

  @ApiModelProperty(value = "数据权限",required = true)
  private String dataAuth;

  @ApiModelProperty(value = "排序控件")
  private List<OrderComponentDTO> orderColumnList;

  @ApiModelProperty(value = "计算字段")
  private List<ComputeComponentPropertyDTO> computeColumnList;

  @ApiModelProperty(value = "报表结构, 描述顺序信息 JSON List ReportSimpleColumn")
  private List<ReportSimpleColumn> reportStructureList;

  @ApiModelProperty(value = "表格配置")
  private TableConfiguration tableConfigurationObj;

  @ApiModelProperty(value = "对比字段")
  private List<ContrastComponentPropertyDTO> contrastColumnList;

  @ApiModelProperty(value = "责任人（中文名称）",required = true)
  private String ownerNameCh;

  @ApiModelProperty(value = "责任人登录名",required = true)
  private String ownerName;

  @ApiModelProperty(value = "责任人邮箱",required = true)
  private String email;

  @ApiModelProperty(value = "报表类型（0：明细表  1：聚合表 ）",required = true)
  @NotNull(message = "报表类型不能为空！")
  private Integer reportType;

  @ApiModelProperty(value = "数据是否包含敏感信息: 0: false 1:true",required = true)
  private Integer fileContainSensitiveInfo;

  @ApiModelProperty(value = "敏感字段")
  private String sensitiveFields;

  @ApiModelProperty(value = "状态（0：上线  1：待发布  9：删除）",required = true)
  private Integer statusCode;

  @ApiModelProperty(value = "是否开启上卷下钻 0关闭 1开启, 默认值为0")
  private Integer rollupDown;

  @ApiModelProperty(value = "收藏Id")
  private Integer collectionId;

  @ApiModelProperty(value = "图表类型；0-列表；1-柱形图；2-折线图；3-环形图；",required = true)
  private Integer chartType;

  @ApiModelProperty(value = "图表属性",required = true)
  private Object chartFieldObj;

  @ApiModelProperty(value = "是否显示指标总计")
  private TotalDTO showIndexTotalObj;

  @ApiModelProperty(value = "查询总行数")
  private String maxRows;

  @ApiModelProperty(value = "创建时间")
  private Date createdAt;

  @ApiModelProperty(value = "创建人")
  private String createdBy;

  @ApiModelProperty(value = "更新时间")
  private Date updatedAt;

  @ApiModelProperty(value = "更新人")
  private String updatedBy;

  @ApiModelProperty(value = "创建人组织机构代码",required = true)
  private String createdByOrg;

  @ApiModelProperty(value = "展示字段/维度",required = true)
  private List<DimensionComponentPropertyDTO> showColumnList;

  @ApiModelProperty(value = "指标控件",required = true)
  private List<IndexComponentPropertyDTO> indexColumnList;

  @ApiModelProperty(value = "过滤控件",required = true)
  private List<FilterComponentPropertyDTO> filterColumnList;

  @ApiModelProperty(value = "查询控件",required = true)
  private List<ConditionComponentPropertyDTO> conditionList;

  @ApiModelProperty(value = "关键字",required = true)
  private List<KeywordComponentPropertyDTO> keywordList;

  @ApiModelProperty(value = "数据集信息",required = true)
  private List<DatasetInfo> datasetInfoList;

  @ApiModelProperty(value = "归属组织名称",required = true)
  private String orgName;

  @ApiModelProperty(value = "表格字体样式")
  private TableFontStyleRequest tableFontStyle;

  @ApiModelProperty(value = "仪表板报表坐标轴配置")
  private CommonCoordinateAxisConfigRequest coordinateAxisConfigRequest;

  @ApiModelProperty(value = "来源烈性")
  private String resourceType;

  @ApiModelProperty(value = "组件类型")
  private String componentType;

  @ApiModelProperty(value = "报表水印设置")
  private WatermarkSetting watermarkSetting;

  @ApiModelProperty(value = "卡片配置")
  private CardStyleConfig cardStyleConfigRes;

  @ApiModelProperty(value = "图表配置配置")
  private GraphicDisplayDTO graphicDisplayDTO;

  public void setCoordinateAxisConfigRequest(CommonCoordinateAxisConfigRequest request) {
    this.coordinateAxisConfigRequest = request;
  }
}
