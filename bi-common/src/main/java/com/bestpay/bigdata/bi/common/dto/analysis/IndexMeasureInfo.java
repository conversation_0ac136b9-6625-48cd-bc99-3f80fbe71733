package com.bestpay.bigdata.bi.common.dto.analysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * (TIndexMeasure)实体类
 *
 * <AUTHOR>
 * @since 2021-08-16 14:01:26
 */
@Data
@ApiModel("度量集合")
public class IndexMeasureInfo implements Serializable {

    @ApiModelProperty(value = "创建时间")
    private Long id;
    /**
     * 指标Id
     */
    @ApiModelProperty(value = "指标Id")
    private Long indexId;
    /**
     * 度量名称
     */
    @ApiModelProperty(value = "度量名称")
    private String name;
    /**
     * 计算函数
     */
    @ApiModelProperty(value = "计算函数编码")
    private Integer functionCode;
    /**
     * 计算函数名称
     */
    @ApiModelProperty(value = "计算函数名称")
    private String functionName;
    /**
     * 计算类型
     */
    @ApiModelProperty(value = "计算类型")
    private String calculateType;
    /**
     * 计算字段
     */
    @ApiModelProperty(value = "计算字段")
    private String calulateColumn;
    /**
     * 返回类型
     */
    @ApiModelProperty(value = "返回类型编码")
    private Integer dataTypeCode;
    /**
     * 返回类型名称
     */
    @ApiModelProperty(value = "返回类型名称")
    private String dataTypeName;
    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型编码")
    private Integer operTypeCode;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;


}
