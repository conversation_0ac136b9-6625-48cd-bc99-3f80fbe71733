package com.bestpay.bigdata.bi.common.enums;

/**
 * <AUTHOR>
 * @date 2024-08-30
 */
public enum DataScreenVersionTypeEnum {
  PUBLISHED("published", "已发布"),
  DRAFT("draft", "草稿"),


  /**
   *
   */
  OFFLINE("offline", "下线"),
  DELETE("delete", "删除");

  private final String code;
  private final String message;

  DataScreenVersionTypeEnum(String code, String message) {
    this.code = code;
    this.message = message;
  }

  public String getCode() {
    return code;
  }

  public String getMessage() {
    return message;
  }
}
