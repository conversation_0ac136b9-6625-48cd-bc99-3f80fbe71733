package com.bestpay.bigdata.bi.common.error;

public enum SubscribeErrorCode implements ErrorCodeSupplier {
    NOT_SUPPORT_SUB_TYPE("00001", ErrorType.BUSINESS_ERROR),


    DING_TALK_SEND_ERROR("30001", ErrorType.EXTERNAL_ERROR),
    ACCESS_TOKEN_ERROR("30002", ErrorType.EXTERNAL_ERROR),

    /**
     * 订阅任务执行时参数不能为空
     */
    SUBSCRIBE_PARAM_ERROR("30003", ErrorType.BUSINESS_ERROR),

    ;

    private static final String PREFIX = "SUBSCRIBE_";

    private final ErrorCode errorCode;

    SubscribeErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
