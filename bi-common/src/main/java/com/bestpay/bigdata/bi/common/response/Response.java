package com.bestpay.bigdata.bi.common.response;

import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.util.LogTraceIdGenerator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.slf4j.MDC;

/**
 * 请求响应对象
 * <AUTHOR>
 * @date 2021/3/11 14:56
 **/
@Data
@ApiModel(value = "返回类")
public class Response<T> implements Serializable {

  @ApiModelProperty(value = "是否成功")
  private boolean success;

  @ApiModelProperty(value = "错误编码")
  private String code;

  @ApiModelProperty(value = "错误信息")
  private String message;

  @ApiModelProperty(value = "错误信息")
  private String errorMessage;

  @ApiModelProperty(value = "数据")
  private T data;

  private String traceId;
  private String requestUrl;
  private boolean sensitive = false;

  public Response() {
  }

  public Response(boolean success) {
    this.success = success;
  }

  public Response(boolean success, CodeEnum codeEnum) {
    this.success = success;
    this.code = codeEnum.code();
    this.message = codeEnum.message();
    this.traceId= MDC.get(LogTraceIdGenerator.TRACE_ID);
  }

  public Response(boolean success, String code, String message, T data) {
    this.success = success;
    this.code = code;
    this.message = message;
    this.data = data;
    this.traceId= MDC.get(LogTraceIdGenerator.TRACE_ID);
  }

  public Response(boolean sensitive, boolean success, String code, String message, T data) {
    this.success = success;
    this.code = code;
    this.message = message;
    this.data = data;
    this.traceId= MDC.get(LogTraceIdGenerator.TRACE_ID);
    this.sensitive=sensitive;
  }

  public Response(boolean success, String code, String message, String errorMessage, T data) {
    this.success = success;
    this.code = code;
    this.message = message;
    this.data = data;
    this.errorMessage=errorMessage;
    this.traceId= MDC.get(LogTraceIdGenerator.TRACE_ID);
  }

  public static <T> Response<T> ok() {
    return ok(false, null, null, null);
  }

  public static <T> Response<T> ok(T data) {
    return ok(false, null, null, data);
  }

  public static <T> Response<T> ok(boolean sensitive, T data) {
    return ok(sensitive, null, null, data);
  }

  public static <T> Response<T> ok(CodeEnum codeEnum) {
    return ok(codeEnum, null);
  }

  public static <T> Response<T> ok(CodeEnum codeEnum, T data) {
    return ok(false, codeEnum.code(), codeEnum.message(), data);
  }

  public static <T> Response<T> ok(boolean sensitive, String code, String message, T data) {
    return new Response<>(sensitive, true, code, message, data);
  }

  /**
   * 请求错误
   * @param codeEnum
   * @param <T>
   * @return
   */
  public static <T> Response<T> error(CodeEnum codeEnum) {
    return new Response<>(false, codeEnum);
  }

  public static <T> Response<T> error(String code, String message) {
    return new Response<>(false,false, code, message, null);
  }

  public static <T> Response<T> error(String code, String message, String errorMessage) {
    return new Response<>(false, code, message, errorMessage,null);
  }
}
