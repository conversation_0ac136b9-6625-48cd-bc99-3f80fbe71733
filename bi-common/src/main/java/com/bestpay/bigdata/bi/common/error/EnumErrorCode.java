package com.bestpay.bigdata.bi.common.error;

public enum EnumErrorCode implements ErrorCodeSupplier {

    ENUM_CODE_ERROR("00001", ErrorType.BUSINESS_ERROR),
    ;

    private static final String PREFIX = "ENUM_";

    private final ErrorCode errorCode;

    EnumErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
