package com.bestpay.bigdata.bi.common.dto.report;

import java.util.UUID;

/**
 * @Author: wybStart
 * @Date: 2025/6/5  16:23
 * @Description:
 */
public class ReportUuidGenerateUtil {

    public static final String REPORT_CONFIG_UUID_PREFIX = "report_config_";

    public static final String REPORT_COMPUTE_UUID_PREFIX = "report_compute_";

    public static final String REPORT_MEASURE_UUID_PREFIX = "report_measure_";

    /**
     * 生成报告配置的唯一标识符（UUID）
     *
     * 此方法用于创建一个唯一的字符串标识符，用于报告配置它通过将一个预定义的前缀与一个随机生成的UUID结合来实现
     * 这种做法确保了在系统中每个报告配置都能拥有一个唯一的身份标识，便于管理和追踪
     *
     * @return String 返回报告配置的UUID，格式为"report_config_"加上一个随机UUID
     */
    public static String generateReportConfigUuid() {
        return REPORT_CONFIG_UUID_PREFIX + UUID.randomUUID();
    }


    /**
     * 生成报告计算的唯一标识符（UUID）
     *
     * 该方法用于创建一个唯一的字符串标识符，用于报告计算过程中使用此标识符可以确保报告的唯一性和可追踪性
     *
     * @return String 返回一个以特定前缀开始的UUID，格式为“前缀+随机UUID”
     */
    public static String generateReportComputeUuid() {
        return REPORT_COMPUTE_UUID_PREFIX + UUID.randomUUID();
    }


    public static String generateReportMeasureUuid() {
        return REPORT_MEASURE_UUID_PREFIX + UUID.randomUUID();
    }
}
