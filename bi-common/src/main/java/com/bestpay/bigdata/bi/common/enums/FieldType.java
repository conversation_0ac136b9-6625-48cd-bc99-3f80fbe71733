package com.bestpay.bigdata.bi.common.enums;

import java.util.Arrays;
import java.util.Optional;

/**
 * 字段显示类型对应前端 showTypeName
 * todo 类名称可以改为 showType
 */
public enum FieldType {
    //字符
    CHARACTER,
    //数值
    DECIMAL,
    //日期
    DATETIME;

    public final static String allValue = "CHARACTER,DECIMAL,DATETIME";

    public static boolean isInclude(String name) {
        for (FieldType item : FieldType.values()) {
            if (name.contains(item.name())) {
                return true;
            }
        }
        return false;
    }

    public static Optional<FieldType> fromString(String name) {
        for (FieldType item : FieldType.values()) {
            if (item.name().equalsIgnoreCase(name)) {
                return Optional.of(item);
            }
        }
        return Optional.empty();
    }

    public static FieldType getFieldType(String code) {
        return Arrays.stream(values()).filter(item -> item.name().equals(code)).findFirst().orElse(null);
    }
}
