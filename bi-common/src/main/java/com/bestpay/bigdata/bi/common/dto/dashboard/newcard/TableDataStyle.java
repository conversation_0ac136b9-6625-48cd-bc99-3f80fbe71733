package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author:gaodingsong
 * @description: 表格数据样式
 * @createTime:2024/5/10 18:14
 * @version:1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "表格数据样式")
public class TableDataStyle extends CustomerStyleBaseRequest {


    @ApiModelProperty(value = "是否加粗")
    private Boolean bold;

    @ApiModelProperty(value = "斜体")
    private Boolean italic;

    @ApiModelProperty(value = "下划线")
    private Boolean underline;

    @ApiModelProperty(value = "删除线")
    private Boolean strikethrough;
}
