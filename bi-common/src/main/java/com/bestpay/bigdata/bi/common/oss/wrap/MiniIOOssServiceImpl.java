package com.bestpay.bigdata.bi.common.oss.wrap;

import cn.hutool.core.lang.Assert;
import com.bestpay.bigdata.bi.common.enums.FileSystemTypeEnum;
import com.bestpay.bigdata.bi.common.oss.AbstractOssService;
import com.bestpay.bigdata.bi.common.oss.OssContext;
import com.bestpay.bigdata.bi.common.util.MinioUtil;
import com.bestpay.drip.oss.proxy.common.BaseResult;
import com.bestpay.drip.oss.proxy.common.Constants;
import java.io.InputStream;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ClassName: MiniIOOssServiceImpl
 * Package: com.bestpay.bigdata.bi.common.oss
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/11/29 14:43
 * @Version 1.0
 */
@Slf4j
@Component
public class MiniIOOssServiceImpl extends AbstractOssService
{
    @Autowired
    private MinioUtil minioUtil;

    @Override
    public String upload(OssContext ossContext)
    {
        if (!ossContext.getFileSystemType().equals(FileSystemTypeEnum.MINIIO)) {
            //todo throw exception
        }
        MiniIOOssContext miniIOUploadContext = (MiniIOOssContext) ossContext;

        Integer webURLMode = miniIOUploadContext.getWebURLMode();
        Integer ttlDays = miniIOUploadContext.getTtlDays();
        InputStream inputStream = miniIOUploadContext.getInputStream();
        String fileExtName = miniIOUploadContext.getFileExtName();
        Assert.notNull(inputStream, "upload miniIO inputStream can not be null");
        Assert.notNull(fileExtName, "upload miniIO fileExtName can not be null");

        if (Objects.isNull(webURLMode)) {
            webURLMode = Constants.NEVER_ACCESSED_BY_URL;
            // default upload private bucket
            miniIOUploadContext.setWebURLMode(Constants.NEVER_ACCESSED_BY_URL);
        }

        String result = null;
        // public bucket
        if (Constants.ACCESSED_BY_UNSIGNED_URL == webURLMode) {
            log.info("miniIo public bucket");
            if (ttlDays == null) {
                result = minioUtil.uploadPublicMinio(inputStream, fileExtName);
            } else {
                result = minioUtil.uploadPublicMinio(inputStream, ttlDays, fileExtName);
            }
        } else {
            log.info("miniIo private bucket");
            // private bucket
            if (ttlDays == null) {
                result = minioUtil.uploadPrivateMinio(inputStream, fileExtName);
            } else {
                result = minioUtil.uploadPrivateMinio(inputStream, ttlDays, fileExtName);
            }
        }
        return result;
    }

    @Override
    public byte[] download(OssContext ossContext)
    {
        if (!ossContext.getFileSystemType().equals(FileSystemTypeEnum.MINIIO)) {
            //todo throw exception
        }
        MiniIOOssContext miniIOContext = (MiniIOOssContext) ossContext;
        String miniIoPath = miniIOContext.getMiniIoPath();
        Assert.notEmpty(miniIoPath, "miniIoPath can not be empty");

        return minioUtil.downloadMinio(miniIoPath);
    }

    @Override
    public FileSystemTypeEnum getFileSystemType()
    {
        return FileSystemTypeEnum.MINIIO;
    }

    @Override
    public String parseHttpUrl(OssContext ossContext)
    {
        if (!ossContext.getFileSystemType().equals(FileSystemTypeEnum.MINIIO)) {
            //todo throw exception
        }
        MiniIOOssContext miniIOContext = (MiniIOOssContext) ossContext;
        String miniIoPath = miniIOContext.getMiniIoPath();
        Assert.notEmpty(miniIoPath, "miniIoPath can not be empty");

        log.info(" parseHttpUrl  miniIoPath : {}", miniIoPath);

        String url = minioUtil.generatePreSignUrl(miniIoPath); //application/zip
        log.info("parseHttpUrl miniIO : {}", url);
        return url;
    }


    @Override
    public void delete(OssContext ossContext)
    {
        if (!ossContext.getFileSystemType().equals(FileSystemTypeEnum.MINIIO)) {
            //todo throw exception
        }
        MiniIOOssContext miniIOContext = (MiniIOOssContext) ossContext;
        String miniIoPath = miniIOContext.getMiniIoPath();
        Assert.notEmpty(miniIoPath, "miniIoPath can not be empty");

        minioUtil.delete(miniIoPath);
    }


    public boolean judgeResourceExists(OssContext ossContext) {
        try {
            if (!ossContext.getFileSystemType().equals(FileSystemTypeEnum.MINIIO)) {
                //todo throw exception
            }
            MiniIOOssContext miniIOContext = (MiniIOOssContext) ossContext;
            String miniIoPath = miniIOContext.getMiniIoPath();
            Assert.notEmpty(miniIoPath, "miniIoPath can not be empty");

            BaseResult<Boolean> booleanBaseResult = minioUtil.existsMinio(miniIoPath);

            if (booleanBaseResult.isSuccess()) {
                return booleanBaseResult.getData();
            } else {
                return false;
            }
        }
        catch (Exception e) {
            log.error("miniIO judge resource exists happen error, ossContext : {}", ossContext, e);
            return false;
        }
    }
}
