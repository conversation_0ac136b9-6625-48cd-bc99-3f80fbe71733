package com.bestpay.bigdata.bi.common.dto.warn;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class WarnConditionDTO {

    /**报表中每个元素的唯一标识*/
    private String uuid;

    private String configUuid;

    /** 'field' / 'contrast' 标识维度 'index' / 'overlayIndex' 标识指标 */
    private String reportField;

    /**日期类型*/
    private String dateType;

    /**比较 @ScopeFilterTypeEnum*/
    private String scopeFilterType;

    private String stringValue;

    private List<Object> values;

    private String showTypeName;

    /**
     * 日期类型维度统计类型：1-日；2-月；3-年；4-周；5-季度；6-年月日 时分秒
     */
    private Integer dateGroupType;

    private String name;

    private Long datePickerId;
}
