package com.bestpay.bigdata.bi.common.entity;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: den<PERSON><PERSON><PERSON>
 * @CreateDate: 2022/3/3 15:59
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SftpInfo implements Serializable {

  /**
   * SFTP 登录用户名
   */
  private String username;
  /**
   * SFTP 登录密码
   */
  private String password;
  /**
   * 私钥
   */
  private String privateKey;
  /**
   * SFTP 服务器地址IP地址
   */
  private String host;
  /**
   * SFTP 端口
   */
  private int port;
  /**
   * sftp base path
   */
  private String sftpBasePath;

  /**
   * 构造基于密码认证的sftp对象
   */
  public SftpInfo(String username, String password, String host, int port) {
    this.username = username;
    this.password = password;
    this.host = host;
    this.port = port;
  }

  /**
   * 构造基于秘钥认证的sftp对象
   */
  public SftpInfo(String username, String host, int port, String privateKey) {
    this.username = username;
    this.host = host;
    this.port = port;
    this.privateKey = privateKey;
  }

}
