package com.bestpay.bigdata.bi.common.bean.aiapi;

import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName AccountInfo
 * @description 智加账户信息对象
 * @date 2025/7/30
 */
@Data
@Accessors(chain = true)
public class AccountInfo implements Serializable {

  private static final Long serialVersionUID = 8988326797385953724L;

  /**
   * 账户ID
   */
  private String uuid;

  /**
   * 账户名称
   */
  private String account;

  /**
   * 账户名称
   */
  private String username;

  /**
   * 邮箱
   */
  private String email;

  /**
   * 手机号
   */
  private String mobile;

  /**
   * 状态 1:正常 2:冻结 3:注销
   */
  private String status;

  /**
   * 组织
   */
  private QueryCodeAndNameResultVO org;

  /**
   * 省公司
   */
  private QueryCodeAndNameResultVO province;

  /**
   * 城市
   */
  private QueryCodeAndNameResultVO city;

}
