package com.bestpay.bigdata.bi.common.config;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.extension.Activate;
import com.alibaba.dubbo.rpc.Filter;
import com.alibaba.dubbo.rpc.Invocation;
import com.alibaba.dubbo.rpc.Invoker;
import com.alibaba.dubbo.rpc.Result;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.dubbo.rpc.RpcException;
import com.bestpay.bigdata.bi.common.util.LogTraceIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 * 用于在dubbo接口调用时添加日志追踪所用的trace_id
 * <AUTHOR>
 * @date 2019/8/10 14:41
 **/
@Activate(group = {Constants.CONSUMER,Constants.PROVIDER})
public class LogTraceIdFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(LogTraceIdFilter.class);

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        RpcContext rpcContext = RpcContext.getContext();
        if (rpcContext.isProviderSide()) {
            // 如果是提供方，则从rpcContext中获取trace_id，并设置进MDC
            String traceId = rpcContext.getAttachment(LogTraceIdGenerator.TRACE_ID);
            MDC.put(LogTraceIdGenerator.TRACE_ID, traceId);
        }

        if (rpcContext.isConsumerSide()) {
            // 如果是消费方，则从MDC中获取trace_id，并在调用dubbo接口的时候设置进rpcContext
            String traceId = MDC.get(LogTraceIdGenerator.TRACE_ID);
            rpcContext.setAttachment(LogTraceIdGenerator.TRACE_ID, traceId);
        }

        try {
            long start = System.currentTimeMillis();
            Result invoke = invoker.invoke(invocation);
            long end = System.currentTimeMillis();
            logger.info("dubbo rpc request {}.{} api 耗时 {}ms", invoker.getInterface().getName(), invocation.getMethodName(), (end - start));
            return invoke;
        } finally {
            // provider清除MDC中的traceId
            if (rpcContext.isProviderSide()) {
                MDC.remove(LogTraceIdGenerator.TRACE_ID);
            }
        }
    }

}
