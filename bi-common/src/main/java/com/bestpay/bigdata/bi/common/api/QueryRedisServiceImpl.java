package com.bestpay.bigdata.bi.common.api;

import com.bestpay.bigdata.bi.common.bean.CachedQueryResult;
import com.bestpay.bigdata.bi.common.dto.FileDownloadResult;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.enums.Status;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.common.Constant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@Slf4j
@Component
public class QueryRedisServiceImpl implements QueryRedisService {

    @Autowired
    private RedisService redisService;

    @Override
    public Integer getQueryStatus(String queryId) throws BusinessException {
        Optional<Integer> statusOptional = redisService.getObj(Constant.REDIS_BIGDATA_BI_QUERY_STATUS + queryId);
        if (!statusOptional.isPresent()) {
            throw new BusinessException("The queryId [" + queryId + "] can not get status!");
        }
        return statusOptional.get();
    }

    @Override
    public Integer getQueryCount(String requestUser) {
        Optional<Integer> statusOptional = redisService.getObj(Constant.REDIS_TASK_CONCURRENT_COUNT + requestUser);
        return statusOptional.orElse(null);
    }

    @Override
    public FileDownloadResult getRemoteFilePath(String queryId) {
        Optional<FileDownloadResult> remoteFilePath = redisService.getObj(Constant.REDIS_BIGDATA_BI_DOWNLOAD_PATH + queryId);
        return remoteFilePath.orElse(null);
    }


    @Override
    public String getQueryProgress(String queryId) {
        String result = "0";
        Optional<String> processOptional = redisService.getHashObj(Constant.REDIS_BIGDATA_BI_QUERY_PROGRESS_INFO_MAP_KEY, queryId);
        return processOptional.orElse(result);
    }

    @Override
    public QueryContext getQueryResult(String queryId) throws BusinessException {
        Optional<QueryContext> optionalQ = redisService.getObj(Constant.REDIS_BIGDATA_BI_QUERY_CONTEXT + "." + queryId);
        if (!optionalQ.isPresent()) {
            throw new BusinessException("The queryId [" + queryId + "] can not get result!");
        }

        QueryContext queryContext = optionalQ.get();
        String resultCacheKey = queryContext.getResultCacheKey();
        if (queryContext.getStatus().equals(Status.QUERY_SUCCESS) && StringUtils.isNotBlank(resultCacheKey)) {
            Optional<CachedQueryResult> queryResult = redisService.getObj(resultCacheKey);
            CachedQueryResult cachedQueryResult = queryResult.get();
            if (cachedQueryResult != null) {
                log.info("queryId:{} get CachedQueryResult success, set it to QueryContext",queryId);
                queryContext.setResults(cachedQueryResult.getResults());
                queryContext.setResultPath(cachedQueryResult.getResultPath());
            } else {
                log.warn("the queryContext queryId:{} status is success,but queryResult is null", queryId);
            }

        }

        return queryContext;
    }

    @Override
    public QueryContext getQueryResultNoData(String queryId) throws BusinessException {
        Optional<QueryContext> optionalQ = redisService.getObj(Constant.REDIS_BIGDATA_BI_QUERY_CONTEXT + "." + queryId);
        if (!optionalQ.isPresent()) {
            throw new BusinessException("The queryId [" + queryId + "] can not get result!");
        }

        QueryContext queryContext = optionalQ.get();
        queryContext.setResults(null);
        return queryContext;
    }
}
