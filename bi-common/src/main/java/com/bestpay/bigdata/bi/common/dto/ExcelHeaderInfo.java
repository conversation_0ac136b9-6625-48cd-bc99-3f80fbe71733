package com.bestpay.bigdata.bi.common.dto;

import com.bestpay.bigdata.bi.common.entity.ColumnName;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Excel头信息
 * @Author: den<PERSON><PERSON><PERSON>
 * @CreateDate: 2022/3/7 13:51
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelHeaderInfo implements Serializable {

  /**
   * 报表名称
   */
  private String reportName;
  /**
   * 责任人
   */
  private String owner;
  /**
   * 数据日期
   */
  private String dataDate;
  /**
   * 数据总数
   */
  private Long rowCount;
  /**
   * 报表说明
   */
  private String description;
  /**
   * 列名信息
   */
  private List<ColumnName> columnNameMaps;
}
