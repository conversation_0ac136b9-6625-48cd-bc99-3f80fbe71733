package com.bestpay.bigdata.bi.common.dto;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * ClassName: DataSourceKey
 * Package: com.bestpay.bigdata.bi.backend.config
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/8/1 10:27
 * @Version 1.0
 */
@Slf4j
@Builder
@Data
public class DataSourceKey implements Serializable {

    private String dataSource;
    private String engine;
    private String requestSystem;
    private String routerGroup;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DataSourceKey that = (DataSourceKey) o;
        return Objects.equals(dataSource, that.dataSource)
                && Objects.equals(engine, that.engine)
                && Objects.equals(requestSystem, that.requestSystem)
                && Objects.equals(routerGroup, that.routerGroup);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dataSource, engine, requestSystem, routerGroup);
    }

    @Override
    public String toString() {
        return "DataSourceKey{" +
                "dataSource='" + dataSource + '\'' +
                ", engine='" + engine + '\'' +
                ", requestSystem='" + requestSystem + '\'' +
                ", routerGroup='" + routerGroup + '\'' +
                '}';
    }

    public boolean contains(DataSourceKey that) {
        return contains(that.getEngine(), that.getDataSource(), that.getRouterGroup(), that.getRequestSystem());
    }

    public boolean contains(String engine, String dataSource, String routerGroup, String requestSystem) {
        boolean result = false;
        log.info("to string : {}", this);
        if (StringUtils.isEmpty(routerGroup)) {
            routerGroup = "adhoc"; // special process
        }
        if (this.engine.equals(engine) && this.dataSource.equals(dataSource)
                && this.routerGroup.equals(routerGroup)) {
            List<String> requestSystemList = Arrays.stream(this.requestSystem.split(","))
                    .map(String::toUpperCase).collect(Collectors.toList());
            requestSystemList.add(""); // compile history special scene
            log.info("requestSystemList : {}", requestSystemList);
            if (requestSystemList.contains(requestSystem.toUpperCase())) {
                result = true;
            }
        }
        return result;
    }
}
