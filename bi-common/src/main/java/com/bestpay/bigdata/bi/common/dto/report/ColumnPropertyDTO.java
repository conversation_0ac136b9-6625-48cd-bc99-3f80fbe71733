package com.bestpay.bigdata.bi.common.dto.report;

import com.bestpay.bigdata.bi.common.enums.ScopeFilterTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ColumnPropertyDTO{

  @ApiModelProperty(value = "维度ID")
  private Long id;

  /**目前是 层级的 uuid */
  private String parentId;

  /**
   * 唯一标识
   */
  private String uuid;

  /**
   * 报表配置的唯一标识
   */
  private String configUuid;

  /**
   * 字段名称
   */
  @ApiModelProperty(value = "中文-字段名称",notes = "入参")
  private String name;

  @ApiModelProperty(value = "中文-字段原名",notes = "入参")
  private String originalName;

  @ApiModelProperty(value = "英文-字段原名",notes = "入参")
  private String originEnName;

  @ApiModelProperty(value = "底层表字段类型",notes = "入参")
  private String typeName;

  @ApiModelProperty(value = "范围过滤类型",required = true,allowableValues = ScopeFilterTypeEnum.ALL_VALUE)
  private String scopeFilterType;

  @ApiModelProperty(value = "字段昵称")
  private String nickName;
  /**
   * 字段英文名称
   */
  @ApiModelProperty(value = "字段英文名称")
  private String enName;
  /**
   * 指标名称
   */
  @ApiModelProperty(value = "指标名称")
  private String indexName;
  /**
   * 字段显示类型名称
   */
  @ApiModelProperty(value = "字段显示类型名称")
  private String showTypeName;

  /**
   * 字段显示类型名称
   */
  @ApiModelProperty(value = "计算逻辑")
  private String calculateLogic;
  /**
   * 聚合方式
   */
  @ApiModelProperty(value = "聚合方式")
  private String polymerization;
  /**
   * 计算字段计算逻辑
   */
  @ApiModelProperty(value = "计算字段计算逻辑")
  private String fun;

  /**
   * 1.维度时：为字段名称
   * 2.指标时：需带上计算逻辑
   */
  @ApiModelProperty(value = "字段英文名称",required = true)
  private String fieldName;

  /**
   * 报表字段：指标-index；字段-field；叠加指标-overlayIndex；
   */
  @ApiModelProperty(value = "类型名称")
  private String reportField;
  /**
   * 高级计算
   */
  @ApiModelProperty(value = "高级计算")
  private AdvancedComputing advancedComputing;
  /**
   * 日期类型维度统计类型：1-日；2-月；3-年；4-周；5-季度
   */
  @ApiModelProperty(value = "日期类型维度统计类型")
  private Integer dateGroupType;

  /**日期截取展示枚举 @DateShowFormatEnum */
  private Integer dateShowFormat;

  @ApiModelProperty(value = "是否统计小计")
  private Boolean showSubtotal=false;

  /**
   * 数据格式中的度量单位据类型
   */
  @ApiModelProperty(value = "数据格式中的度量单位数据类型")
  private String dataType;
  /**
   * 数据格式中的度量单位小数位数
   */
  @ApiModelProperty(value = "数据格式中的度量单位小数位数")
  private Integer decimaCarry;
  /**
   * 数据格式中的度量单位
   */
  @ApiModelProperty(value = "数据格式中的度量单位")
  private Integer unit;

  @ApiModelProperty(value = "比较符用到的常量值，当fieldType是SELECT-INPUT字符时，这个字段有值，逗号隔开")
  private String stringValue;

  /**
   * 数据格式中的是否显示千分位
   */
  @ApiModelProperty(value = "数据格式中的是否显示千分位")
  private Boolean showThousandth;

  @ApiModelProperty(value = "比较符用到的常量值",required = true)
  private List<Object> values;

  /**区间 1动态时间 2固定时间*/
  private String intervalType;

  /** 如果选择了动态时间,这里为动态时间 见FilterDateValueEnum 定义 */
  private String dynamicDate;

  @ApiModelProperty(value = "筛选默认值")
  private ReportScreeningConditionDTO screeningCondition;


  @ApiModelProperty(value = "是否是计算字段")
  private boolean isComputeField;

  /**
   * 计算字段解析后的逻辑
   */
  private String computeFieldLogic;

  private List<HighFunction> highFunctions;

  // Report Field Enum
  private String fieldType;

  private List<String> valueList;

  private Long datePickerId;

  private Boolean isHide;

  private Boolean isFold;

  /**
   * 是否隐藏 数据格式中的度量单位, 默认展示单位
   */
  private Boolean isShowUnit;
}
