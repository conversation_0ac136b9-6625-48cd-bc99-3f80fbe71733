package com.bestpay.bigdata.bi.common.entity;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: den<PERSON><PERSON><PERSON>
 * @CreateDate: 2021/12/21 10:55
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrgName implements Serializable {

  /**
   * 组织编码
   */
  private String code;
  /**
   * 组织名称
   */
  private String name;
}
