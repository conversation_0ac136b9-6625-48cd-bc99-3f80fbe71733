package com.bestpay.bigdata.bi.common.dto.dashboard;

import cn.hutool.json.JSONUtil;
import lombok.Data;

/**
 * @Author：Song
 * @Date：2024/12/18 15:57
 * @Desc: 北极星仪表板特殊配置
 */
@Data
public class NorthStarDashboardSpecialConf {


    /**
     * 调用AI接口配置
     */
    private InsightAiConfDTO insightAiConf;

    private String dashboardIdList;

    // 特殊仪表板缓存时间
    private String specialDashboardCacheTimeConf ;


    public static NorthStarDashboardSpecialConf jsonToBean(String json){
        return JSONUtil.toBean(json, NorthStarDashboardSpecialConf.class);
    }

}
