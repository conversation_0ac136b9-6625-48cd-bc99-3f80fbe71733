package com.bestpay.bigdata.bi.common.bean.aiapi;

import com.bestpay.bigdata.bi.common.bean.PageVo;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName NewUserInfoRequest
 * @description 新智加查询用户信息列表入参
 * @date 2025/7/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NewUserInfoRequest extends PageVo {
  private String accountName;

  private List<Integer> status;

  public static NewUserInfoRequest buildDefaultValue() {
    NewUserInfoRequest request = new NewUserInfoRequest();
    request.setPageSize(100);
    request.setPageNum(1);
    return request;
  }
}