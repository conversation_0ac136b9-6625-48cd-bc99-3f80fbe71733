package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:gaodingsong
 * @description: 自定义样式基础信息
 * @createTime:2024/5/10 18:17
 * @version:1.0
 */
@Data
@ApiModel("自定义样式基础信息")
public class CustomerStyleBaseRequest {

    @ApiModelProperty(value = "填充色")
    private String fillColor;

    @ApiModelProperty(value = "字体大小")
    private Integer fontSize;

    @ApiModelProperty(value = "字体填充色")
    private String fontFillColor;


}
