package com.bestpay.bigdata.bi.common.dto.report;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("基础格式")
public class BasicFormat {

    @ApiModelProperty(value = "是否开启分页")
    private Boolean pageEnabled;

    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "是否合并单元格")
    private Boolean isMergeCell ;

    @ApiModelProperty(value = "对齐方式")
    private String alignmentType;

    @ApiModelProperty(value = "是否自动换行")
    private Boolean isWrap;

    @ApiModelProperty(value = "是否排除总计小计")
    private Boolean excludeTotalAndSubTotal;

    @ApiModelProperty(value = "是否排序列号")
    private Boolean showSequenceColumn;

    @ApiModelProperty(value = "列宽类型")
    private String columnWidthConfigGroup;

    @ApiModelProperty(value = "列宽详细信息")
    private List<CustomColumnWidth> customColumnWidth;

    @ApiModelProperty(value = "样式选择")
    private String styleSelection;


}
