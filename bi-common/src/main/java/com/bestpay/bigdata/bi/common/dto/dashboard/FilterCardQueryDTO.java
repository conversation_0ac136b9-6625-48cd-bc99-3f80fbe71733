package com.bestpay.bigdata.bi.common.dto.dashboard;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class FilterCardQueryDTO implements Serializable {

    private static final long serialVersionUID = 6363324806388605745L;
    private List<Long> idList;
    private Long dashboardId;



    private Long datasetId;


    public static FilterCardQueryDTO build( Long datasetId,List<Long> idList){
        FilterCardQueryDTO cardQueryDTO = new FilterCardQueryDTO();
        cardQueryDTO.setIdList(idList);
        cardQueryDTO.setDatasetId(datasetId);
        return cardQueryDTO;
    }
}
