package com.bestpay.bigdata.bi.common.response.metaData;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-03-22-13:57
 */
@Data
public class MetaDataSearchTableResponse implements Serializable{

    //current page num
    private Integer current;

    //page size
    private Integer size;

    //response record count
    private Integer total;

    //response page num
    private Integer page;

    //response data
    private List<MetaDataTable> records;
}
