package com.bestpay.bigdata.bi.common.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author：Song
 * @Date：2024/11/28 12:02
 * @Desc:
 */
@Data
@ApiModel("主题样式")
public class ThemeStyle {

    @ApiModelProperty(value = "颜色设置")
    private String colorConfig;

    @ApiModelProperty(value = "斑马纹")
    private Boolean zebraStripes;

}
