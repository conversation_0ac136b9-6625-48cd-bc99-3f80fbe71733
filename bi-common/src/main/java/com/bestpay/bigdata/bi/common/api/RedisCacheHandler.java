package com.bestpay.bigdata.bi.common.api;//package com.bestpay.bigdata.product.profile.handler;

import static com.bestpay.bigdata.product.profile.constant.RedisConstant.KEY_PREFIX;

import com.bestpay.bigdata.product.profile.handler.BaseCacheHandler;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedisCacheHandler implements BaseCacheHandler {

  @Resource(name="biRedisRedisTemplate")
  private RedisTemplate redisTemplate;

  @Override
  public boolean set(String key, String obj) {
    boolean flag = true;
    try {
      redisTemplate.opsForValue().set(KEY_PREFIX + key, obj);
    } catch (Exception e) {
      log.error("set redis error", e);
      flag = false;
    }
    return flag;
  }

  @Override
  public String get(String key) {
    Object obj = null;
    try {
      obj = redisTemplate.boundValueOps(KEY_PREFIX + key).get();
    } catch (Exception e) {
      log.error("get value from redis error", e.getMessage(), e);
    }
    return (String) obj;
  }

  @Override
  public void del(String key) {
    redisTemplate.delete(KEY_PREFIX + key);
  }
}
