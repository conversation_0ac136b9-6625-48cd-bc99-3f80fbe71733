package com.bestpay.bigdata.bi.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-12-14-11:15
 */
public enum PrestoCatalog {

    HIVE("hive"),
    HIVE_HDP("hive_hdp");

    private String catalog;

    PrestoCatalog(String catalog) {
        this.catalog = catalog;
    }

    public static Map<String, String> databaseMapCatalogMap = new HashMap<>();

    static {
        databaseMapCatalogMap.put(DataBaseSource.HIVE_11.getName(), HIVE.catalog);
        databaseMapCatalogMap.put(DataBaseSource.HIVE_36.getName(), HIVE_HDP.catalog);
    }
}
