package com.bestpay.bigdata.bi.common.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * ClassName: OpenapiCancelQueryRequest
 * Package: com.bestpay.bigdata.bi.common.request
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/8/1 16:03
 * @Version 1.0
 */
@NoArgsConstructor
@Data
public class OpenapiCancelQueryRequest implements Serializable {

    private String queryId;

    private String username;

    private String requestSystem;
}
