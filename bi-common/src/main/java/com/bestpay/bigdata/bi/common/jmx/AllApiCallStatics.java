package com.bestpay.bigdata.bi.common.jmx;

import com.bestpay.bigdata.bi.common.entity.ApiMetrics;
import com.bestpay.bigdata.bi.common.entity.ApiState;

import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 * @create 2022-04-14-15:12
 */
public class AllApiCallStatics {

    // 接口累计调用统计信息
    private Map<String, ApiMetrics> totalMetricsMap = new ConcurrentHashMap<>();
    private Map<String, ApiMetrics> successMetricsMap = new ConcurrentHashMap<>();
    private Map<String, ApiMetrics> failureMetricsMap = new ConcurrentHashMap<>();

    // 一段时间间隔内接口统计信息
    private Map<String, ApiMetrics> intervalTotalMetricsMap = new ConcurrentHashMap<>();
    private Map<String, ApiMetrics> intervalSuccessMetricsMap = new ConcurrentHashMap<>();
    private Map<String, ApiMetrics> intervalFailureMetricsMap = new ConcurrentHashMap<>();

    private static final long TIME_INTERVAL = 5 * 60 * 1000L;
    private Map<String,Long> lastCallDateMap = new ConcurrentHashMap<>();

    public Map<String, ApiMetrics> getTotalMetricsMap() {
        return totalMetricsMap;
    }

    public Map<String, ApiMetrics> getSuccessMetricsMap() {
        return successMetricsMap;
    }

    public Map<String, ApiMetrics> getFailureMetricsMap() {
        return failureMetricsMap;
    }

    public Map<String, ApiMetrics> getIntervalTotalMetricsMap() {
        updateExpireIntervalMap(this.intervalTotalMetricsMap);
        return intervalTotalMetricsMap;
    }

    public Map<String, ApiMetrics> getIntervalSuccessMetricsMap() {
        updateExpireIntervalMap(this.intervalSuccessMetricsMap);
        return intervalSuccessMetricsMap;
    }

    public Map<String, ApiMetrics> getIntervalFailureMetricsMap() {
        updateExpireIntervalMap(this.intervalFailureMetricsMap);
        return intervalFailureMetricsMap;
    }

    public Map<String, Long> getLastCallDateMap() {
        return lastCallDateMap;
    }

    /**
     *
     * @param key api class name plus method name
     */
    public void updateCountStatics(String key, long consumeTime, ApiState apiState){
        Map<String, ApiMetrics> tmpMap = null;
        switch (apiState){
            case normal:
                tmpMap = this.totalMetricsMap;
                break;
            case success:
                tmpMap = this.successMetricsMap;
                break;
            case failure:
                tmpMap = this.failureMetricsMap;
                break;
        }
        ApiMetrics metrics = tmpMap.getOrDefault(key, new ApiMetrics());
        tmpMap.put(key,updateMetrics(metrics,consumeTime,apiState));
        lastCallDateMap.put(key,new Date().getTime());
    }


    public void updateIntervalStatics(String key,long consumeTime,ApiState apiState){
        Map<String, ApiMetrics> tmpMap = null;
        switch (apiState){
            case normal:
                tmpMap = this.intervalTotalMetricsMap;
                break;
            case success:
                tmpMap = this.intervalSuccessMetricsMap;
                break;
            case failure:
                tmpMap = this.intervalFailureMetricsMap;
                break;
        }
        ApiMetrics intervalMetrics = tmpMap.getOrDefault(key, new ApiMetrics());
        Long lastCallDate = lastCallDateMap.getOrDefault(key, new Date().getTime());
        long currentDate = new Date().getTime();
        if(currentDate - lastCallDate > TIME_INTERVAL){
            intervalMetrics.setCount(0);
            intervalMetrics.setTotalConsumeTime(0);
            intervalMetrics.setAvgConsumeTime(0);
            intervalMetrics.setLastConsumeTime(0);
        }
        tmpMap.put(key,updateMetrics(intervalMetrics,consumeTime,apiState));
    }

    private ApiMetrics updateMetrics(ApiMetrics metrics, long consumeTime, ApiState apiState){

        if(ApiState.normal.equals(apiState)){
            if(consumeTime <= 0L){
                long count = metrics.getCount();
                metrics.setCount(++count);
                return metrics;
            }
            long totalTime = metrics.getTotalConsumeTime();
            totalTime += consumeTime;
            metrics.setTotalConsumeTime(totalTime);
            metrics.setAvgConsumeTime(metrics.getTotalConsumeTime() / metrics.getCount());
            metrics.setLastConsumeTime(consumeTime);
            return metrics;
        }

        long count = metrics.getCount();
        metrics.setCount(++count);
        long totalTime = metrics.getTotalConsumeTime();
        totalTime += consumeTime;
        metrics.setTotalConsumeTime(totalTime);
        metrics.setAvgConsumeTime(metrics.getTotalConsumeTime() / metrics.getCount());
        metrics.setLastConsumeTime(consumeTime);
        return metrics;
    }


    private void updateExpireIntervalMap(Map<String, ApiMetrics> tmpMap){
        for (Map.Entry<String, Long> entry : lastCallDateMap.entrySet()) {
            String key = entry.getKey();
            Long lastCallDate = entry.getValue();
            ApiMetrics metrics = tmpMap.getOrDefault(key, null);
            if(Objects.nonNull(metrics)) {
                long currentDate = new Date().getTime();
                if (currentDate - lastCallDate > TIME_INTERVAL) {
                    metrics.setCount(0);
                    metrics.setTotalConsumeTime(0);
                    metrics.setAvgConsumeTime(0);
                    metrics.setLastConsumeTime(0);
                    tmpMap.put(key,metrics);
                }
            }
        }
    }


}
