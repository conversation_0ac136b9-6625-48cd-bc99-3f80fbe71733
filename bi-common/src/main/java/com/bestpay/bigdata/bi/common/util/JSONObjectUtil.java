package com.bestpay.bigdata.bi.common.util;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.exception.ZhiJiaException;
import lombok.extern.slf4j.Slf4j;

/**
 * Json工具方法类。
 * <AUTHOR>
 */
@Slf4j
public class JSONObjectUtil {

    private static String CODE="code";
    public static String processStringResponse(String body) {
        log.info("request AiPlus AuthorizationUrl result is {}", body);
        JSONObject jsonObject = new JSONObject(body);
        Boolean status = jsonObject.getBool("success");
        if (!status) {
            log.info("failed to get currentUser : {}", jsonObject.getStr("message"));
            throwException(jsonObject);
        }
        return jsonObject.getStr("data");
    }

    public static  Boolean processBooleanResponse(String body) {
        JSONObject jsonObject = new JSONObject(body);
        Boolean status = jsonObject.getBool("success");
        log.info(body+":body"+status+":status"+jsonObject.getBool("data"));
        if (!status) {
            log.info("failed to get currentUser : {}", jsonObject.getStr("message"));
            throwException(jsonObject);
        }
        return jsonObject.getBool("data");
    }
    public static  JSONObject processResponse(String body) {
        JSONObject jsonObject = new JSONObject(body);
        Boolean status = jsonObject.getBool("success");
        if (!status) {
            log.info("failed to get currentUser : {}", jsonObject.getStr("message"));
            throwException(jsonObject);
        }
        JSONObject object = jsonObject.getJSONObject("data");
        if(object==null||object.size()==0) {
            return null;
        }

        return object;
    }

    public static  JSONArray processResponseArray(String body) {
        JSONObject jsonObject = new JSONObject(body);
        Boolean status = jsonObject.getBool("success");
        if (!status) {
            log.info("failed to get currentUser : {}", jsonObject.getStr("message"));
            throwException(jsonObject);
        }
        return jsonObject.getJSONArray("data");
    }
    /**
    *调用观星台处理接口
    *
     */
    public static  JSONObject processResponseDataSbp(String body) {
        JSONObject jsonObject = new JSONObject(body);
        log.info("jsonObject:{}",body);
        Boolean status = jsonObject.getBool("status");
        if (!status) {
            log.info("failed to get datasbp report : {}", jsonObject.getStr("message"));
            throwException(jsonObject);
        }
        return jsonObject.getJSONObject("data");
    }
    private static void throwException(JSONObject jsonObject){
        if(jsonObject.getStr(CODE).equals(CodeEnum.COOKIE_FAILED.code())){
            throw new ZhiJiaException(CodeEnum.REQUEST_CURRENT_USER_ERROR.code(),jsonObject.getStr("message"));
        }
        else {
            throw new BusinessException(CodeEnum.SYSTEM_ERROR.code(), jsonObject.getStr("message"));
        }
    }
}
