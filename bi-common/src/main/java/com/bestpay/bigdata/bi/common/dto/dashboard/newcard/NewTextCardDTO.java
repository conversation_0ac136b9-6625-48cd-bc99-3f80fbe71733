package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import cn.hutool.core.collection.CollUtil;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 文本卡片
 * <AUTHOR>
 */
@Data
public class NewTextCardDTO extends NewCardDTO {
    private String htmlContent;     // 文本内容
    private String bgColor;         // 背景色
    private String indexCardType;   // 指标卡片类型：IndexCardTypeEnum
    private List<EmbedIndexDTO> embedIndexList;
    private String bgImage;//背景图片
    private String otherBgImage;//外网背景图片
    private String bgImageFileName;//背景图片文件名

    @Data
    public static class EmbedIndexDTO {
        private Long indexCardId;
        private String name;
        private Integer status;
        private Object dragResult;
    }

    public String getIndexIds(){

        if(CollUtil.isEmpty(this.embedIndexList)){
            return "";
        }

        List<Long> refIndexIds = this
                .getEmbedIndexList()
                .stream()
                .map(EmbedIndexDTO::getIndexCardId)
                .collect(Collectors.toList());

        return StringUtils.join(refIndexIds,",");
    }
}
