package com.bestpay.bigdata.bi.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.annotation.Resource;

/**
 * Swagger 配置类
 *
 * <AUTHOR>
 * @date 2021/12/09
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {

  @Resource
  private ApolloRefreshConfig apolloRefreshConfig;
  @Bean
  public Docket api() {
    // 项目里面没有配置application-dev类似于这样的yaml，
    // 所以environment.getActiveProfiles()== 0 这样就不会展示swagger信息
    if (apolloRefreshConfig.getSwaggerSwitch()){
      return new Docket(DocumentationType.SWAGGER_2)
              .select()
              //api的配置路径
              .apis(RequestHandlerSelectors.any())
              //扫描路径选择
              .paths(PathSelectors.any())
              .build()
              .apiInfo(apiInfo());
    }
    return new Docket(DocumentationType.SWAGGER_2)
            .select()
            //api的配置路径
            .apis(RequestHandlerSelectors.none())
            //扫描路径选择
            .paths(PathSelectors.none())
            .build()
            .apiInfo(apiInfo());
  }

  private ApiInfo apiInfo() {
    return new ApiInfoBuilder()
        //文档标题
        .title("title")
        //接口概述
        .description("description")
        //版本号
        .version("1.0")
        //服务的域名
        .termsOfServiceUrl(String.format("url"))
        //.license("LICENSE")//证书
        //.licenseUrl("http://www.guangxu.com")//证书的url
        .build();
  }
}
