package com.bestpay.bigdata.bi.common.api;


import com.bestpay.bigdata.bi.common.dto.FileDownloadResult;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.exception.BusinessException;

/**
 * 查询服务
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
public interface QueryRedisService {


    /**
     * Gets query status.
     *
     * @param queryId the query id
     * @return the query status
     */
    Integer getQueryStatus(String queryId)throws BusinessException;
    /**
     * Gets query current count.
     *
     * @param requestUser the query id
     * @return the query current count
     */
    Integer getQueryCount(String requestUser);
    /**
     *查询进度条百分比
     *  @param queryId 查询Id
     *  @return String
     **/
    String getQueryProgress(String queryId);

    FileDownloadResult getRemoteFilePath(String queryId);
    /**
     * Gets query result.
     *
     * @param queryId the query id
     * @return the query result
     */
    QueryContext getQueryResult(String queryId)throws BusinessException;
    /**
     * Gets query result.
     *
     * @param queryId the query id
     * @return the query result
     */
    QueryContext getQueryResultNoData(String queryId)throws BusinessException;

}
