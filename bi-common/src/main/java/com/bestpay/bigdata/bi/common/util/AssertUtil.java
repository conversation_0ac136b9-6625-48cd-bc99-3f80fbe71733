package com.bestpay.bigdata.bi.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.error.ErrorCodeSupplier;
import com.bestpay.bigdata.bi.common.error.StandardErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.exception.ParameterException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

/**
 * 参数断言工具
 * <AUTHOR>
 * @date 2021/3/11 15:19
 **/
public class AssertUtil {

    public static <T>  void  notEmpty(ErrorCodeSupplier errorCodeSupplier, List<T> collection, String errorMsg) {
        if (CollUtil.isEmpty(collection)) {
            throw new BiException(errorCodeSupplier,errorMsg);
        }
    }

    public static <T>  void isTrue(ErrorCodeSupplier errorCodeSupplier, boolean expression, String errorMsg) {
        if (!expression) {
            throw new BiException(errorCodeSupplier,errorMsg);
        }
    }

    public static <T>  void isFalse(ErrorCodeSupplier errorCodeSupplier, boolean expression, String errorMsg) {
        if (expression) {
            throw new BiException(errorCodeSupplier,errorMsg);
        }
    }


    public static <T>  void notNull(ErrorCodeSupplier errorCodeSupplier, T objects, String errorMsg) {
        if (objects == null) {
            throw new BiException(errorCodeSupplier,errorMsg);
        }
    }


    public static <T>  void isNull(ErrorCodeSupplier errorCodeSupplier, T objects, String errorMsg) {
        if (objects != null) {
            throw new BiException(errorCodeSupplier,errorMsg);
        }
    }

    public static void notBlank(ErrorCodeSupplier errorCodeSupplier,String string, String errorMsg){
        if (StrUtil.isBlank(string)) {
            throw new BiException(errorCodeSupplier,errorMsg);
        }
    }

    public static <T extends CharSequence> void notBlank(T text, CodeEnum codeEnum) {
        if (StrUtil.isBlank(text)) {
            throw new BiException(StandardErrorCode.ASSERT_UTIL_DEFAULT_ERROR,codeEnum.message());
        }
    }

    public static <T extends CharSequence> void isBlank(T text, CodeEnum codeEnum) {
        if (StrUtil.isNotBlank(text)) {
            throw new BiException(StandardErrorCode.ASSERT_UTIL_DEFAULT_ERROR,codeEnum.message());
        }
    }

    @Deprecated
    public static<T> void notNull(T object, CodeEnum codeEnum) {
        if (null == object) {
            throw new BiException(StandardErrorCode.ASSERT_UTIL_DEFAULT_ERROR,codeEnum.message());
        }
    }

    @Deprecated
    public static<T> void isNull(T object, CodeEnum codeEnum) {
        if (null != object) {
            throw new BiException(StandardErrorCode.ASSERT_UTIL_DEFAULT_ERROR,codeEnum.message());
        }
    }

    @Deprecated
    public static<T> void isTrue(boolean expression, CodeEnum codeEnum) {
        if (!expression) {
            throw new BiException(StandardErrorCode.ASSERT_UTIL_DEFAULT_ERROR,codeEnum.message());
        }
    }

    @Deprecated
    public static<T> void isFalse(boolean expression, CodeEnum codeEnum) {
        if (expression) {
            throw new BiException(StandardErrorCode.ASSERT_UTIL_DEFAULT_ERROR,codeEnum.message());
        }
    }

    @Deprecated
    public static <E, T extends Iterable<E>> void notEmpty(T collection, CodeEnum codeEnum) {
        if (CollUtil.isEmpty(collection)) {
            throw new BiException(StandardErrorCode.ASSERT_UTIL_DEFAULT_ERROR,codeEnum.message());
        }
    }

    @Deprecated
    public static <E, T extends Iterable<E>> void isEmpty(T collection, CodeEnum codeEnum) {
        if (CollUtil.isNotEmpty(collection)) {
            throw new BiException(StandardErrorCode.ASSERT_UTIL_DEFAULT_ERROR,codeEnum.message());
        }
    }

    @Deprecated
    public static <K, V, T extends Map<K, V>> void notEmpty(T map, CodeEnum codeEnum) {
        if (CollUtil.isEmpty(map)) {
            throw new BiException(StandardErrorCode.ASSERT_UTIL_DEFAULT_ERROR,codeEnum.message());
        }
    }

    @Deprecated
    public static <K, V, T extends Map<K, V>> void isEmpty(T map, CodeEnum codeEnum) {
        if (CollUtil.isNotEmpty(map)) {
            throw new BiException(StandardErrorCode.ASSERT_UTIL_DEFAULT_ERROR,codeEnum.message());
        }
    }

    /**
     * 所有属性非空校验。
     * String类型额外校验空字符串。
     * @param entity
     * <AUTHOR>
     * @date 2023/7/12 11:14
     * @return void
     */
//    public static void validate(Object entity) {
//        Class<?> clazz = entity.getClass();
//        Method[] methods = clazz.getDeclaredMethods();
//        for (Method method : methods) {
//            if (method.getReturnType() != void.class && method.getParameterCount() == 0) {
//                Object value = null;
//                try {
//                    value = method.invoke(entity);
//                } catch (IllegalAccessException | InvocationTargetException e) {
//                    throw new BusinessException(method.getName() + " invoke error");
//                }
//                if (value == null) {
//                    throw new BusinessException(method.getName() + " is null");
//                } else if (value instanceof String && ((String) value).isEmpty()) {
//                    throw new BusinessException(method.getName() + " is blank");
//                }
//            }
//        }
//    }

    /**
     * 所有属性非空校验。
     * String类型额外校验空字符串。
     * @param entity
     * <AUTHOR>
     * @date 2023/7/12 11:14
     * @return void
     */
    public static void validate(Object entity) {
        Class<?> entityClass = entity.getClass();
        Field[] fields = entityClass.getDeclaredFields();
        for (Field field : fields) {
            Object value;
            try {
                Method getterMethod = entityClass.getMethod(getGetterName(field.getName()));
                value = getterMethod.invoke(entity);
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                throw new BusinessException("实体对象格式异常，无法使用validate校验");
            }

            ReqNullable annotation = field.getAnnotation(ReqNullable.class);
            if (annotation == null) {
                if (value == null) {
                    throw new BusinessException(field.getName() + " is null");
                }
                if (value instanceof String && ((String) value).isEmpty()) {
                    throw new BusinessException(field.getName() + " is blank");
                }
                if (value instanceof List) {
                    List<?> list = (List<?>) value;
                    for (Object obj : list) {
                        validate(obj);
                    }
                }
            }
        }
    }

    private static String getGetterName(String fieldName) {
        return "get" + Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);
    }
}
