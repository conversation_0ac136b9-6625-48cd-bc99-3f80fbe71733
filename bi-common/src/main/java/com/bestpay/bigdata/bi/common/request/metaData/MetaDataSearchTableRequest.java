package com.bestpay.bigdata.bi.common.request.metaData;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2022-03-22-13:51
 */
@Data
@Builder
public class MetaDataSearchTableRequest implements Serializable{

    // current page num
    private Integer current;

    //page size
    private Integer size;

    //search condition
    private MetaDataSearch search;
}
