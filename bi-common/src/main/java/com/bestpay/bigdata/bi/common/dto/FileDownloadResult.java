package com.bestpay.bigdata.bi.common.dto;

import java.io.Serializable;

/**
 * @author: laiyao
 * @date: 2022/05/17
 */
public class FileDownloadResult implements Serializable {

    private Boolean success;

    private String downloadPath;

    private String message;

    private String errorMessage;

    private String zipPassword;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getDownloadPath() {
        return downloadPath;
    }

    public void setDownloadPath(String downloadPath) {
        this.downloadPath = downloadPath;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getZipPassword() {
        return zipPassword;
    }

    public void setZipPassword(String zipPassword) {
        this.zipPassword = zipPassword;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
