package com.bestpay.bigdata.bi.common.dto.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("仪表盘布局信息")
public class DashboardGridSetting {

  @ApiModelProperty(value = "行间隔")
  private Integer rowInterval;
  @ApiModelProperty(value = "列间隔")
  private Integer colInterval;
  @ApiModelProperty(value = "卡片单元高度")
  private Integer cardUnitHeight;
  @ApiModelProperty(value = "网格列数")
  private Integer gridColNum;

}
