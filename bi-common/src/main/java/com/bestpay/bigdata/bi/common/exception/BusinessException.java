package com.bestpay.bigdata.bi.common.exception;

import com.bestpay.bigdata.bi.common.enums.CodeEnum;

/**
 * 系统处理异常
 * <AUTHOR>
 * @date 2021/3/11 14:55
 **/
public class BusinessException extends BaseBiException {


    public BusinessException(String code, String message) {
        super(code,message);
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(CodeEnum codeEnum) {
        super(codeEnum.code(),codeEnum.message());
    }

}
