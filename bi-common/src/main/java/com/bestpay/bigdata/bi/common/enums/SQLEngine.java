package com.bestpay.bigdata.bi.common.enums;

import com.bestpay.bigdata.bi.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum SQLEngine {

    PRESTO("presto"),
    HIVE("hive"),
    KYLIN("kylin"),
    SPARK_SQL("spark_sql"),
    MYSQL("mysql"),
    CLICKHOUSE("clickhouse"),
    POSTGRESQL("postgresql"),
    DORIS("doris"),
    KYUUBI("kyuubi"),
    OCEANBASE("oceanbase"),
    MOCK_ENGINE("mock_engine");

    private String engineName;

    public String getEngineName() {
        return engineName;
    }

    SQLEngine(String engine) {
        this.engineName = engine;
    }

    public static SQLEngine getEnumByEngineName(String engineName){
        for (SQLEngine engine : SQLEngine.values()) {
            if(engineName.equalsIgnoreCase(engine.engineName)){
                return engine;
            }
        }

        throw new BusinessException("不支持的引擎, engineName="+engineName);
    }
}
