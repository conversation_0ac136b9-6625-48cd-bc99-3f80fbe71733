package com.bestpay.bigdata.bi.common.enums;

/**
 * 响应码枚举
 *
 * <AUTHOR>
 * @date 2021/3/11 14:38
 **/
public enum CodeEnum {

    /**
     * 一切OK
     */
    SUCCESS("00000", ""),

    DELETE_SUCCESS("00001", "删除成功"),

    CREATE_SUCCESS("00002", "创建成功"),

    UPDATE_SUCCESS("00003", "更新成功"),

    EDIT_SUCCESS("00004", "编辑成功"),

    HIVE_JDBC_CONNECTION_SUCCESS("00007", "连通性测试通过"),

    ADD_FAVORITES_SUCCESS("00005", "收藏成功"),

    CANCEL_FAVORITES_SUCCESS("00006", "取消收藏成功"),

    ADD_FAVORITES_FAIL("00006", "已收藏"),

    PRIVILEGE_SUCCESS("00008", "赋权成功"),

    INSERT_SUCCESS("00009", "新增成功"),
    NOT_IMPLEMENTED("00010", "Function has not implemented, please implemented by business scenario!"),
    UPDATE_FAIL("00011", "更新失败"),
    PUBLISH_FAIL("00012", "发布失败"),
    /**
     * 服务端错误，系统异常
     */
    SYSTEM_ERROR("B0001", "系统异常"),
    DATA_ERROR("B00001", "数据异常"),

    CREATE_TABLE_FAILED("B0002", "建表失败"),

    SAVE_METADATA_FAILED("B0003", "建表成功，保存元数据失败"),

    UNAUTHORIZED("401", "长时间未操作，系统退出，请重新登录"),

    ILLEGAL_ARGUMENT("A0000", "参数异常"),

    EMPTY_ARGUMENT("A0001", "参数不能为空"),

    NO_PERMISSION("A0002", "权限不足，请联系管理员"),

    UNSUPPORTED_OPERATION("A0003", "不支持的操作"),

    ILLEGAL_ACCOUNT("A0004", "账号信息异常，请求失败"),

    COOKIE_FAILED("A0023", "Cookie失效"),

    JUMP_FAILED("A0310", "调取智加失败，请稍后再试"),

    LOGIN_FAILED("A0311", "登录失败"),

    REQUEST_AIPLUS_ERROR("A0312", "请求智加异常"),

    LDAP_ACCOUNT_ERROR("A0313", "请检查用户是否注册Ldap邮箱账号"),

    REQUEST_METADATA_ERROR("A0320", "元数据请求异常"),

    REQUEST_OPENAPI_ERROR("A0330", "OPEN API 请求异常"),

    REQUEST_INDEX_SETDATA_ERROR("A0321", "指标数据集请求异常"),

    REQUEST_INDEX_LIST_ERROR("A0322", "请求指标加工平台指标列表接口异常"),

    REQUEST_INDEX_DETAIL_ERROR("A0323", "请求指标加工平台指标详情接口异常"),

    REQUEST_DIMENSION_VALUE_ERROR("A0324", "维度值请求异常"),

    REQUEST_CURRENT_USER_ERROR("A0325", "获取当前用户失败"),

    REQUEST_USER_ORG_ERROR("A0326", "获取当前用户所属组织失败"),

    REQUEST_ORG_LIST_ERROR("A0327", "获取智加组织列表失败"),

    REQUEST_USER_INFO_ERROR("A0332", "获取智加用户信息失败"),

    REQUEST_DOWNLOAD_CENTER_ERROR("A0328", "下载中心请求异常"),

    REQUEST_DATASBP_USER_REPORTS_ERROR("A0329", "调用观星台报表接口请求异常"),

    REQUEST_DATASBP_REPORT_ERROR("A0330", "调用观星台链接接口请求异常"),

    REQUEST_INDEX_DATA_DATE_ERROR("A0331", "请求指标加工平台数据日期接口异常"),



    /************************SQL校验**********************/
    SQL_SYNATAX_CHECK_ERROR("S1000","语法校验失败！"),

    SQL_CHECK_OVER_LENGTH("S1001","查询脚本字符不超过1w"),

    SQL_EXECUTE_OVER_CONCURRENT("S1002","查询失败！当前用户任务执行并发度已达到上限（单用户仅限5个脚本同时运行），请稍后重试"),

    SQl_PARSE_ERROR("S1003","SQL解析结果为空"),

    SQL_CHECK_IS_NULL("S1004","脚本编辑框不为空"),

    SQL_SYNATAX_CREATE_TABLE_CHECK_ERROR("S1005","create只支持在临时库创建表（临时库为tmp开头），如需操作正式库，请前往元数据平台"),

    SQL_SYNATAX_CREATE_VIEW_CHECK_ERROR("S1006","create view只支持在临时库创建视图（临时库为tmp开头）"),

    SQL_SYNATAX_DROP_TABLE_CHECK_ERROR("S1007","Drop只支持在临时库删除表（临时库为tmp开头），如需操作正式库，请前往元数据平台；"),

    SQL_SYNATAX_DROP_VIEW_CHECK_ERROR("S1008","Drop只支持在临时库删除视图（临时库为tmp开头），如需操作正式库，请前往元数据平台；"),

    SQL_SYNATAX_CREATE_TABLE_AS_CHECK_ERROR("S1009","CTAS 只支持在临时库操作（临时库为tmp开头）"),

    SQL_SYNATAX_ALTER_RENAME_CHECK_ERROR("S1010","Alter table rename只支持在临时库修改表名（临时库为tmp开头），如需操作正式库，请前往元数据平台；"),

    SQL_SYNATAX_INSERT_INTO_CHECK_ERROR("S1011","insert into只支持在临时库操作（临时库为tmp开头）"),

    SQL_SYNATAX_INSERT_TABLE_CHECK_ERROR("S1012","insert只支持在临时库操作（临时库为tmp开头）"),

    SQL_SYNATAX_OTHER_CHECK_ERROR("S1013","当前存在不支持的DDL语法（{0}），当前只支持对临时表进行create table/view、alter table rename和drop table/view类DDL操作，其他DDL语法操作暂不支持；"),

    SQL_UDF_FUNC_UNION_TABLE_ERROR("S1014","当前语句包含UDF函数，暂不支持关联查询语句，仅支持单条SQL执行。"),

    SQL_UDF_FUNC_HIVE_ENGINE_ERROR("S1015","当前语句包含UDF函数，请在语句前增加“--hive”。"),

    SQL_UDF_FUNC_CHECK_PERMISSION_ERROR("S1016","权限不足。当前用户无{}函数（对{}表）的使用权限，请申请权限后再进行操作。"),

    SQL_ENGINE_EXECUTE_NOT_ALLOW_ERROR("S1017", "当前数据源 %s 不允许 %s 操作"),


    /************************SQL执行**********************/
    SQL_OPERATION_NOT_PERMIT("S2001","当前引擎不支持该操作"),
    SQL_RUNNER_CREATE_EXCEPTION("S2002","无法创建SQL_QUERY_RUNNER"),
    SQL_RUN_QUERY_EXCEPTION("S2003","执行SQL异常"),
    SQL_RUN_QUERY_TIMEOUT_EXCEPTION("S2004","查询超出指定时间"),
    SQL_CANCEL_QUERY("S2005","取消SQL执行"),
    SQL_CANCEL_QUERY_EXCEPTION("S2006","取消SQL异常"),
    SQL_QUERY_RESULT_LIMIT_EXCEPTION("S2007","当前查询结果为{0}M，超出系统上限（{1}M），请修改SQL（增加limit或限定分区）后重新查询~"),
    SQL_OPERATION_ERROR("S2008","数据库操作错误！"),

    CSV_CREATE_TABLE_ERROR("S2009","提交失败，上传文件格式有误, 错误原因:{0}"),
    CSV_UPLOAD_ERROR("S2010","建表失败，请重新上传文件"),

    CREATE_TABLE_AS_TEMP_SCHEMA_ERROR("S2011","语义校验失败，当前存在临时表CTAS关联分析，不能将该临时表数据通过CTAS导入到其他表中"),

    CSV_UPLOAD_SIZE_EXCEEDED("S2012","提交失败，文件大小超出{0}"),

    CSV_TABLE_ALREADY_EXISTS("S2013","提交失败，表名已存在"),

    CSV_CREATE_TABLE_FAILED("S2014","建表失败，请重试"),
    SQL_RUN_QUERY_SUCCESS("S2015","执行成功！"),

    /************************枚举值校验**********************/
    ENUM_CODE_ERROR("E0001","非法的枚举值"),

    USER_PERMISSION_CHECK_ERROR("M1000","用户权限不足！"),

    /************************Json Check**********************/
    JSON_CHECK_IS_NULL("JC0001","内容为空！"),
    JSON_CHECK_FORMAT_ERROR("JC0002","格式校验出错！"),
    JSON_CHECK_FIELD_TYPE_ERROR("JC0003","字段数据格式校验出错！"),
    JSON_CHECK_COMPARE_TYPE_ERROR("JC0004","比较类型校验出错！"),
    JSON_CHECK_RELATION_TYPE_ERROR("JC0005","关系类型校验出错！"),
    JSON_CHECK_SHOW_MODE_ERROR("JC0006","显示方式校验出错！"),
    JSON_CHECK_DIMENSION_MODE_ERROR("JC0007","显示方式校验出错！"),
    JSON_CHECK_DIMENSION_DECIMAL_VALUES_ERROR("JC0008","维度值数量校验出错！"),
    JSON_CHECK_DIMENSION_DECIMAL_VALUES_SORT_ERROR("JC0009","维度值数值大小比对校验出错！"),
    JSON_CHECK_DIMENSION_COUNTS_ERROR("JC0010","维度数量校验出错！"),
    JSON_CHECK_FILTER_CHARACTER_VALUES_LENGTH_ERROR("JC00011","过滤条件字符型数值个数校验出错！"),
    JSON_CHECK_FILTER_VALUES_LENGTH_ERROR("JC00012","过滤条件数值个数校验出错！"),
    JSON_CHECK_FILTER_DECIMAL_VALUE_ERROR("JC00013","过滤条件数值不是数字类型！"),
    JSON_CHECK_FILTER_DECIMAL_VALUE_COMPARE_ERROR("JC00014","过滤条件数值比较出错！"),
    JSON_CHECK_FILTER_DATETIME_VALUE_ERROR("JC00015","过滤条件数值不是日期类型！"),
    JSON_CHECK_FILTER_DATETIME_VALUE_COMPARE_ERROR("JC00016","过滤条件日期比较出错！"),
    JSON_CHECK_FILTER_COUNTS_ERROR("JC00017","过滤条件数量超过规定数量！"),
    JSON_CHECK_ANALYSIS_JSON_ERROR("JC0018","解析JSON串出错！"),
    JSON_CHECK_DATE_VALUE_ERROR("JC0019","日期值类型校验出错！"),
    JSON_CHECK_INDEX_TOP_VALUE_ERROR("JC00020","指标TOPN数值不是数字类型！"),
    JSON_CHECK_INDEX_TOP_MAX_ERROR("JC00021","指标TOPN超过最大限定值！"),
    JSON_CHECK_INDEX_SORT_INCLUDE_ERROR("JC00022","指标排序内容出错！"),
    JSON_CHECK_INDEX_COUNTS_ERROR("JC00023","指标总数出错！"),

    /************************多维分析查询**********************/
    STATUS_IS_NULL("ST1000","queryId对应运行状态Status为空！"),
    PROCESS_DATA_ERROR("ST1003", "处理数据异常"),
    QUERY_ID_EMPTY_ARGUMENT("ST1001", "queryId参数不能为空"),
    SORT_EXCEPTION("ST1002","排序解析异常！"),

    /************************报表加工**********************/
    DIR_NAME_OVER_LENGTH("B1000","目录名称超过20个字符"),
    DIR_NAME_EXISTED("B1001","目录名称已存在，请修改"),
    DELETE_DIRECTORY_ERROR("B1002","该目录下存在报表，无法删除目录"),
    SFTP_INFO_ERROR("B1003","sftp链接报错"),
    SFTP_UPLOAD_FILE_ERROR("B1004","上传sftp文件报错"),
    REPORT_NAME_EXITING("B1005","报表名称已存在！"),

    REPORT_DATA_AUTH_FORMAT_ERROR("B1006","报表数据权限格式错误"),
    CONVERT_SQL_ERROR("B1007","转化sql报错，数据库或者表名为空"),

    DATA_SIZE_OVER_ERROR("B1008","当前数据量{}大于10000，建议更换表格形式展示"),

    ADVANCE_COMPUTING_SUBTOTAL_ERROR("B1009","暂不支持高级计算和小计/总计功能同时使用~"),

    ADVANCE_COMPUTING_SUBTOTAL_NOT_SUPPORT("B1010","对比字段暂不支持高级计算和小计/总计功能~"),
    REPORT_DIR_NOT_EXISTS("B1011","报表目录不存在,请新建报表目录"),
    REPORT_NOT_EXISTS("B1012","报表不存在，请检查是否上线"),
    REPORT_RESULT_IS_ERROR("B1013","所属目录:{},报表:{},返回结果错误"),
    EMAIL_ACCOUNT_NOT_NULL("B1014","发件人信息不能为空"),
    /***********************数据探查*********************/
    SAVE_SCRIPT_NAME_OVER_LENGTH("P1000","脚本名称长度超过30个字符限制！"),

    SAVE_SCRIPT_REPEAT_NAME("P1002","脚本名称重复，请更换脚本名称！"),
    SAVE_SCRIPT_COUNT_LIMIT("P1003","脚本最多保存100条~"),
    QUERY_SCRIPT_NONE_EXIST("P1004","数据库中不存在该脚本！"),
    DATASOURCE_NAME_NOT_NULL("P1005","datasourceName can not be null!"),
    DATABASE_NAME_NOT_NULL("P1006","databaseName can not be null!"),
    TABLE_NAME_NOT_NULL("P1007","tableName can not be null!"),
    KEYWORD_NOT_NULL("P1008", "keyword can not be null!"),
    RESOURCE_NOT_EXIST("P1009", "当前库表 {}不存在，请重新检查~"),
    DATA_CACHE_ALREADY_EXPIRED("P1010", "数据缓存已失效，请重新查询"),

    /***********************仪表板*********************/
    DASHBOARD_EXISTS("D1000","名称重复，请重新输入!"),
    DASHBOARD_NAME_OVER_LENGTH("D1001","数据大屏名称超过50，请修改!"),
    DASHBOARD_NAME_NOT_BLANK("D1002","仪表盘名称不可为空"),
    DASHBOARD_DIR_NOT_BLANK("D1003","仪表盘所属目录不可为空"),
    DASHBOARD_DIR_ID_NOT_EXIST("D1004","仪表盘所属目录Id不存在"),
    OWNER_NOT_NULL("D1005","责任人不可为空"),
    OWNER_EMAIL_NOT_NULL("D1006","责任人邮箱不可为空"),
    OWNER_EN_NAME_NOT_NULL("D1007","责任人英文名不可为空"),
    DASHBOARD_CREATE_ERROR("D1008","数据大屏创建错误"),
    DASHBOARD_DATA_ERROR("D1009","数据异常"),
    SUB_TASK_NAME_OVER_LENGTH("D1010","订阅任务的名称字符长度限制在50个字符以内"),
    SUB_EMAIL_TOPIC_OVER_LENGTH("D1011","订阅邮件的主题字符长度限制在50个字符以内"),
    SUB_EMAIL_MSG_OVER_LENGTH("D1012","订阅邮件的正文字符长度限制在100个字符以内"),
    DASHBOARD_SUB_OPER_ERROR("D1013","订阅任务数据库操作失败！"),
    UPLOAD_ERROR("D1014","上传失败！"),
    DOWNLOAD_ERROR("D2021","下载失败！"),
    DASHBOARD_SUB_PARAMETER_ERROR("D1015","订阅任务开始时间参数错误"),
    DASHBOARD_DIR_REQUEST_ERROR("D1016","请求仪表盘目录报错"),
    DASHBOARD_DIR_REPORT_REQUEST_ERROR("D1017","请求仪表盘卡片报错"),
    DASHBOARD_DETAIL_REQUEST_ERROR("D1018","请求仪表板详情报错"),
    DASHBOARD_DETAIL_RESULT_ERROR("D1019","仪表板详情结果为空"),
    DASHBOARD_SBP_RESULT_NULL("D1020","观星台接口返回值为空"),
    DING_TALK_MSG_OVER_LENGTH("D1021","钉钉消息的正文字符长度限制在100个字符以内"),
    DING_TALK_WEBHOOK_SECRET_OVER_LENGTH("D1021","钉钉webHook或者密钥字符长度限制在512个字符以内"),
    DATA_SCREEN_DB_DATA_ERROR("D1022","数据大屏数据库数据异常"),
    DASHBOARD_MOVE_PARAM_ERROR("D1023", "仪表板移动参数错误"),
    DASHBOARD_NOT_EXISTS("D1024", "仪表板不存在"),
    DASHBOARD_DOWN_LINE("D1025", "仪表板已下线，请联系对应责任人"),
    DASHBOARD_PLEASE_DOWN_LINE("D1026", "该仪表板已上线，请先下线"),
    DASHBOARD_PAGE_SETTING_NOT_EXISTS("D1027", "页面设置不存在"),
    DATASCREEN_NOT_EXISTS("D1028", "数据大屏不存在"),
    IMAGE_FORMAT_ERROR("D1029", "文件格式不支持"),
    IMAGE_SIZE_ERROR("D1030", "超过文件大小限制"),
    IMAGE_UPLOAD_ERROR("D1031", "文件上传失败"),
    DASHBOARD_DISPLAY_TYPE_ERROR("D1027", "该仪表板展示类型错误"),
    DASHBOARD_REPORT_CARD_NOT_EXISTS("D1028", "该仪表板报表卡片不存在"),
    DASHBOARD_FILTER_CARD_NOT_EXISTS("D1029", "该仪表板筛选器卡片不存在"),
    DASHBOARD_TEXT_CARD_NOT_EXISTS("D1030", "该仪表文本卡片不存在"),
    DASHBOARD_INDEX_TEXT_CARD_NOT_EXISTS("D1031", "该仪表INDEX卡片不存在"),
    DASHBOARD_COPY_TEXT_EXCEPTION("D1032", "复制Text出现异常"),
    DASHBOARD_COPY_FILTER_EXCEPTION("D1033", "复制filter出现异常"),
    DING_TALK_PRIVATE_USERIDS_SIZE("D1034","个人钉钉推送配置用户上限为50个"),

    /*******************仪表板目录**********************/
    DASHBOARD_DIRECTORY_NAME_NULL_ERROR("DD1001", "目录名称不能为空"),
    DASHBOARD_DIRECTORY_NAME_LENGTH_ERROR("DD1002", "目录名称长度不能超过50个字符"),
    DASHBOARD_DIRECTORY_SAME_NAME_ERROR("DD1003", "目录名称已经存在，请重新输入"),
    DASHBOARD_DIRECTORY_NOT_EMPTY_ERROR("DD1004", "目录里面非空，删除失败"),
    DASHBOARD_DIRECTORY_MOVE_PARAM_ERROR("DD1005", "目录移动失败"),


    /*******************仪表板目录**********************/
    REPORT_DIRECTORY_NAME_NULL_ERROR("DD1001", "目录名称不能为空"),
    REPORT_DIRECTORY_NAME_LENGTH_ERROR("DD1002", "目录名称长度不能超过100个字符"),
    REPORT_DIRECTORY_SAME_NAME_ERROR("DD1003", "目录名称已经存在，请重新输入"),
    REPORT_DIRECTORY_NOT_EMPTY_ERROR("DD1004", "目录里面非空，删除失败"),
    REPORT_DIRECTORY_MOVE_PARAM_ERROR("DD1005", "目录移动失败"),

    /***********************嵌入应用*********************/
    APP_EMBED_EXISTS("D1008","应用嵌入任务名称重复，请重新输入!"),
    APP_EMBED_TASK_CODE_ERROR("D1009","生成应用嵌入任务code错误!"),
    APP_EMBED_APP_KEY_REQUEST_PARAMETER_ERROR("AE1003","获取appKey参数：嵌入对象或者接入平台为Null!"),
    APP_EMBED_APP_KEY_ERROR("AE1004","appKey错误，请重新获取！"),
    APP_VERIFY_IDENTITY_ERROR("A1006", "调用方信息有误!"),
    APP_EMBED_NULL_ERROR("AE1005","应用对象为空!"),
    APP_KEY_EXPIRE_ERROR("AE1007", "appKey已经过期，请重新获取!"),
    APP_SERVER_INTERNAL_ERROR("A1008", "调用服务内部错误!"),
    APP_DECRY_ERROR("A1009", "解密报错!"),
    APP_URL_NOT_COMPLETE_ERROR("A1010", "链接url缺失!"),

    DASHBOARD_NOT_PUBLISH_MOBILE("A1011", "仪表板没有发布移动端!"),

    TABLEAU_DOWNLOAD_FAIL("A1012", "tableau导出失败!"),

    META_FIELDS_QUERY_FAIL("A1013", "元数据字段信息查询失败!"),



    /******************* 数据集模块 *********************/
    DATASET_IDS_BLANK("A1014", "数据集id为空!"),
    DATASET_NOT_EXIST("A1015", "数据集不存在!"),
    DATASET_ELEMENT_NOT_EXIST("A1016", "数据集没有配置表!"),
    REQUEST_INDEX_DATA_SET_ERROR("A0332", "数据集存在异常，请重新配置"),
    DATASET_STATUS_ERROR("AE1009", "数据集 %s 已经下线，请上线后查看"),
    DATASET_ELEMENT_ADD_ERROR("AE1000", "添加节点失败，请刷新页面后重试!"),
    DATASET_ELEMENT_REMOVE_ERROR("AE1001", "移除节点失败，请刷新页面后重试!"),
    DATASET_RELATION_ADD_ERROR("AE1002", "添加关联关系失败，请刷新页面后重试!"),
    DATASET_RELATION_SHOW_ERROR("AE1003", "显示关联关系失败，请刷新页面后重试!"),
    DATASET_CONFIG_REFRESH_ERROR("AE1004", "数据集配置刷新失败，请刷新页面后重试!"),
    DATASET_CACHE_EXCEPTION_ERROR("AE1005", "数据异常，请刷新页面后重试!"),
    DATASET_DATA_PREVIEW_ERROR("AE1006", "数据预览刷新失败，请刷新页面后重试!"),
    DATASET_DATA_PARAM_ERROR("AE1006", "数据集参数异常!"),
    DATASET_CONFIG_REFRESH("AE1007", "数据集配置需要刷新!"),
    DATASET_NAME_REPEAT("AE1008", "数据集名称重复!"),
    DATASET_RELATION_NOT_EXIST("A1009", "边不存在!"),
    DATASET_PREVIEW_DATA_EMPTY("A1011", "数据为空!"),
    DATASET_CALCULATION_LOGIC_IS_INCONSISTENT("A1012", "计算逻辑已发生改变!"),

    /******************* 预警模块 *********************/
    REPORT_WARN_CONFIG_NOT_EXIST("A1012", "预警配置不存在!"),
    REPORT_WARN_CONFIG_NAME_REPEATED("A1013", "名称重复!"),
    REPORT_NOT_EXIST("A1014", "报表不存在"),


    /*******************数据探查脚本目录**********************/
    SCRIPT_DIRECTORY_NAME_NULL_ERROR("DD1001", "目录名称不能为空"),
    SCRIPT_DIRECTORY_NAME_LENGTH_ERROR("DD1002", "目录名称长度不能超过50个字符"),
    SCRIPT_DIRECTORY_SAME_NAME_ERROR("DD1003", "目录名称已经存在，请重新输入"),
    SCRIPT_DIRECTORY_NOT_EMPTY_ERROR("DD1004", "目录里面非空，删除失败"),
    SCRIPT_DIRECTORY_MOVE_PARAM_ERROR("DD1005", "目录移动参数错误"),
    SCRIPT_MOVE_PARAM_ERROR("D1023", "脚本移动参数错误"),

    /*******************参数校验**********************/
    PARAM_VALID_ERROR("paramValidError","参数校验错误"),

    /******************* 新版数据大屏 *********************/
    DATA_SCREEN_NOT_EXIST("DS1000", "数据大屏不存在"),


    /******************* AI *********************/
    CALL_AI_FAILED("AI1001", "调用AI失败."),


    ;


    private final String code;

    private final String message;

    CodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String code() {
        return this.code;
    }

    public String message() {
        return this.message;
    }
    public String message(String value) {
        return this.message+value;
    }

    @Override
    public String toString() {
        return "{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
