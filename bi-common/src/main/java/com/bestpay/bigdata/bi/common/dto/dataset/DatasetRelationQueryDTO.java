package com.bestpay.bigdata.bi.common.dto.dataset;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-07-04-15:35
 */
@Data
@Builder
public class DatasetRelationQueryDTO implements Serializable {

    private static final long serialVersionUID = -88089201981327986L;

    private List<String> datasetCodeList;

    /**主键*/
    private Long id;

    /**编码*/
    private String code;

    /**数据集编码*/
    private String datasetCode;

    /**连接类型*/
    private String joinType;

    /**@DatasetElement code*/
    private String childCode;

    /**@DatasetElement code*/
    private String parentCode;

    /**连接配置*/
    private String relationConfig;

    /**状态*/
    private Integer statusCode;

    private Date createdAt;

    private String createdBy;

    private Date updatedAt;

    private String updatedBy;
}
