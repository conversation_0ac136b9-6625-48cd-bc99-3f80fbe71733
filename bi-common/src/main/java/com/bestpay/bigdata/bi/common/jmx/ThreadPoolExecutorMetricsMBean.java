package com.bestpay.bigdata.bi.common.jmx;

/**
 * <AUTHOR>
 * @create 2022-04-08-16:15
 */
public interface ThreadPoolExecutorMetricsMBean {

    boolean isShutdown();

    boolean isTerminating();

    boolean isTerminated();

    String getRejectedExecutionHandler();

    int getCorePoolSize();

    void setCorePoolSize(int corePoolSize);

    int getMaximumPoolSize();

    void setMaximumPoolSize(int maximumPoolSize);

    int getPoolSize();

    int getActiveCount();

    int getLargestPoolSize();

    String getKeepAliveTime();

    void setKeepAliveTime(String duration);

    boolean isAllowCoreThreadTimeOut();

    void setAllowCoreThreadTimeOut(boolean allowsCoreThreadTimeOut);

    long getTaskCount();

    long getCompletedTaskCount();

    int getQueuedTaskCount();
}
