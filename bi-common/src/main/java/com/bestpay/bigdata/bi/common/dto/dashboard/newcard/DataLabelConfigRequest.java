package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataLabelConfigRequest extends LabelBaseConfigRequest{

    /**
     * 是否加粗
     */
    private Boolean bold;
    /**
     * 斜体
     */
    private Boolean italic;

    @ApiModelProperty(value = "是否展示标签")
    private Boolean showLabel;

    @ApiModelProperty(value = "展示位置")
    private Byte showPosition;

    @ApiModelProperty(value = "标签旋转角度")
    private Integer labelRotationAngle;

    /**
     * X轴偏移量
     * 使用JsonProperty的元原因：https://blog.csdn.net/LB_bei/article/details/140626871
     */
    @JsonProperty("xLabelOffset")
    private Integer xLabelOffset;
    /**
     * Y轴偏移量
     */
    @JsonProperty("yLabelOffset")
    private Integer yLabelOffset;
    /**
     * 是否智能展示颜色
     */
    private Boolean showAutoColor;

    public Boolean getShowAutoColor() {
        if(this.showAutoColor==null){
            return false;
        }

        return showAutoColor;
    }

    public void setShowAutoColor(Boolean showYAxis) {
        if(showYAxis==null){
            this.showAutoColor = false;
            return;
        }

        this.showAutoColor = showYAxis;
    }
}
