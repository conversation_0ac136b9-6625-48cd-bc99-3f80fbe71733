package com.bestpay.bigdata.bi.common.common;

/**
 * ClassName: ResultTypeEnum
 * Package: com.bestpay.bigdata.bi.backend.cache
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/3/11 9:24
 * @Version 1.0
 */
public enum ResultTypeEnum
{
    DATA("data"),
    PATH("path");

    private String code;

    ResultTypeEnum(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public static boolean checkValidResultType(String code) {
        ResultTypeEnum[] values = ResultTypeEnum.values();
        for (ResultTypeEnum value : values) {
            if (value.code.equals(code)) {
                return true;
            }
        }
        return false;
    }
}
