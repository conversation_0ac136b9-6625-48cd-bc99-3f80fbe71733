package com.bestpay.bigdata.bi.common.util;

import static com.bestpay.bigdata.bi.common.common.Constant.DATA_SCREEN_ADMIN;
import static com.bestpay.bigdata.bi.common.common.Constant.DATA_SCREEN_COOKIE;
import static com.bestpay.bigdata.bi.common.common.Constant.DATA_SCREEN_EMAIL;
import static com.bestpay.bigdata.bi.common.common.Constant.DATA_SCREEN_NAME;

import com.bestpay.bigdata.bi.common.entity.City;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.common.entity.Province;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.facebook.presto.jdbc.internal.guava.collect.Maps;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 */
public class UserContextUtil {

  private static ThreadLocal<UserInfo> user = new ThreadLocal<>();

  public static void preHandler(UserInfo userInfo) {
    user.set(userInfo);
  }

  public static void removeHandler() {
    user.remove();
  }

  public static UserInfo get(){
    if(user.get()==null) {
      // 未登录
      UserInfo userInfo = new UserInfo();
      userInfo.setEmail(DATA_SCREEN_EMAIL);
      userInfo.setOrg(Org.builder().name(DATA_SCREEN_NAME).build());
      userInfo.setAccount(DATA_SCREEN_ADMIN);
      userInfo.setUserType(UserInfo.UserType.MOCK_DATA_SCREEN);
      userInfo.setCookieId(DATA_SCREEN_COOKIE);
      userInfo.setIsManager(false);
      userInfo.setProvince(new Province("",""));
      userInfo.setCity(new City("",""));
      userInfo.setIsManager(false);
      userInfo.setPermissionInfoList(Lists.newArrayList());
      userInfo.setUserGroupsList(Lists.newArrayList());
      userInfo.setPermissions(Maps.newHashMap());
      return userInfo;
    }

    return user.get();
  }

  /**
   * 优化后的代码均使用getUserInfo获取用户，确保新老兼容
   * @return
   */
  public static UserInfo getUserInfo(){
    return get();
  }

}
