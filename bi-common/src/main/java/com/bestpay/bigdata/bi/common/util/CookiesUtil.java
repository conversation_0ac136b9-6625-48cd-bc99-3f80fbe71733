package com.bestpay.bigdata.bi.common.util;

import cn.hutool.core.util.ArrayUtil;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.Cookie;

/**
 * Cookies工具方法类。
 * <AUTHOR>
 */
public class CookiesUtil {
    /**
     * 从cookie中获取指定key的value
     *
     * @param cookieList
     * @param cookieName
     * @return
     */
    public static String getCookieValue(Cookie[] cookieList, String cookieName) {
        if (ArrayUtil.isNotEmpty(cookieList)) {
            for (Cookie cookie : cookieList) {
                if (cookieName.equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    public static Map<String,String> getCookieMap(Cookie[] cookieList) {
        Map<String,String> map = new HashMap<>();
        if (ArrayUtil.isNotEmpty(cookieList)) {
            for (Cookie cookie : cookieList) {
                map.put(cookie.getName(),cookie.getValue());
            }
        }
        return map;
    }
}
