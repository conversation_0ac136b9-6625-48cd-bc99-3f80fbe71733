package com.bestpay.bigdata.bi.common.error;

public enum ScheduleErrorCode implements ErrorCodeSupplier {
    POST_FREQUENCY_ERROR("00001",ErrorType.BUSINESS_ERROR),
    TIME_TYPE_ERROR("00002",ErrorType.BUSINESS_ERROR),
    INTERVAL_ERROR("00003",ErrorType.BUSINESS_ERROR),
    INVOKE_JOB_ERROR("00004",ErrorType.BUSINESS_ERROR),

    ;

    private static final String PREFIX = "SCHEDULE_";

    private final ErrorCode errorCode;

    ScheduleErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
