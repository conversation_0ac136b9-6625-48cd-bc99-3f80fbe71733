package com.bestpay.bigdata.bi.common.request.metaData;

import lombok.Builder;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2022-03-22-15:02
 */
@Data
@Builder
public class MetaDataQueryFieldRequest implements Serializable{

    private String datasourceType;
    private String datasourceName;
    private String dbName;
    private String tableName;

    public String getUniqueKey(){
        return this.datasourceType + this.datasourceName + this.dbName + this.tableName;
    }
}
