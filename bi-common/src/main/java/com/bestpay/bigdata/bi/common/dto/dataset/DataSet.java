package com.bestpay.bigdata.bi.common.dto.dataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 数据集实体类
 *
 * <AUTHOR>
 * @since 2021-12-09 10:27:40
 */
@Data
@ApiModel(value = "多维分析数据集类")
public class DataSet{

  @ApiModelProperty(value = "数据集Id")
  private Long datasetId;

  @ApiModelProperty(value = "数据集名称")
  private String name;

  @ApiModelProperty(value = "应用场景code",required = true)
  private Integer code;

  @ApiModelProperty(value = "组织code",required = true)
  private String orgCode;

  @ApiModelProperty(value = "授权组织code",required = true)
  private String orgAuth;

  @ApiModelProperty(value = "数据日期")
  private String dataDate;

  @ApiModelProperty(value = "数据源名称")
  private String dataSourceName;

  @ApiModelProperty(value = "数据库名称")
  private String dataBaseName;

  @ApiModelProperty(value = "表名称")
  private String tableName;

  @ApiModelProperty(value = "数据集类型")
  private String dataSourceType;

  @ApiModelProperty(value = "数据集类型编码")
  private Integer dataSourceTypeCode;

  @ApiModelProperty(value = "参数配置")
  private List<DatasetParamDTO> paramList;
}
