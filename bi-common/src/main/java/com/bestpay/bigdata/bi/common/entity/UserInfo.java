package com.bestpay.bigdata.bi.common.entity;

import com.bestpay.bigdata.bi.common.dto.AiplusChannelInfo;
import com.bestpay.bigdata.bi.common.dto.CurrentAiPlusUserGroup;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/3/12 11:15
 **/
@Data
@Accessors(chain = true)
@ApiModel("用户信息")
public class UserInfo implements Serializable {

  public static final String KEY = "userInfo";

  @ApiModelProperty(value = "用户唯一id")
  private String oneId;

  @ApiModelProperty(value = "用户名")
  private String username;

  @ApiModelProperty(value = "用户角色")
  private String role;

  @ApiModelProperty(value = "用户角色名称")
  private String roleName;

  @ApiModelProperty(value = "用户邮箱")
  private String email;

  @ApiModelProperty(value = "用户权限信息")
  private List<PermissionInfo> permissionInfoList;

  @ApiModelProperty(value = "用户权限标识集合 buttonList")
  private Map<String, List<String>> permissions;

  @ApiModelProperty(value = "token")
  private String token;

  @ApiModelProperty(value = "中文名")
  private String name;

  @ApiModelProperty(value = "账号")
  private String account;

  @ApiModelProperty(value = "手机号")
  private String mobile;

  @ApiModelProperty(value = "中文名")
  private String nickName;

  @ApiModelProperty(value = "组织信息")
  private Org org;

  @ApiModelProperty(value = "省份")
  private Province province;

  @ApiModelProperty(value = "城市")
  private City city;

  @ApiModelProperty(value = "是否是管理员")
  private Boolean isManager;

  @ApiModelProperty(value = "用户组")
  private List<List<CurrentAiPlusUserGroup>> userGroupsList;

  @ApiModelProperty(value = "用户cookie")
  private String cookieId;

  @ApiModelProperty(value = "用户认证类型 智加cookie、邮箱、mock（大屏）")
  private UserType userType;

  @ApiModelProperty(value = "app嵌入token")
  private String appEmbedToken;

  @ApiModelProperty(value = "钉钉userId,通过智加获取")
  private String dingDingUserId;

  @ApiModelProperty(value = "用户状态")
  private Integer status;

  @ApiModelProperty(value = "用户渠道信息")
  private List<AiplusChannelInfo> channelInfoList;

  /**
   * user枚举类型
   */
  public enum UserType {
    /**
     * AI_PLUS_AUTH
     */
    AI_PLUS_AUTH,
    /**
     * EMAIL_AUTH
     */
    EMAIL_AUTH,
    /**
     * MOCK_DATA_SCREEN
     */
    MOCK_DATA_SCREEN
  }

}
