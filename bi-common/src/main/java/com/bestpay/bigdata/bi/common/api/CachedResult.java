package com.bestpay.bigdata.bi.common.api;

import com.google.common.hash.HashCode;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;

/**
 * The interface CachedResult.
 *
 * <AUTHOR> <PERSON>
 * @date 2022 /3/24 15:30
 */
public interface CachedResult<T> {
    public final String RESULTS_KEY_PREFIX = "CACHED_RESULT_KEY_PREFIX:%s";

    T dataToBeCached();

    String getCacheKey();
    default String generateCacheKey(List<String> args){
        HashFunction function = Hashing.sha256();
        Hasher hasher = function.newHasher();
        hasher.putString(args.toString(), StandardCharsets.UTF_8);
        HashCode hashCode = hasher.hash();
        return String.format(RESULTS_KEY_PREFIX, hashCode.toString());
    }
}
