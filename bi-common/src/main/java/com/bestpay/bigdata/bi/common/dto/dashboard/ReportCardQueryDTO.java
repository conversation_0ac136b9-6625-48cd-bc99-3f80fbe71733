package com.bestpay.bigdata.bi.common.dto.dashboard;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ClassName: ReportCardQueryDTO
 * Package: com.bestpay.bigdata.bi.common.dto.dashboard
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/1/10 13:40
 * @Version 1.0
 */
@Data
public class ReportCardQueryDTO implements Serializable
{
    private static final long serialVersionUID = 3728415161519923371L;
    private Long dashboardId;
    private List<Long> idList;

    private Long datasetId;


    public static ReportCardQueryDTO build(Long datasetId, List<Long> idList){
        ReportCardQueryDTO cardQuery = new ReportCardQueryDTO();
        cardQuery.setIdList(idList);
        cardQuery.setDatasetId(datasetId);
        return cardQuery;
    }
}
