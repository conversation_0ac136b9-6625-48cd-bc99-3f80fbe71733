package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import lombok.Data;

/**
 * @author:gaodingsong
 * @description:仪表板表格字体样式自定义model
 * @createTime:2024/5/7 10:29
 * @version:1.0
 */
@Data
public class DashboardTableFontStyleCustomerModel {

    // 1 行表头  2 列表头 3:表格数据字体/颜色配置
    private Byte customerType;

    // -1 表示 自动 当customerType == 2的时候才有值
    private Integer rowHeight;

    // -1 表示 自动当customerType == 1的时候 才有值
    private Integer columnsNum;
    // 填充色
    private String fillColor;

    private Integer fontSize;
}
