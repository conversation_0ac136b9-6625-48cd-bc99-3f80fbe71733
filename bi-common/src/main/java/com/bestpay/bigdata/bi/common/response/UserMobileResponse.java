package com.bestpay.bigdata.bi.common.response;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserMobileResponse implements Serializable {
    /**
     * 用户手机号
     */
    private List<String> userMobileList;
    /**
     * 错误消息
     */
    private List<String> senderErrorMessageList;
}
