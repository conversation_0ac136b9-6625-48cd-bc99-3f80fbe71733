package com.bestpay.bigdata.bi.common.dto.report.component;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "维度组件")
public class DimensionComponentPropertyDTO extends CommonComponentPropertyDTO {

  @ApiModelProperty(value = "日期截取展示枚举 @DateShowFormatEnum")
  private Integer dateShowFormat;

  @ApiModelProperty(value = "字段值列表")
  private List<String> valueList;

  @ApiModelProperty(value = "是否隐藏")
  private Boolean isHide = false;

  @ApiModelProperty(value = "是否统计小计")
  private Boolean showSubtotal=false;

  @ApiModelProperty(value = "度量在列表第几位")
  private int positionIndex;
}
