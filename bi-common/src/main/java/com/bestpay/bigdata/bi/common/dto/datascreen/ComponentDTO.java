package com.bestpay.bigdata.bi.common.dto.datascreen;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("组件信息")
public class ComponentDTO {

  @ApiModelProperty(value = "组件code")
  private String componentCode;

  @ApiModelProperty(value = "组件名称")
  private String componentName;

  @ApiModelProperty(value = "组件类型")
  private String componentType;

  @ApiModelProperty(value = "组件信息")
  private Object componentInfo;

  public static ComponentDTO createComponentDTO(String componentCode,String componentName,String componentType,Object componentInfo){
    ComponentDTO componentDTO = new ComponentDTO();
    componentDTO.setComponentCode(componentCode);
    componentDTO.setComponentName(componentName);
    componentDTO.setComponentType(componentType);
    componentDTO.setComponentInfo(componentInfo);
    return componentDTO;
  }
}
