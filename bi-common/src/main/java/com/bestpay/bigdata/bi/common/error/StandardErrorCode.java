package com.bestpay.bigdata.bi.common.error;

import static com.bestpay.bigdata.bi.common.error.ErrorType.*;

public enum StandardErrorCode implements ErrorCodeSupplier {

    BAD_REQUEST("10001", USER_ERROR),
    AUTH_PERMISSION_DENIED("10002", USER_ERROR),
    DATA_PERMISSION_DENIED("10003", USER_ERROR),
    UNSUPPORTED_DATE_TYPE_ERROR("10004", ErrorType.USER_ERROR),
    UNSUPPORTED_TIME_TYPE_ERROR("10005", ErrorType.USER_ERROR),
    UNSUPPORTED_PICKER_TYPE_ERROR("10006", ErrorType.USER_ERROR),

    SYSTEM_INTERNAL_ERROR("20001", INTERNAL_ERROR),
    ASSERT_UTIL_DEFAULT_ERROR("00001",BUSINESS_ERROR),
    FILE_NOT_FOUND_ERROR("20002", INTERNAL_ERROR),
    FILE_DELETE_ERROR("20003", INTERNAL_ERROR),
    ZIP_PARAM_ERROR("20004", INTERNAL_ERROR),

    METADATA_SERVICE_REQUEST_ERROR("30001", EXTERNAL_ERROR),
    SENSITIVE_SERVICE_REQUEST_ERROR("30002", EXTERNAL_ERROR),
    ;

    private static final String PREFIX = "STANDARD_";

    private final ErrorCode errorCode;

    StandardErrorCode(String code, ErrorType type) {
        errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
