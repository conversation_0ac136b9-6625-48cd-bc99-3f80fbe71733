package com.bestpay.bigdata.bi.common.api;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Author: dengyanwei
 * @CreateDate: 2021/3/10 10:42
 */
public interface RedisService {
    List<Object> getValuesByMultiKey(String key, List<String> hashKeys);

    /**
     * del key
     *
     * @param key will delete
     */
    void del(String... key);
    /**
     * get a key value
     *
     * @param key redis key
     * @param <T> generic
     * @return Optional value
     */
    <T> Optional<T> getObj(String key);
    /**
     * Get value for given {@code key} from the hash at the bound key.
     *
     * @param key redis key
     * @param hashMapKey redis hash key
     * @param <T> generic
     * @return Optional value
     */
    <T> Optional<T> getHashObj(String key, String hashMapKey);

    /**
     * get hash object only by key
     *
     * @param key key
     * @param <T> return object type
     * @return result
     */
    <T> Optional<T> getHashObj(String key);

    /**
     * Get values for given {@code fields} from the hash at the bound key.
     *
     * @param key redis key
     * @param fields redis hash keys
     * @param <T> generic
     * @return Optional value
     */
    <T> Optional<List<T>> getHashObjects(String key, List<Object> fields);

    <T,R> Optional<Map<T,R>> getHashObjects(String key);

    /**
     * Delete given hash {@code keys} at the bound key.
     *
     * @param key redis key
     * @param hashMapKey redis hash keys
     */
    void delHashObj(String key, String hashMapKey);
    /**
     * Set the {@code value} and expiration {@code expireTime} for {@code key}.
     *
     * @param key must not be {@literal null}.
     * @param obj must not be {@literal null}.
     * @param expireTime the key expiration timeout.
     * @param timeUnit must not be {@literal null}.
     * @return set result
     */
    boolean setObj(String key, Object obj, long expireTime, TimeUnit timeUnit);
    /**
     * Set {@code obj} for {@code key}.
     *
     * @param key redis key
     * @param obj redis value
     * @return set result
     */
    boolean setObj(String key, Object obj);
    /**
     * Set {@code obj} for {@code key}, only if {@code key} does not exist.
     *
     * @param key redis key
     * @param obj redis value
     * @return set result
     */
    boolean setNxObj(String key, Object obj);
    /**
     * Set multiple hash fields to multiple values using data provided in {@code hashMap}.
     *
     * @param key redis key
     * @param hashMap redis hash
     * @return set result
     */
    boolean setHashObj(String key, Map hashMap);
    /**
     * Get entire hash stored at {@code key}.
     *
     * @param key must not be {@literal null}.
     * @param <T> generic
     * @param <S> generic
     * @return {@literal null} when used in pipeline / transaction.
     */
    <T, S> Optional<Map<T, S>> entries(String key);
    /**
     * Set the {@code value} of a hash {@code hashKey}.
     *
     * @param key must not be {@literal null}.
     * @param field must not be {@literal null}.
     * @param obj redis value
     * @return set result
     */
    boolean setHashObj(String key, String field, Object obj);
    /**
     * Set the {@code value} of a hash {@code hashKey}.
     *
     * @param key must not be {@literal null}.
     * @param hashMap must not be {@literal null}.
     * @param expireTime expireTime
     * @param timeUnit unit
     * @return set result
     */
    boolean setHashObj(String key, Map hashMap, long expireTime, TimeUnit timeUnit);


    /**
     *
     * @param key
     * @param innerKey
     * @param object
     * @param expireTime
     * @param timeUnit
     * @return
     */
    boolean setHashObj(String key, String innerKey, Object object, long expireTime, TimeUnit timeUnit);


    List<Object> getValues(String key);

    /**
     * rename key
     *
     * @param oldKey old
     * @param newKey new
     * @param useKeyPrefix key prefix use flag
     */
    void rename(String oldKey, String newKey, boolean useKeyPrefix);
    /**
     * Increment a floating point number value stored as string value under {@code key} by {@code by}.
     *
     * @param key must not be {@literal null}.
     * @return {@literal null} when used in pipeline / transaction.
     */
    double decr(String key);
    /**
     * Increment a floating point number value stored as string value under {@code key} by {@code by}.
     *
     * @param key must not be {@literal null}.
     * @param by delta
     * @return {@literal null} when used in pipeline / transaction.
     */
    double decr(String key, double by);
    /**
     * Increment a floating point number value stored as string value under {@code key} by {@code
     * delta}.
     *
     * @param key must not be {@literal null}.
     * @param by delta
     * @return {@literal null} when used in pipeline / transaction.
     */
    double incr(String key, double by);

    /**
     * Atomically increment by one the current value.
     *
     * @param key
     * @return the updated value.
     */
    Long getIncrementNum(String key);

    /**
     * Initialize self-increasing id
     *
     * @param key
     * @param value
     */
    void setIncr(String key, long value);

    /**
     * Increment an integer value stored as string value under {@code key} by one..
     *
     * @param key must not be {@literal null}.
     * @return {@literal null} when used in pipeline / transaction.
     */
    Long incr(String key);

    /**
     * expire key
     *
     * @param key key
     * @param time expire time
     * @param timeUnit unit
     */
    void expire(String key, long time, TimeUnit timeUnit);

    /**
     * Add given {@code values} to set at the bound key.
     *
     * @param key redis key
     * @param value redis value
     * @return {@literal null} when used in pipeline / transaction.
     */
    boolean sAdd(String key, Object value);


    /**
     * pop
     * @param key
     * @return
     */
    Object sPop(String key);

    /**
     * Remove given {@code values} from set at the bound key and return the number of removed
     * elements.
     *
     * @param key redis key
     * @param value redis value
     * @return {@literal null} when used in pipeline / transaction.
     */
    boolean sRemove(String key, String value);
    /**
     * Check if set at the bound key contains {@code value}.
     *
     * @param key redis key
     * @param value redis value
     * @return {@literal null} when used in pipeline / transaction.
     */
    boolean sIsMember(String key, String value);
    /**
     * Get size of set at the bound key.
     *
     * @param key redis key
     * @return {@literal null} when used in pipeline / transaction.
     */
    Long sCard(String key);

    <T> Object get(String key);

    boolean hashKey(String key);

    Set<String> keys(String keyPrefix);

    /**
     *
     * @param key 固定字符串
     * @param expire 锁的过期时间
     * @return
     */
    boolean lock(String key, long expire);

    /**
     * 释放锁
     * @param key
     */
    void unlock(String key);
}

