package com.bestpay.bigdata.bi.common.enums;

import lombok.Getter;

/**
 * <AUTHOR> @Description: 执行状态
 * @date 2021/11/16
 */
public enum StatusCodeEnum {
    /**
     * 已上线
     */
    ONLINE(0,"已上线"),
    /**
     * 已下线
     */
    OFFLINE(1,"已下线"),
    /**
     * 删除
     */
    DELETE(9,"删除");

    @Getter
    private final int code;
    @Getter
    private final String msg;


    StatusCodeEnum(int code, String msg) {
        this.code = code;
        this.msg=msg;
    }

    public static String getNameByCode(Integer appEmbed){
        if(appEmbed==null){
            return "";
        }

        for (StatusCodeEnum value : StatusCodeEnum.values()) {
            if(value.getCode()==appEmbed){
                return value.getMsg();
            }
        }

        return "";
    }

}
