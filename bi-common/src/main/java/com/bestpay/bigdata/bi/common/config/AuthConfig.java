package com.bestpay.bigdata.bi.common.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/4/6 9:56
 **/
@ConfigurationProperties(prefix = "authorization")
@Component("authConfig")
@RefreshScope
@Slf4j
@Data
public class AuthConfig {
    /**
     * 短信发送URL
     */
    private String smsSendUrl;
    /**
     * 智加-组织列表
     */
    private String newOrgListUrl;

    /**
     * 智加-用户组列表
     */
    private String aiPlusUserGroupList;

    /**
     * 智加-当前登录用户信息 --新
     */
    private String newCurrentUserUrl;

    /**
     * 通过账户获取用户信息的接口路径--新
     */
    private String newUserInfoByMultipleConditionsUrl;

    /**
     * 通过账户精确获取用户信息的接口路径--新
     */
    private String newUserInfoByAccountUrl;

    /**
     * 通过账户精确获取用户信息的接口路径--新
     */
    private String newUserInfoByOneIdUrl;

    /**
     * 通过渠道账号精确获取用户信息的接口路径--新
     */
    private String newUserInfoByChannelAccountUrl;
    /**
     * 智加-当前登录用户权限
     */
    private String newPermissionUrl;

    /**
     * 智加-权限校验 --新
     */
    private String checkPermissionUrl;
    private String downloadAppInsertUrl;
    private String metadataUrl;
    private String metadataSearchTableUrl;
    private String metadataQueryFieldUrl;
    private String metadataQueryDatabaseUrl;
    private String metadataQueryTableUrl;
    private String metadataHiveUdfUrl;
    private String metadataHiveResourceCheckUrl;
    private String metadataSecurityLevelUrl;
    private String metadataAuthorityDatabaseUrl;
    private String metadataCkCreateTableUrl;
    private String metadataCkDropTableUrl;
    private String desktopUrl;
    private String datasbpUserReportsUrl;
    private String datasbpReportUrl;
    private String innerDatasbpReportUrl;
    private String openapiQueryStatusUrl;
    private String openapiFileUploadUrl;
    private String openapiTextQueryUrl;
    private String openapiBatchQueryUrl;
    private String openapiQueryResultUrl;
    private String openapiCancelQueryUrl;

    //for dashboard AI analysis reporting
    private String dashboardAIAnalysisUrl;
    private String dashboardAIAnalysisAPIKey;
    private String dashboardAIAnalysisAppID;

    //for AI find report
    private String findReportAIUrl;
    private String findReportAIAPIKey;
    private String findReportAIAppID;
}
