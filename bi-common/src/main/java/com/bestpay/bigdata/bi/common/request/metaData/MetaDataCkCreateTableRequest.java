package com.bestpay.bigdata.bi.common.request.metaData;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: den<PERSON><PERSON><PERSON>
 * @CreateDate: 2022/6/14 9:38
 */
@Data
public class MetaDataCkCreateTableRequest implements Serializable {
  private static final long serialVersionUID = 4375105952446862967L;
  private String sourceName;
  private String dbName;
  private String tableName;
  private String cluster;
  private String tableNameCn;
  private String owner;
  private List<Column> columns;

  @Data
  public static class Column implements Serializable{
    private static final long serialVersionUID = 5165659861718134431L;
    private String name;
    private String type;
    private String nameCn;
    private Boolean isInPrimaryKey;
  }
}
