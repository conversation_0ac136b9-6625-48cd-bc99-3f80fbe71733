package com.bestpay.bigdata.bi.common.error;

public enum ReportErrorCode implements ErrorCodeSupplier {

    REPORT_NOT_EXISTS("00001", ErrorType.BUSINESS_ERROR),
    REPORT_INDEX_NAME_EXISTS("00002", ErrorType.BUSINESS_ERROR),
    REPORT_DOWNLOAD_RESTRICT("00003", ErrorType.BUSINESS_ERROR),
    REPORT_DATA_EMPTY("00004", ErrorType.BUSINESS_ERROR),
    REPORT_ONLY_MANAGER_OPERATE("00005", ErrorType.BUSINESS_ERROR),
    REPORT_CARD_NOT_EXISTS("00006", ErrorType.BUSINESS_ERROR),
    REPORT_PARSE_ERROR("00007", ErrorType.BUSINESS_ERROR),
    REPORT_QUERY_FAIL("00008", ErrorType.BUSINESS_ERROR),
    REPORT_DOWNLOAD_EXCEPTION("00009", ErrorType.BUSINESS_ERROR),
    REPORT_REQUEST_INDEX_DATA_SET_ERROR("00010", ErrorType.BUSINESS_ERROR),
    REPORT_DATASET_STATUS_ERROR("00011", ErrorType.BUSINESS_ERROR),
    REPORT_FILTER_MUST_HAVE_DEFAULT("00012", ErrorType.BUSINESS_ERROR),
    REPORT_DATASET_EMPTY("00013", ErrorType.BUSINESS_ERROR),
    REPORT_WARN_CONFIG_NAME_REPEATED("00014", ErrorType.BUSINESS_ERROR),
    REPORT_WARN_CONFIG_NOT_EXIST("00015", ErrorType.BUSINESS_ERROR),
    REPORT_INVALID_WEEKLY_INFO("00016", ErrorType.BUSINESS_ERROR),
    REPORT_WARN_BASE_INFO_CHANGED("00017", ErrorType.BUSINESS_ERROR),

    REPORT_DIRECTORY_NAME_NULL_ERROR("10001", ErrorType.USER_ERROR),
    REPORT_DIRECTORY_NAME_LENGTH_ERROR("10002", ErrorType.USER_ERROR),
    REPORT_DIRECTORY_SAME_NAME_ERROR("10003", ErrorType.USER_ERROR),
    REPORT_DIRECTORY_NOT_EMPTY_ERROR("10004", ErrorType.USER_ERROR),
    REPORT_CONF_INFO_CHANGED("10005", ErrorType.BUSINESS_ERROR),


    ;

    private static final String PREFIX = "REPORT_";

    private final ErrorCode errorCode;

    ReportErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
