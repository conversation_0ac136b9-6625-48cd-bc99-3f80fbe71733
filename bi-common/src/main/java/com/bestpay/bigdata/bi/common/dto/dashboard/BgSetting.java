package com.bestpay.bigdata.bi.common.dto.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "背景信息设置")
public class BgSetting {

    /** 背景色 */
    @ApiModelProperty(value = "背景色")
    private String bgColor;

    /** 背景图片 */
    @ApiModelProperty(value = "背景图片")
    private String bgImage;

    /** 外网访问 */
    @ApiModelProperty(value = "外网访问")
    private String otherBgImage;

    /** 文件名称 */
    @ApiModelProperty(value = "文件名称")
    private String bgImageFileName;
}
