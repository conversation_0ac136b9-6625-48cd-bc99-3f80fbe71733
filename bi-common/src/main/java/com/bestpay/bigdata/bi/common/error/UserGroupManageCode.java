package com.bestpay.bigdata.bi.common.error;

/**
 * 用户组错误码
 * @date: 2019/07/01
 * @author: lm
 */
public enum UserGroupManageCode implements ErrorCodeSupplier {
    /**
     * 不存在
     */
    NOT_EXISTS("00001",ErrorType.BUSINESS_ERROR),
    EXISTS_SUB_DATA("00002",ErrorType.BUSINESS_ERROR),

    /**
     * 参数校验错误
     */
    PARAM_CHECK("00003", ErrorType.BUSINESS_ERROR),

    ;

    private static final String PREFIX = "UG_";

    private final ErrorCode errorCode;

    UserGroupManageCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
