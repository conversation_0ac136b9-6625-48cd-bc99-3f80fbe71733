package com.bestpay.bigdata.bi.common.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 报表加工 -- 新建报表 -- 筛选控件
 * <AUTHOR>
 * @create 2023-03-20-16:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "筛选控件")
public class ReportScreeningConditionDTO {

    // showTypeName 字段展示类型
    @ApiModelProperty(value = "字段展示类型")
    private String fieldType;

    // 时间筛选类型 范围还是选择
    @ApiModelProperty(value = "时间筛选类型")
    private String filterDateType;

    // 日期默认值
    @ApiModelProperty(value = "日期默认值")
    private String dateType;

    // 字符类型默认值
    @ApiModelProperty(value = "字符类型默认值")
    private String stringValue;

    // 字符选择默认值，或者数值默认值
    @ApiModelProperty(value = "字符选择默认值")
    private List<String> values;

    @ApiModelProperty(value = "日期选择器ID")
    private Long datePickerId;

    /**
     * 筛选类型
     */
    @ApiModelProperty(value = "筛选类型")
    private String filterType;
    /**
     * 时间类型
     */
    @ApiModelProperty(value = "时间类型")
    private String timeType;

    /**
     * 字段显示类型名称
     */
    @ApiModelProperty(value = "字段显示类型名称")
    private String showTypeName;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
    private List<String> defaultValues;

    // 排序字段
    @ApiModelProperty(value = "排序字段")
    private String sort;

    /**
     * 是否加密
     */
    @ApiModelProperty(value = "是否加密")
    private Boolean isEncrypt;

    /**
     * 是否包含本周期
     */
    @ApiModelProperty(value = "是否包含本周期")
    private Boolean includeCurrent;

    /**
     * 默认类型
     */
    @ApiModelProperty(value = "默认类型")
    private String defaultType;
}
