package com.bestpay.bigdata.bi.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * 仪表板类型 0：自建 1：嵌入
 */
public enum DashboardSourceTypeEnum {
    /**
     * BI 自己的
     */
    BI(0,"自建"),
    /**
     * 来源于tableau
     */
    TABLEAU(1,"嵌入"),;

    @Getter
    private final int code;
    @Getter
    private final String msg;

    DashboardSourceTypeEnum(int code, String msg) {
        this.code = code;
        this.msg=msg;
    }
}
