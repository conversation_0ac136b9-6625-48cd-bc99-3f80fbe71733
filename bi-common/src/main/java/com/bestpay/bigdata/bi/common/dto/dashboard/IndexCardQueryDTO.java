package com.bestpay.bigdata.bi.common.dto.dashboard;

import java.io.Serializable;
import java.util.List;

import com.bestpay.bigdata.bi.common.enums.IndexCardTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class IndexCardQueryDTO implements Serializable {
    private static final long serialVersionUID = 3728415161519923371L;
    private Long dashboardId;
    private String indexCardType;
    private List<Long> idList;



    private Long datasetId;


    public static IndexCardQueryDTO build(Long datasetId, IndexCardTypeEnum cardTypeEnum, List<Long> idList) {
        IndexCardQueryDTO cardQuery = new IndexCardQueryDTO();
        cardQuery.setIdList(idList);
        cardQuery.setIndexCardType(cardTypeEnum.getCode());
        cardQuery.setDatasetId(datasetId);
        return cardQuery;
    }


}
