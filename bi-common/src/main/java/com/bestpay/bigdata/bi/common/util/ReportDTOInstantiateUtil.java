package com.bestpay.bigdata.bi.common.util;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.CommonCoordinateAxisConfigRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.CoordinateAxisConfigRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.FunnelChartCoordinateAxisConfigRequest;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewReportCardDTO;
import com.bestpay.bigdata.bi.common.dto.datascreen.component.ReportComponentInfoDTO;

/**
 * <AUTHOR>
 */
public class ReportDTOInstantiateUtil {

  public static final String COORDINATE_AXIS_CONFIG_REQUEST = "coordinateAxisConfigRequest";

  /**
   * 实例化仪表板图表卡片
   * @param json
   * @return
   */
  public static NewReportCardDTO toNewReportCardDTO(String json){
    if(StringUtil.isEmpty(json)){
      return null;
    }

    NewReportCardDTO cardDTO = json==null ? null : JSONUtil.toBean(json, NewReportCardDTO.class);

    Object config = com.alibaba.fastjson.JSONObject
        .parseObject(json).get(COORDINATE_AXIS_CONFIG_REQUEST);

    if(config!=null) {
      cardDTO.setCoordinateAxisConfigRequest(getInstantiate(cardDTO.getChartType(), config.toString()));
    }

    return cardDTO;
  }

  /**
   * 实例化数据大屏图表卡片
   * @param json
   * @return
   */
  public static ReportComponentInfoDTO toReportComponentInfoDTO(String json){
    if(StringUtil.isEmpty(json)){
      return null;
    }

    ReportComponentInfoDTO cardDTO = json==null ? null : JSONUtil.toBean(json, ReportComponentInfoDTO.class);

    Object config = com.alibaba.fastjson.JSONObject
        .parseObject(json).get(COORDINATE_AXIS_CONFIG_REQUEST);

    if(config!=null) {
      cardDTO.setCoordinateAxisConfigRequest(getInstantiate(cardDTO.getChartType(), config.toString()));
    }

    return cardDTO;
  }

  /**
   * 实例化图表样式配置
   * @param chartType
   * @param config
   * @return
   */
  // todo 放到chart 里面
  public static CommonCoordinateAxisConfigRequest getInstantiate(Integer chartType, String config) {

    if(ChartTypeEnum.FUNNEL_CHART.getCode().equals(chartType)){
      FunnelChartCoordinateAxisConfigRequest funnelChart = JSONUtil.toBean(config, FunnelChartCoordinateAxisConfigRequest.class);
      return funnelChart;
    }

    return JSONUtil.toBean(config, CoordinateAxisConfigRequest.class);
  }
}
