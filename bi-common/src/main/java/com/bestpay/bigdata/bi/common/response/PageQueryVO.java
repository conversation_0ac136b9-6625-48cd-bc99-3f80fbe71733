package com.bestpay.bigdata.bi.common.response;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "分页信息")
public class PageQueryVO<T> {

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "总数量")
    private Long totalSize;

    @ApiModelProperty(value = "数据")
    private List<T> data;

    public PageQueryVO(){
    }

    public PageQueryVO(List<T> data){
        this.data = data;
    }

    public static PageQueryVO empty(){
        return new PageQueryVO(Lists.newArrayList());
    }
}
