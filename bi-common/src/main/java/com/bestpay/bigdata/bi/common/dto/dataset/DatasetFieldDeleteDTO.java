package com.bestpay.bigdata.bi.common.dto.dataset;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DatasetFieldDeleteDTO implements Serializable {

    private static final long serialVersionUID = -4719174667103758663L;
    private String datasourceType;
    private String datasourceName;
    private String databaseName;
    private String tableName;
    private List<String> idList;
}
