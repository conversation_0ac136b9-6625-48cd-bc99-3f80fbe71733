package com.bestpay.bigdata.bi.common.error;

public enum ChartErrorCode implements ErrorCodeSupplier {
    COMPUTE_ERROR("00001",ErrorType.BUSINESS_ERROR),
    ONLY_CK_SOURCE_ERROR("00002",ErrorType.BUSINESS_ERROR),
    ONLY_LIST_TABLE_HIDDEN("00003",ErrorType.BUSINESS_ERROR),
    WATER_FALL_DATA_CONVERT_ERROR("00004",ErrorType.BUSINESS_ERROR),
    WATER_FALL_CONF_ERROR("00005",ErrorType.BUSINESS_ERROR),
    TRANSPOSITION_TABLE_CONF_ERROR("00006",ErrorType.BUSINESS_ERROR),
    SCATTER_CHART_DATA_ERROR("00007",ErrorType.BUSINESS_ERROR),
    RADAR_CHART_INIT_ERROR("00008",ErrorType.BUSINESS_ERROR),
    RADAR_CHART_CONF_ERROR("00009",ErrorType.BUSINESS_ERROR),
    FUNNEL_CHART_DATA_ERROR("00010",ErrorType.BUSINESS_ERROR),
    CONTRAST_TABLE_CHART_DATA_ERROR("00011",ErrorType.BUSINESS_ERROR),
    CONTRAST_TABLE_CHART_CONF_ERROR("00012",ErrorType.BUSINESS_ERROR),

    ;

    private static final String PREFIX = "CHART_";

    private final ErrorCode errorCode;

    ChartErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
