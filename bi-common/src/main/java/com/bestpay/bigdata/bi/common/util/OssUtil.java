package com.bestpay.bigdata.bi.common.util;

import com.bestpay.bigdata.bi.common.enums.FileSystemTypeEnum;
import com.bestpay.bigdata.bi.common.error.SftpErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.oss.OssService;
import com.bestpay.bigdata.bi.common.oss.OssServiceFactory;
import com.bestpay.bigdata.bi.common.oss.wrap.MiniIOOssContext;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Slf4j
public class OssUtil {

    /**
     * 文件上传
     *
     * @param ossServiceFactory OSS 服务工厂
     * @param filePath          本地文件路径
     * @param ttlDays           文件在存储系统中存活的天数（可选）
     * @param model             模式标识（业务相关）
     * @return 上传后路径或 URL
     */
    public static String upload(OssServiceFactory ossServiceFactory,
                         String filePath,
                         Integer ttlDays,
                         Integer model) {
        Path path = Paths.get(filePath);

        if (!Files.exists(path)) {
            throw new BiException(SftpErrorCode.UPLOAD_ERROR, "上传失败：文件不存在！");
        }

        try (InputStream inputStream = Files.newInputStream(path)) {
            long size = Files.size(path);
            String suffix = getFileSuffix(path.getFileName().toString());

            OssService ossService = ossServiceFactory.getOssServiceByType();
            FileSystemTypeEnum fileSystemType = ossService.getFileSystemType();

            log.info("上传文件：filePath={}, fileSystemType={}, suffix={}", filePath, fileSystemType, suffix);

            MiniIOOssContext ossContext = new MiniIOOssContext(fileSystemType, inputStream, ttlDays, suffix, model, size);
            String remotePath = ossService.upload(ossContext);
            ossContext.setMiniIoPath(remotePath);

            return fileSystemType == FileSystemTypeEnum.MINIO_ORIGINAL
                    ? ossService.parseHttpUrl(ossContext)
                    : remotePath;
        } catch (Exception e) {
            log.error("文件上传异常：{}", LogUtil.getStackTrace(e));
            throw new BiException(SftpErrorCode.UPLOAD_ERROR, "上传失败！");
        }
    }

    /**
     * 文件下载
     *
     * @param ossServiceFactory OSS 服务工厂
     * @param remotePath        文件在 OSS 中的路径
     * @return 文件字节数组
     */
    public static byte[] download(OssServiceFactory ossServiceFactory,
                           String remotePath) {
        try {
            OssService ossService = ossServiceFactory.getOssServiceByType();
            FileSystemTypeEnum fileSystemType = ossServiceFactory.getCurrentSystemUseFileSystemType();

            log.info("下载文件：fileSystemType={}, path={}", fileSystemType, remotePath);

            if (FileSystemTypeEnum.MINIO_ORIGINAL.equals(fileSystemType)) {
                if (remotePath.lastIndexOf("/") != -1) {
                    remotePath = remotePath.substring(remotePath.lastIndexOf("/") + 1);
                    remotePath = new String(Base64Util.decryBASE64(remotePath));
                }
            }

            MiniIOOssContext ossContext = new MiniIOOssContext(fileSystemType, remotePath);
            return ossService.download(ossContext);
        } catch (Exception e) {
            log.error("文件下载异常：{}", LogUtil.getStackTrace(e));
            throw new BiException(SftpErrorCode.DOWNLOAD_ERROR, "下载失败！");
        }
    }

    /**
     * 提取文件后缀名
     */
    private static String getFileSuffix(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        return (dotIndex != -1) ? fileName.substring(dotIndex + 1) : "";
    }

}
