package com.bestpay.bigdata.bi.common.util;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 下载文件工具类。
 * <AUTHOR>
 */
@Slf4j
public class DownloadFileUtil {

  /**
   * 根据文件路径生成返回。
   *
   * @param filename  文件名。
   * @param response 重复次数。
   * @return 生成的response。
   */
  public static void setHeader(String filename, HttpServletResponse response) {

    try {
      //response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
      response.setContentType("application/octet-stream");
      response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
      response.addHeader("charset", "utf-8");
      response.addHeader("Pragma", "no-cache");
      String encodeName = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());
      response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);

    } catch (Exception e) {
      log.warn("setHeader exception",e);
    }
  }
  public static Map<String,String> getDirectoryAndFileName(String filePath){
    int i = StrUtil.lastIndexOfIgnoreCase(filePath, "/");
    int length = filePath.length();
    String directory = StrUtil.sub(filePath, 0, i);
    String fileName = StrUtil.sub(filePath, i+1, length);
    if (StrUtil.isBlank(directory)) {
      directory = "/";
    }
    Map<String, String> map = MapUtil.newHashMap();
    map.put("directory",directory);
    map.put("fileName",fileName);
    log.info("directory is {};fileName is {}",directory,fileName);
    return map;
  }
  /**
   * 根据文件路径生成返回。
   *
   * @param buffer  文件路径及文件名。
   * @param response 重复次数。
   * @return 生成的response。
   */
  public static void downloadByte(byte[] buffer, HttpServletResponse response) throws IOException {
    ServletOutputStream outputStream=null;
    try {
      outputStream=response.getOutputStream();
      outputStream.write(buffer);
      outputStream.flush();
    } catch (Exception e) {
      throw e;
    }finally {
      try {
        if(outputStream!=null){
          outputStream.close();
        }
      } catch (Exception e) {
        log.error("close outputStream error",e);
      }
    }
  }
  /**
   * 根据文件路径生成返回。
   *
   * @param filePath  文件路径及文件名。
   * @param response 重复次数。
   * @return 生成的response。
   */
  public static HttpServletResponse generateResponse(String filePath, HttpServletResponse response) {
    try {
      // path是指欲下载的文件的路径。
      File file = new File(filePath);
      // 取得文件名。
      String filename = file.getName();
      // 取得文件的后缀名。
      String ext = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
      // 以流的形式下载文件。
      InputStream fis = new BufferedInputStream(new FileInputStream(filePath));
      byte[] buffer = new byte[fis.available()];
      fis.read(buffer);
      fis.close();
      // 清空response
      response.reset();
      // 设置response的Header
      response.addHeader("Content-Disposition", "attachment;filename=" + new String(filename.getBytes()));
      response.addHeader("Content-Length", "" + file.length());
      OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
      response.setContentType("application/octet-stream");
      toClient.write(buffer);
      toClient.flush();
      toClient.close();
    } catch (Exception e) {
      throw new BusinessException(e.getMessage());
    }
    return response;
  }
}
