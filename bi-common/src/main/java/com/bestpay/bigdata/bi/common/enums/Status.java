package com.bestpay.bigdata.bi.common.enums;

import static com.bestpay.bigdata.bi.common.enums.CodeEnum.ENUM_CODE_ERROR;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.google.common.collect.Sets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> @Description: 执行状态
 * @date 2021/11/16
 */
public enum Status {
    /**
     * 提交
     */
    QUERY_SUBMIT(0, "提交"),
    /**
     * 成功
     */
    QUERY_SUCCESS(1, "成功"),
    /**
     * 失败
     */
    QUERY_FAILED(2, "失败"),
    /**
     * 取消
     */
    QUERY_CANCEL(3, "取消"),
    /**
     * 执行时间
     */
    QUERY_EXECUTING(4, "执行中"),
    /**
     * 超时
     */
    QUERY_TIMEOUT(5, "超时");

    @Getter
    private final int code;

    @Getter
    private final String desc;

    private static final Map<Integer, Status> ENUM_VALUE_MAP;
    private static final Set<Status> DONE_STATUS_SET;

    Status(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    static {
        //结束状态
        DONE_STATUS_SET = Sets.newHashSet(QUERY_SUCCESS, QUERY_FAILED, QUERY_CANCEL, QUERY_TIMEOUT);
        Map<Integer, Status> valueMap = new HashMap<>();
        Status[] statuses = Status.values();

        for (Status status : statuses) {
            valueMap.put(status.code, status);
        }
        ENUM_VALUE_MAP = Collections.unmodifiableMap(valueMap);
    }

    public static Status getStatus(int code) {
        Status status = ENUM_VALUE_MAP.get(code);
        if (ObjectUtils.isEmpty(status)) {
            throw new BusinessException(ENUM_CODE_ERROR);
        }
        return status;
    }

    public static boolean isDone(Status status) {
        return DONE_STATUS_SET.contains(status);
    }


}
