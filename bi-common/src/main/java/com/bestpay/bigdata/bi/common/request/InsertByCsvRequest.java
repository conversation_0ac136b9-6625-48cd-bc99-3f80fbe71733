package com.bestpay.bigdata.bi.common.request;

import com.bestpay.bigdata.bi.common.util.ReqNullable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/23
 */
@Data
public class InsertByCsvRequest implements Serializable {
    private String username;
    @ApiModelProperty(value = "查询引擎",required = true,allowableValues ="presto,hive,kylin,spark_sql,clickhouse")
    private String sqlEngine;
    @ApiModelProperty(value = "数据源",required = true,allowableValues ="hive_11,hive_36,ck_1,ck_2,ck_3,ck_4,ck_5")
    private String databaseSource;
    @ApiModelProperty(value = "访问系统",required = true,allowableValues ="bi,fengkong,edm")
    private String requestSystem;

    private String dbName;

    private String tableName;

    private List<ColumnRequest> csvColumnList;

    @ReqNullable
    private List<ColumnRequest> constantColumnList;

    @Data
    public static class ColumnRequest implements Serializable{
        private String columnName;

        private String columnType;

        // 列常量
        // 允许csv文件中不存在常量列的数据，以常量列参数方式传输，open-api会自动为每行数据添加该常量列
        @ReqNullable
        private String columnConstant;
    }
}
