package com.bestpay.bigdata.bi.common.enums;

import java.util.Arrays;

public enum ReportResourceTypeEnum {
  DATASCREEN("datascreen", "数据大屏"),
  REPORT("report", "报表");

  private final String code;
  private final String message;

  ReportResourceTypeEnum(String code, String message) {
    this.code = code;
    this.message = message;
  }

  public String getCode() {
    return code;
  }

  public String getMessage() {
    return message;
  }

  public static ReportResourceTypeEnum getReportResourceType(String code){
    return Arrays.stream(values()).filter(rrt->rrt.code.equals(code)).findFirst().orElse(null);
  }
}
