package com.bestpay.bigdata.bi.common.error;

/**
 * 用户相关异常枚举
 */
public enum UserErrorCode implements ErrorCodeSupplier {

    ILLEGAL_ACCOUNT("00001", ErrorType.BUSINESS_ERROR);

    private static final String PREFIX = "USER_";

    private final ErrorCode errorCode;

    UserErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
