package com.bestpay.bigdata.bi.common.exception;

import com.bestpay.bigdata.bi.common.enums.CodeEnum;

/**
 * 元数据请求异常
 * <AUTHOR>
 */
public class MetaDataRequestException extends BaseBiException {

    public MetaDataRequestException(String code, String message) {
        super(code,message );
    }

    public MetaDataRequestException(String message) {
        super(message);
    }

    public MetaDataRequestException(CodeEnum codeEnum) {
        super(codeEnum);
    }
}