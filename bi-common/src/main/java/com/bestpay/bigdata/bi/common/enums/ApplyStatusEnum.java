package com.bestpay.bigdata.bi.common.enums;


import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 申请状态
 * @Author: dengyanwei
 * @CreateDate: 2021/12/17 14:52
 */
@Getter
public enum ApplyStatusEnum {
  /**文件生成中*/
  FILE_GENERATING(1,"文件生成中"),
  /**审批中*/
  FILE_AUDITING(2,"审批中"),
  /**已完成*/
  FILE_COMPLETED(3,"已完成"),
  /**拒绝*/
  FILE_REJECT(4,"拒绝"),
  /**数据异常*/
  DATA_EXCEPTION(5,"数据异常");

  private final int code;
  private final String message;

  private static final Map<Integer, ApplyStatusEnum> ENUM_VALUE_MAP;

  ApplyStatusEnum(int code, String message) {
    this.code = code;
    this.message = message;
  }

  static {
    //结束状态
    Map<Integer, ApplyStatusEnum> valueMap = new HashMap<>();
    ApplyStatusEnum[] applyStatusEnums = ApplyStatusEnum.values();

    for (ApplyStatusEnum applyStatusEnum : applyStatusEnums) {
      valueMap.put(applyStatusEnum.code, applyStatusEnum);
    }
    ENUM_VALUE_MAP = Collections.unmodifiableMap(valueMap);
  }

  public static ApplyStatusEnum getStatus(int code) {
    ApplyStatusEnum applyStatusEnum = ENUM_VALUE_MAP.get(code);
    if (ObjectUtils.isEmpty(applyStatusEnum)) {
      //throw new BusinessException(ENUM_CODE_ERROR);
    }
    return applyStatusEnum;
  }

  public static String getValue(Integer type) {
    ApplyStatusEnum[] applyStatusEnums = values();
    for (ApplyStatusEnum applyStatusEnum : applyStatusEnums) {
      if (Objects.equals(applyStatusEnum.getCode(), type)) {
        return applyStatusEnum.getMessage();
      }
    }
    return null;
  }

  public static Integer getKey(String desc) {
    ApplyStatusEnum[] applyStatusEnums = values();
    for (ApplyStatusEnum applyStatusEnum : applyStatusEnums) {
      if (StrUtil.equals(applyStatusEnum.getMessage(),desc)) {
        return applyStatusEnum.getCode();
      }
    }
    return null;
  }

}
