package com.bestpay.bigdata.bi.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Data
public class InsertByPathRequest extends InsertByCsvRequest implements Serializable {
    // 文件path
    // minio_path格式为：miniio://logicName=xxx,bucket=xxx,objectName=xxx
    private String filePath;

    @ApiModelProperty(value = "文件类型",required = true,allowableValues ="mini_io, fast_dfs")
    private String fileType;
}