package com.bestpay.bigdata.bi.common.error;

public enum DatasetErrorCode implements ErrorCodeSupplier {

    SHOW_TYPE_NAME_ERROR("00001", ErrorType.BUSINESS_ERROR),
    DATASET_PARAM_ERROR("00002", ErrorType.BUSINESS_ERROR),
    DATASET_CONF_ERROR("00003", ErrorType.BUSINESS_ERROR),
    DATASET_INFO_ERROR("00004", ErrorType.BUSINESS_ERROR),
    DATASET_STATUS_ERROR("00005", ErrorType.BUSINESS_ERROR),
    DATASET_PREVIEW_DATA_EMPTY("00006", ErrorType.BUSINESS_ERROR),
    DATASET_CACHE_EXCEPTION_ERROR("00007", ErrorType.BUSINESS_ERROR),
    DATASET_QUERY_ERROR("00008", ErrorType.BUSINESS_ERROR),
    DATASET_MATE_DATA_ERROR("00009", ErrorType.BUSINESS_ERROR),
    DATASET_RELATION_ERROR("00010", ErrorType.BUSINESS_ERROR),

    DATASET_NOT_FOUND("10001", ErrorType.USER_ERROR),
    DATASET_ELEMENT_CONF_ERROR("10002", ErrorType.USER_ERROR),
    DATASET_DATE_GROUP_TYPE_ERROR("10003", ErrorType.USER_ERROR),
    DATASET_NAME_REPEAT("10004", ErrorType.USER_ERROR),
    FILE_UPLOAD_CREATE_TABLE_EXISTS("10005", ErrorType.USER_ERROR),
    FILE_UPLOAD_DATASET_NOT_FOUND("10006", ErrorType.USER_ERROR),
    FILE_UPLOAD_DATASET_REFERENCE_EXISTS("10007", ErrorType.USER_ERROR),
    FILE_UPLOAD_EXTENSION_NOT_SUPPORTED("10008", ErrorType.USER_ERROR),
    COLUMN_TYPE_NOT_SUPPORTED("10009", ErrorType.USER_ERROR),


    ;

    private static final String PREFIX = "DATASET_";

    private final ErrorCode errorCode;

    DatasetErrorCode(String code, ErrorType type) {
        this.errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode() {
        return errorCode;
    }
}
