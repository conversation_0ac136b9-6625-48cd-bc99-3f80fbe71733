package com.bestpay.bigdata.bi.common.exception;

import com.bestpay.bigdata.bi.common.enums.CodeEnum;

import static com.bestpay.bigdata.bi.common.enums.CodeEnum.SYSTEM_ERROR;

/**
 * <AUTHOR>
 * @date: 2021/12/1
 */
public class BaseBiException extends RuntimeException  {

    protected String code;
    protected CodeEnum codeEnum;

    public BaseBiException(String code, String message) {
        super(message);
        this.code = code;
    }

    public BaseBiException(String message) {
        super(message);
        this.code = SYSTEM_ERROR.code();
    }

    public BaseBiException(CodeEnum codeEnum) {
        super(codeEnum.message());
        this.code = codeEnum.code();
        this.codeEnum = codeEnum;
    }

    public String getCode() {
        return code;
    }

    public CodeEnum getCodeEnum() {
        return codeEnum;
    }
}
