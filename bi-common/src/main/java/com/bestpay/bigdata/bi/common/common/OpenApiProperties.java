package com.bestpay.bigdata.bi.common.common;

import com.bestpay.bigdata.bi.common.exception.BusinessException;

import java.util.Map;

/**
 * ClassName: OpenApiProperties
 * Package: com.bestpay.bigdata.bi.common.common
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/3/11 10:54
 * @Version 1.0
 */
public class OpenApiProperties
{
    public static final String RESULT_TYPE = "result.type";

    public static String getResultType(Map<String, String> properties) {
        if (properties.containsKey(RESULT_TYPE)) {
            String resultType = properties.get(RESULT_TYPE);
            if (ResultTypeEnum.checkValidResultType(resultType)) {
                return resultType;
            } else {
                throw new BusinessException(
                        String.format("非法的Open api properties, key : %s, value : %s", RESULT_TYPE, resultType));
            }
        }
        return ResultTypeEnum.DATA.getCode();
    }
}
