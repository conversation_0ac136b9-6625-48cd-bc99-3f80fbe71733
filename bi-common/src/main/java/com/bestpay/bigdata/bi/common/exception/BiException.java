package com.bestpay.bigdata.bi.common.exception;

import com.bestpay.bigdata.bi.common.error.ErrorCode;
import com.bestpay.bigdata.bi.common.error.ErrorCodeSupplier;
import lombok.Getter;

@Getter
public class BiException extends RuntimeException {

    private final ErrorCode errorCode;

    public BiException(ErrorCodeSupplier errorCodeSupplier, String message) {
        this(errorCodeSupplier, message, null);
    }

    public BiException(ErrorCodeSupplier errorCodeSupplier, Throwable throwable) {
        this(errorCodeSupplier, null, throwable);
    }

    public BiException(ErrorCodeSupplier errorCodeSupplier, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCodeSupplier.toErrorCode();
    }

    @Override
    public String getMessage() {
        String message = super.getMessage();
        if (message == null && getCause() != null) {
            message = getCause().getMessage();
        }
        if (message == null) {
            message = errorCode.getName();
        }
        return message;
    }
}
