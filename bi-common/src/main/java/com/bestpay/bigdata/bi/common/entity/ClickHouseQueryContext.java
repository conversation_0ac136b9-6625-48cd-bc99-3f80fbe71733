package com.bestpay.bigdata.bi.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/18 15:42
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper=true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClickHouseQueryContext extends QueryContext implements Serializable {

    private static final long serialVersionUID = -1;


    //多维分析信息
    /**
     * 指标名称
     */
    private String indexName;

    /**
     * 查询的指标类型（0:单指标|1:多维指标）
     */
    private Integer indexType;

    /**
     * 显示类型：0-图形；1-表格
     */
    private Integer displayType;

    /**
     * 聚合方式
     */
    private String polymerization;
    /**
     * 中英文列名Map
     */
    private List<ColumnName> columnNameMaps;

    public ClickHouseQueryContext(String sql, String engineName, String dataSource, String username,
        int statementType,String indexName,int displayType,List<ColumnName> columnNameMaps) {
        super(sql, engineName, dataSource, username, statementType);
        this.indexName = indexName;
        this.displayType = displayType;
        this.columnNameMaps = columnNameMaps;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
