package com.bestpay.bigdata.bi.common.dto.dataset;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DatasetQueryDTO implements Serializable {
    private static final long serialVersionUID = -3588576601992931134L;
    private String name;
    private String ownerName;
    private String applicationScenario;
    private Integer statusCode;
    private String keyWord;
    private String orgCode;
    private String orgAuth;
    private List<Long> idList;
    private List<String> datasetCodeList;
    private Integer offset;
    private Integer pageSize;
    private String code;


    /**
     * 排序字段  createdAt 创建时间 updateAt 更新时间 accessCnt 近90天访问量   res_count 最近一次访问时间
     */
    private String sortField;

    /**
     * 排序规则  asc 升序  desc 降序
     */
    private String sortRule;

}
