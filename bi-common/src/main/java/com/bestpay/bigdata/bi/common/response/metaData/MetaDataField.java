package com.bestpay.bigdata.bi.common.response.metaData;

import java.io.Serializable;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022-03-22-15:04
 */
@Data
@Builder
public class MetaDataField implements Serializable{

    /**
     * 索引编号
     */
    private int columnIndex;
    /**
     * 列名称
     */
    private String columnName;
    /**
     * 列类型
     */
    private String columnType;
    /**
     * 列描述
     */
    private String columnComment;
    /**
     * 列中文名
     */
    private String columnChineseName;
    /**
     * 是否是分区字段
     */
    private boolean isPartition;
}
