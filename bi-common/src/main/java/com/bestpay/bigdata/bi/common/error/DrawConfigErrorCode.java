package com.bestpay.bigdata.bi.common.error;

public enum DrawConfigErrorCode implements ErrorCodeSupplier {

    DIMENSION_INDEX_CONFIG_ERROR("10001", ErrorType.USER_ERROR);

    private static final String PREFIX = "DRAW_";

    private final ErrorCode errorCode;

    DrawConfigErrorCode(String code, ErrorType type)
    {
        errorCode = new ErrorCode(PREFIX + code, name(), type);
    }

    @Override
    public ErrorCode toErrorCode()
    {
        return errorCode;
    }
}
