package com.bestpay.bigdata.bi.common.dto.dashboard.newcard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author:gaodingsong
 * @description: 行表头样式
 * @createTime:2024/5/10 18:12
 * @version:1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "行表头样式")
public class RowHeaderStyle extends TableDataStyle{

    @ApiModelProperty(value = "列数")
    private Integer columnsNum;

    @ApiModelProperty(value = "冻结行表头")
    private Boolean freezeRowHeader;
}
