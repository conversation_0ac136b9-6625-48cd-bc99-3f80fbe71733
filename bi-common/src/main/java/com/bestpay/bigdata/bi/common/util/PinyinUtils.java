package com.bestpay.bigdata.bi.common.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * <AUTHOR>
 * @ClassName PinyinUtils
 * @description 拼音转换工具类，支持中英文混合字符串的拼音转换和排序
 * @date 2025/7/4
 */
public class PinyinUtils {

  private static final HanyuPinyinOutputFormat FORMAT = new HanyuPinyinOutputFormat();

  static {
    // 设置拼音格式
    /**
     * LOWERCASE 小写
     * WITHOUT_TONE 不保留声调
     * WITH_V 'ü' 使用 'v' 表示
     */
    FORMAT.setCaseType(HanyuPinyinCaseType.LOWERCASE);
    FORMAT.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
    FORMAT.setVCharType(HanyuPinyinVCharType.WITH_V);
  }

  // 私有构造函数，防止实例化
  private PinyinUtils() {
  }

  /**
   * 将字符串转换为拼音，中文转拼音，非中文保持原样
   *
   * @param input 输入字符串
   * @return 拼音字符串
   */
  public static String toPinyin(String input) {
    if (input == null || input.isEmpty()) {
      return "";
    }

    StringBuilder pinyinBuilder = new StringBuilder();
    char[] chars = input.toCharArray();

    for (char c : chars) {
      if (isChineseCharacter(c)) {
        pinyinBuilder.append(convertChineseCharToPinyin(c));
      } else {
        // 非中文直接添加，转为小写以保证排序一致性
        pinyinBuilder.append(Character.toLowerCase(c));
      }
    }

    return pinyinBuilder.toString();
  }

  /**
   * 将单个中文字符转换为拼音
   *
   * @param c 中文字符
   * @return 拼音字符串，如果无法转换则返回原字符
   */
  private static String convertChineseCharToPinyin(char c) {
    try {
      // 获取拼音数组（可能有多个发音，取第一个）
      String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, FORMAT);
      if (pinyinArray != null && pinyinArray.length > 0) {
        return pinyinArray[0];
      }
    } catch (BadHanyuPinyinOutputFormatCombination e) {
      // 忽略异常，返回原字符
    }
    return String.valueOf(c);
  }

  /**
   * 判断字符是否为中文字符
   *
   * @param c 字符
   * @return 是否为中文字符
   */
  private static boolean isChineseCharacter(char c) {
    // 中文字符范围
    return c >= 0x4E00 && c <= 0x9FFF;
  }

  /**
   * 获取字符串的拼音首字母（中文取首字母，非中文保持原样）
   *
   * @param input 输入字符串
   * @return 拼音首字母字符串
   */
  public static String getFirstLetters(String input) {
    if (input == null || input.isEmpty()) {
      return "";
    }

    StringBuilder result = new StringBuilder();
    char[] chars = input.toCharArray();

    for (char c : chars) {
      if (isChineseCharacter(c)) {
        String pinyin = convertChineseCharToPinyin(c);
        if (!pinyin.isEmpty()) {
          result.append(pinyin.charAt(0));
        }
      } else {
        result.append(Character.toLowerCase(c));
      }
    }

    return result.toString();
  }
}
