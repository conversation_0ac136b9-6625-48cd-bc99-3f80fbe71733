package com.bestpay.bigdata.bi.common.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author：Song
 * @Date：2024/11/21 15:06
 * @Desc:
 */
@Data
@ApiModel("自定义列宽")
public class CustomColumnWidth {

    @ApiModelProperty(value = "字段对应的uuid")
    private String uuid;

    @ApiModelProperty(value = "报表配置的唯一标识")
    protected String configUuid;

    @ApiModelProperty(value = "列宽")
    private String columnConfig;

    @ApiModelProperty(value = "列名")
    private String columnName;

    @ApiModelProperty(value = "报表字段")
    private String reportField;
}
