package com.bestpay.bigdata.bi.common.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @Author: den<PERSON><PERSON><PERSON>
 * @CreateDate: 2022/2/14 17:27
 */
@ConfigurationProperties(prefix = "datasource")
@Component("dataSourceConfig")
@RefreshScope
@Slf4j
@Data
public class DataSourceConfig {

  /**
   * bi mysql
   */
  private String url;
  private String password;
  private String user;

  private String sftp;
  private String driver;


  /**
   *  local download path
   */
  private String localpath;
}
