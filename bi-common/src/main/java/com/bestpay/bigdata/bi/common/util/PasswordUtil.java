package com.bestpay.bigdata.bi.common.util;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.rpc.RpcException;
import com.bestpay.basic.service.SensitiveWordService;
import com.bestpay.basic.service.request.CheckWordRequest;
import com.bestpay.basic.service.response.BasicResponse;
import com.bestpay.basic.service.response.CheckWordResponse;
import com.bestpay.bigdata.bi.common.error.StandardErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.UUID;

@Slf4j
@Component
public class PasswordUtil {

    @Reference
    private SensitiveWordService sensitiveWordService;

    public static final String BASE_SPECIAL_CHAR = "~!@#$^*_";
    public static final String BASE_UPPER_CHAR = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    public static final String PASSWORD_CHARSET = RandomUtil.BASE_CHAR_NUMBER + BASE_SPECIAL_CHAR + BASE_UPPER_CHAR;
    public static final String SCOPE = "SMS";
    public static final String REQUEST_SYSTEM = "BI";
    public static final int PWD_LENGTH = 20;
    public static final int MAX_RETRY = 20;

    public String createPwdAndCheckSensitiveWord() {
        int attempt = 0;

        while (attempt < MAX_RETRY) {
            String password = createPassword();
            attempt++;

            try {
                if (isPasswordLegal(password)) {
                    log.info("Password generation succeeded on attempt {}", attempt);
                    return password;
                } else {
                    log.warn("Password contains sensitive words, regenerating... (attempt {})", attempt);
                }
            } catch (Exception e) {
                if (e instanceof RpcException) {
                    log.info("SensitiveWordService unavailable, skipping sensitive word check.");
                    return password;
                }
                log.error("Error checking sensitive words (attempt {}): {}", attempt, e.getMessage(), e);
            }
        }

        throw new BiException(StandardErrorCode.SENSITIVE_SERVICE_REQUEST_ERROR,
                "Failed to generate a valid password after " + MAX_RETRY + " attempts.");
    }

    private boolean isPasswordLegal(String password) {
        if (sensitiveWordService == null) {
            log.warn("SensitiveWordService reference is null, skipping sensitive word check.");
            return true;
        }

        CheckWordRequest request = new CheckWordRequest();
        request.setWord(password);
        request.setScope(SCOPE);
        request.setReqSystem(REQUEST_SYSTEM);
        request.setInitiationID(UUID.randomUUID().toString());

        BasicResponse<CheckWordResponse> response = sensitiveWordService.checkWordLegalRtnDetail(request);
        if (response != null && response.getResult() != null) {
            CheckWordResponse result = response.getResult();
            Boolean isValid = result.getResult();
            if (Boolean.TRUE.equals(isValid)) {
                return true;
            } else {
                Set<String> sensitiveWords = result.getStrings();
                log.warn("Sensitive words detected: {}", sensitiveWords);
                return false;
            }
        }
        log.warn("Sensitive word check returned null response or result.");
        return false;
    }

    private static String createPassword() {
        return RandomUtil.randomString(PASSWORD_CHARSET, PWD_LENGTH);
    }
}
