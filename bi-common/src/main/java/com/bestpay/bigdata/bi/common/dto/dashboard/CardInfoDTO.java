package com.bestpay.bigdata.bi.common.dto.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewCardDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("卡片信息")
public class CardInfoDTO {

  @ApiModelProperty(value = "卡片名称")
  private String cardName;

  @ApiModelProperty(value = "图表类型")
  private Integer chartType;

  @ApiModelProperty(value = "报表名称")
  private String reportName;
}
