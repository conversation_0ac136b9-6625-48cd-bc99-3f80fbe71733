package com.bestpay.bigdata.bi.common.enums;

import com.bestpay.bigdata.bi.common.exception.BusinessException;
import java.util.Objects;
import lombok.Getter;

@Getter
public enum ScopeFilterTypeEnum {
    //大于
    GREATER("greater"," ({} > {}) "),
    //大于等于
    GREATER_OR_EQUALS("greaterOrEquals"," ({} >= {}) "),
    //小于
    LESS("less"," ({} < {}) "),
    //小于等于
    LESS_OR_EQUALS("lessOrEquals"," ({} <= {}) "),
    //区间
    INTERVAL("interval"," ({} between {} and {}) "),
    //等于
    EQUALS("equals"," ({} = {}) "),
    //不等于
    NOT_EQUALS("notEquals"," ({} != {}) "),
    //为空
    IS_NULL("isNull"," ({} is null) "),
    //不为空
    IS_NOT_NULL("isNotNull"," ({} is not null) "),
    //包含
    IN("in"," ({} in ({})) "),
    //不包含
    NOT_IN("notIn"," ({} not in ({})) "),
    //模糊查询
    LIKE("like"," ({} like CONCAT('%',{},'%' ))"),
    ;
    private final String code;
    private final String msg;

    ScopeFilterTypeEnum(String code,String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static boolean isInclude(String name) {
        ScopeFilterTypeEnum[] scopeFilterTypeEnums = ScopeFilterTypeEnum.values();
        for (ScopeFilterTypeEnum scopeFilterTypeEnum : scopeFilterTypeEnums) {
            if (Objects.equals(scopeFilterTypeEnum.getCode(), name)) {
                return true;
            }
        }
        return false;
    }

    public final static String ALL_VALUE = "greater,greaterOrEquals,less,lessOrEquals,interval,equals,notEquals,isNull,isNotNull,in,notIn";

    public static String getName(String code) {
        ScopeFilterTypeEnum[] scopeFilterTypeEnums = ScopeFilterTypeEnum.values();
        for (ScopeFilterTypeEnum scopeFilterTypeEnum : scopeFilterTypeEnums) {
            if (Objects.equals(scopeFilterTypeEnum.getCode(), code)) {
                return scopeFilterTypeEnum.getMsg();
            }
        }
        throw new BusinessException("-1", "不支持的范围过滤类型:" + code);
    }
}
