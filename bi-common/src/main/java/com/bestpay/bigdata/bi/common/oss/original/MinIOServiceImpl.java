package com.bestpay.bigdata.bi.common.oss.original;

import cn.hutool.core.lang.Assert;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.enums.FileSystemTypeEnum;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.oss.AbstractOssService;
import com.bestpay.bigdata.bi.common.oss.OssContext;
import com.bestpay.bigdata.bi.common.oss.wrap.MiniIOOssContext;
import com.bestpay.bigdata.bi.common.util.Base64Util;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import io.minio.StatObjectArgs;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ConditionalOnProperty(name = "refresh.original-minio-enabled", havingValue = "true")
public class MinIOServiceImpl extends AbstractOssService {

    private static final List<String> IMAGE_FORMATS = Arrays.asList(
            "bmp", "jpg", "png", "tif", "gif", "pcx", "tga", "exif", "fpx", "svg",
            "psd", "cdr", "pcd", "dxf", "ufo", "eps", "ai", "raw", "wmf", "webp", "avif", "apng"
    );
    @Autowired
    private ApolloRefreshConfig apolloRefreshConfig;

    @Autowired
    private MinioClient minioClient;

    @Override
    public String upload(OssContext ossContext) {
        MiniIOOssContext context = (MiniIOOssContext) ossContext;

        Assert.notNull(context.getInputStream(), "InputStream cannot be null");
        Assert.notNull(context.getFileExtName(), "File extension cannot be null");

        String filePath = buildFilePath(context.getFileExtName());
        try (InputStream inputStream = context.getInputStream()) {

            PutObjectArgs.Builder builder = PutObjectArgs.builder()
                    .object(filePath)
                    .bucket(apolloRefreshConfig.getOriginalMinioBucketName())
                    .stream(inputStream, inputStream.available(), -1);

            if (IMAGE_FORMATS.contains(context.getFileExtName().toLowerCase())) {
                builder.contentType("image/" + context.getFileExtName().toLowerCase());
            } else if ("zip".equalsIgnoreCase(context.getFileExtName())) {
                builder.contentType("application/zip");
            } else if ("xlsx".equalsIgnoreCase(context.getFileExtName())) {
                builder.contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            } else if ("csv".equalsIgnoreCase(context.getFileExtName())) {
                builder.contentType("text/csv");
            }

            minioClient.putObject(builder.build());
            log.info("File uploaded successfully: {}", filePath);
        } catch (Exception e) {
            log.error("Failed to upload file: {}", filePath, e);
            throw new BusinessException("Failed to upload file to MinIO");
        }
        return filePath;
    }

    private static String buildFilePath(String fileExtName) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        String prefix = uuid.substring(0, 2);
        return String.format(
                "files/%s/%s/%s.%s",
                new SimpleDateFormat("yyyyMMdd").format(new Date()),
                prefix,
                uuid,
                fileExtName
        );
    }

    @Override
    public byte[] download(OssContext ossContext) {
        MiniIOOssContext context = (MiniIOOssContext) ossContext;
        String miniIoPath = context.getMiniIoPath();
        Assert.notEmpty(miniIoPath, "miniIoPath can not be empty");

        try (InputStream inputStream = minioClient.getObject(GetObjectArgs.builder()
                .bucket(apolloRefreshConfig.getOriginalMinioBucketName())
                .object(miniIoPath)
                .build())) {

            return inputStreamToByteArray(inputStream);
        } catch (Exception e) {
            log.error("Failed to download file: {}, bucket: {}", miniIoPath, apolloRefreshConfig.getOriginalMinioBucketName(), e);
            throw new BusinessException("Failed to download file from MinIO");
        }
    }

    private static byte[] inputStreamToByteArray(InputStream inputStream) throws IOException {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
            return byteArrayOutputStream.toByteArray();
        }
    }

    @Override
    public FileSystemTypeEnum getFileSystemType() {
        return FileSystemTypeEnum.MINIO_ORIGINAL;
    }

    @Override
    public String parseHttpUrl(OssContext ossContext) {
        MiniIOOssContext context = (MiniIOOssContext) ossContext;
        String miniIoPath = context.getMiniIoPath();
        Assert.notEmpty(miniIoPath, "miniIoPath can not be empty");

        log.info(" parseHttpUrl  miniIoPath : {}", miniIoPath);
        String result = apolloRefreshConfig.getFileDownloadUrl() + Base64Util.encryptBASE64(miniIoPath.getBytes());
        log.info(" parseHttpUrl  result : {}", result);
        return  result;
    }

    @Override
    public void delete(OssContext ossContext) {
        MiniIOOssContext context = (MiniIOOssContext) ossContext;
        String miniIoPath = context.getMiniIoPath();
        Assert.notEmpty(miniIoPath, "miniIoPath can not be empty");

        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(apolloRefreshConfig.getOriginalMinioBucketName())
                    .object(miniIoPath)
                    .build());
            log.info("File deleted successfully: {}", miniIoPath);
        } catch (Exception e) {
            log.error("Failed to delete file: {}, bucket: {}", miniIoPath, apolloRefreshConfig.getOriginalMinioBucketName(), e);
            throw new BusinessException("Failed to delete file from MinIO");
        }
    }

    @Override
    public boolean judgeResourceExists(OssContext ossContext) {
        MiniIOOssContext context = (MiniIOOssContext) ossContext;
        String miniIoPath = context.getMiniIoPath();
        Assert.notEmpty(miniIoPath, "miniIoPath can not be empty");

        try {
            minioClient.statObject(StatObjectArgs.builder()
                    .bucket(apolloRefreshConfig.getOriginalMinioBucketName())
                    .object(miniIoPath)
                    .build());
            return true;
        } catch (Exception e) {
            log.error("miniIO judge resource exists happen error, ossContext : {}", ossContext, e);
            return false;
        }
    }
}

