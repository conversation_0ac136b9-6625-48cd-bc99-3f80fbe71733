package com.bestpay.bigdata.bi.common.dto.datascreen;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 组件位置信息
 * <AUTHOR>
 */
@Data
@ApiModel("组件位置信息")
public class DataScreenComponentLocationDTO implements Serializable {
    private static final long serialVersionUID = -4462573082598449403L;
    @ApiModelProperty(value = "组件位置X坐标")
    private Integer x;
    @ApiModelProperty(value = "组件位置Y坐标")
    private Integer y;
    @ApiModelProperty(value = "组件宽度")
    private Integer w;
    @ApiModelProperty(value = "组件高度")
    private Integer h;
}
