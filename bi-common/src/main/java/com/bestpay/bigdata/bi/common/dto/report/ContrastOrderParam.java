package com.bestpay.bigdata.bi.common.dto.report;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.report.component.OrderComponentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/19 14:06
 * @Description :
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "对比表头排序参数")
public class ContrastOrderParam {

    @ApiModelProperty(value = "对比表头排序, 这里面存储对比表头的信息, key: uuid(对应的对比字段id), value: label值, 除行总计表头外")
    private Map<String, String> contrastOrderParam;

    @ApiModelProperty(value = "是否在行总计表头使用排序")
    private Boolean isRowTotal = false;


    public static void main(String[] args) {

        OrderComponentDTO orderComponentDTO = new OrderComponentDTO();
        orderComponentDTO.setEnName("test_region");
        orderComponentDTO.setId(100L);
        orderComponentDTO.setUuid("adfqref23452452452");

        ContrastOrderParam contrastOrderParam1 = new ContrastOrderParam();
        Map<String, String> map = new HashMap<>();
        map.put("q4134234234wfwf", "2023");
        map.put("sfsdfsdgs345345346sds", "电信");
        contrastOrderParam1.setContrastOrderParam(map);
        orderComponentDTO.setOrderParam(contrastOrderParam1);
        System.out.println(JSONUtil.toJsonStr(orderComponentDTO));

    }

}

