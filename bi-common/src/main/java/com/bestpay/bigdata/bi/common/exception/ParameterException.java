package com.bestpay.bigdata.bi.common.exception;

import com.bestpay.bigdata.bi.common.enums.CodeEnum;

/**
 * 参数校验不通过的异常
 * <AUTHOR>
 * @date 2021/3/11 14:51
 **/
public class ParameterException extends BaseBiException{

    public ParameterException(CodeEnum codeEnum) {
        super(codeEnum);
    }

    public ParameterException(String message, String code) {
        super(code,message);
    }

}
