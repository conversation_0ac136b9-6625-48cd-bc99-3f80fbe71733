package com.bestpay.bigdata.bi.common.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.bestpay.bigdata.bi.common.entity.FileDownloadInfo;
import java.io.File;
import java.io.FileOutputStream;
import java.util.List;
import java.util.Objects;

import com.bestpay.bigdata.bi.common.exception.DownloadException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;


public enum FileType {
    /**EXCEL*/
    EXCEL(".xlsx"){
        @Override
        public void download(FileDownloadInfo fileDownloadInfo, boolean firstLoad, boolean lastLoad, BigExcelWriter excelWriter, CsvWriter csvWriter) {

            List<List<String>> rowData = fileDownloadInfo.getDataRow();
            excelWriter.setDestFile(new File(fileDownloadInfo.getFilePath()));
            if (firstLoad) {
                List<String> headerRow = fileDownloadInfo.getHeaderRow();
                int rowCount = 1;
                int size = rowData.size();
                if (CollUtil.isNotEmpty(rowData)) {
                    rowCount = rowData.get(0) != null && rowData.get(0).size() >= 2 ? rowData.get(0).size() - 1 : 1;
                }

                log.info("rowCount is :{}; Data total is :{}; ", rowCount, size);
                if (!CollectionUtils.isEmpty(fileDownloadInfo.getComments())) {
                    for (String comment : fileDownloadInfo.getComments()) {
                        excelWriter.merge(rowCount, comment);
                    }
                }
                log.info("excel 写表头信息 headerRow :{}", headerRow.size());
                excelWriter.writeHeadRow(headerRow);
            }
            // 一次性写出内容，使用默认样式，强制输出标题
            excelWriter.write(rowData,true);
            // 关闭writer，释放内存
            if (lastLoad) {
                excelWriter.close();
            }
        }
    }, CSV(".csv") {
        @Override
        public void download(FileDownloadInfo fileDownloadInfo, boolean firstLoad, boolean lastLoad, BigExcelWriter excelWriter, CsvWriter csvWriter) {

            List<List<String>> dataRow = fileDownloadInfo.getDataRow();
            List<String> headerRow = fileDownloadInfo.getHeaderRow();
/*            String filePath = fileDownloadInfo.getFilePath();
            FileOutputStream fos = null;*/
/*                if (!FileUtil.exist(filePath) && firstLoad) {
                    FileUtil.touch(filePath);
                }
                File file = new File(filePath);
                fos = new FileOutputStream(file);*/
                if (firstLoad) {
                    //UTF-8编码
                    //byte[] bs = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
                    //fos.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});
                    //fos.flush();Charset.forName("UTF-16BE")
                    //writer.setAlwaysDelimitText(true);
                    //append comments if necessary
                    if (!CollectionUtils.isEmpty(fileDownloadInfo.getComments())) {
                        csvWriter.write(fileDownloadInfo.getComments());
                    }
                    String[] headers = headerRow.toArray(new String[headerRow.size()]);
                    log.info("csv 写表头信息 headers size :{}", headers.length);
                    csvWriter.write(headers);
                }
                csvWriter.write(dataRow);

                if (lastLoad) {
                    csvWriter.close();
                }
        }
    };


    private String suffix;

    FileType(String suffix) {
        this.suffix = suffix;
    }

    public String getSuffix() {
        return suffix;
    }

    public abstract void download(FileDownloadInfo fileDownloadInfo, boolean firstLoad, boolean lastLoad, BigExcelWriter excelWriter, CsvWriter csvWriter);


    private static final Logger log = LoggerFactory.getLogger(FileType.class);

    /**
     * 默认为CSV
     * @param fileType
     * @return
     */
    public static FileType getFileType(Integer fileType) {
        if (fileType == 1) {
            return FileType.CSV;
        }
        if (fileType == 2) {
            return FileType.EXCEL;
        }
        return FileType.CSV;
    }

    public static Integer getFileTypeCode(FileType fileType) {
        if (Objects.isNull(fileType)) {
            return 1;
        }
        if (fileType.getSuffix().equals(FileType.CSV.getSuffix())) {
            return 1;
        }
        if (fileType.getSuffix().equals(FileType.EXCEL.getSuffix())) {
            return 2;
        }
        return 1;
    }
}
