package com.bestpay.bigdata.bi.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.entity.PermissionInfo;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PermissionUtils {

    private static final String SHARE_DASHBOARD = "shareDashboard";

    private static final String QUERY_SELECT_VALUE = "/biReport/reportProcess/querySelectValue";

    public static Boolean checkZhiJiaBoarding(HttpServletRequest request, UserInfo userInfo, String urlAuthorityConfigJsonStr){

        if (request.getServletPath().contains(SHARE_DASHBOARD)
                || request.getServletPath().contains(QUERY_SELECT_VALUE)) {
            log.info("not need to check authority, url = {}",request.getServletPath());
            return true;
        }

        log.info("url = {}, method={}", request.getServletPath(), request.getMethod());
        if(userInfo!=null && userInfo.getIsManager()){
            log.info("checkUserBoarding, user is manager");
            return true;
        }


        // 1、获取权限配置
        List<AuthorityUrl> authorityUrls
                = JSONUtil.toList(urlAuthorityConfigJsonStr, AuthorityUrl.class);

        if(CollUtil.isEmpty(authorityUrls)){
            log.info("authorityUrls is empty");
            return true;
        }

        if(userInfo==null){
            log.info("user is null");
            return true;
        }

        List<PermissionInfo> permissionInfoList = userInfo.getPermissionInfoList();
        if(CollUtil.isEmpty(permissionInfoList)){
            log.info("user permissionInfoList is empty");
            return false;
        }

        // 2、判断用户是否有权限访问
        for (AuthorityUrl authorityUrl : authorityUrls) {
            // 匹配url
            if(!request.getServletPath().contains(authorityUrl.getUrl())){
                continue;
            }

            // 匹配method：post，get等等
            if(StringUtil.isNotEmpty(authorityUrl.getRequestType())
                    && !authorityUrl.getRequestType().equals(request.getMethod())){
                continue;
            }

            if (!authorityUrl.getIsLimit()) {
                log.info("not need to check authority, url = {}, isLimit={}",
                        request.getServletPath(), authorityUrl.getIsLimit());
                return true;
            }

            if(CollUtil.isEmpty(authorityUrl.getAuthority())){
                log.info("can't check authority, url = {}, authority is empty",
                        request.getServletPath());
                return false;
            }

            Boolean checkSuccess = matchAuthority(request, permissionInfoList, authorityUrl);
            if (checkSuccess) {
                return true;
            }
        }
        log.info("user authority check not pass");
        return false;
    }


    private static Boolean matchAuthority(HttpServletRequest request,
                                          List<PermissionInfo> permissionInfoList,
                                          AuthorityUrl authorityUrl) {

        for (Authority authority : authorityUrl.getAuthority()) {
            for (PermissionInfo permissionInfo : permissionInfoList) {
                // 匹配资源
                if(!authority.getResource().equals(permissionInfo.getResource())){
                    continue;
                }

                log.info("start check user authority, url = {}, resource={}",
                        request.getServletPath(), authority.getResource());

                // 匹配资源权限登录，浏览，修改，管理员
                if(authority.getPermissionLevel()
                        .compareTo(permissionInfo.getPermissionLevel()) > 0){
                    log.info("url ={}, resource ={}, user permissionLevel = {}, lower required permissionLevel={}",
                            request.getServletPath(),
                            permissionInfo.getResource(),
                            authority.getPermissionLevel(),
                            permissionInfo.getPermissionLevel());
                    continue;
                }

                // 无button
                if(StringUtil.isEmpty(authority.getButton())){
                    log.info("not need to check button, url = {}, resource ={}",
                            request.getServletPath(),
                            permissionInfo.getResource());
                    return true;
                }

                // 匹配资源下的button
                for (String button : permissionInfo.getButtons()) {
                    if(authority.getButton().equals(button)){
                        log.info("url ={}, resource ={}, user has required button ={}",
                                request.getServletPath(),
                                permissionInfo.getResource(),
                                authority.getButton());
                        return true;
                    }
                }
            }
        }

        return false;
    }


    @Data
    class AuthorityUrl{
        private String url;
        private Boolean isLimit;
        private String requestType;
        private List<Authority> authority;
    }

    @Data
    class Authority{
        private String resource;
        private String permissionLevel;
        private String button;
    }


}
