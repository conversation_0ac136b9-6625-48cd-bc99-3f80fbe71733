package com.bestpay.bigdata.bi.common.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "智加用户搜索请求参数")
public class AiPlusUserSearchRequest {

  @ApiModelProperty(value = "用户邮箱")
  private List<String> ownerNames;

}
