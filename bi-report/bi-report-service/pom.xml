<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>bi-report</artifactId>
    <groupId>com.bestpay.bigdata</groupId>
    <version>1.69.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>bi-report-service</artifactId>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.7</maven.compiler.source>
    <maven.compiler.target>1.7</maven.compiler.target>
    <selenium.version>3.141.59</selenium.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.bestpay.seccore</groupId>
      <artifactId>sec-hsm-core-api</artifactId>
      <version>1.23.0</version>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-common</artifactId>
      <version>1.69.0</version>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-report-api</artifactId>
      <version>1.69.0</version>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-database</artifactId>
      <version>1.69.0</version>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-backend-api</artifactId>
      <version>1.69.0</version>
    </dependency>
    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-thirdparty</artifactId>
      <version>1.69.0</version>
    </dependency>

    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-parsesql</artifactId>
      <version>1.69.0</version>
      <exclusions>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-usermanage</artifactId>
      <version>1.69.0</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba.boot</groupId>
      <artifactId>dubbo-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.sgroschupf</groupId>
      <artifactId>zkclient</artifactId>
    </dependency>
    <dependency>
      <groupId>org.seleniumhq.selenium</groupId>
      <artifactId>selenium-java</artifactId>
      <version>${selenium.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-lang3</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>okio</artifactId>
          <groupId>com.squareup.okio</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.csource</groupId>
      <artifactId>fastdfs</artifactId>
      <version>1.27.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.sun.mail</groupId>
      <artifactId>javax.mail</artifactId>
      <version>1.6.2</version>
    </dependency>

    <dependency>
      <groupId>com.hubspot.jinjava</groupId>
      <artifactId>jinjava</artifactId>
      <version>2.6.0</version>
    </dependency>

    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>alibaba-dingtalk-service-sdk</artifactId>
      <version>2.0.0</version>
    </dependency>

    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
      <version>1.3.1</version>
    </dependency>

    <dependency>
      <groupId>com.bestpay.mbp</groupId>
      <artifactId>mbp-output-gateway-api</artifactId>
      <version>1.30.1</version>
    </dependency>

    <dependency>
      <groupId>com.hubspot.jinjava</groupId>
      <artifactId>jinjava</artifactId>
      <version>2.3.1</version>
    </dependency>

    <dependency>
      <groupId>org.reflections</groupId>
      <artifactId>reflections</artifactId>
      <version>0.9.11</version>
    </dependency>
    <dependency>
      <groupId>commons-net</groupId>
      <artifactId>commons-net</artifactId>
      <version>3.6</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.15</version>
      <scope>runtime</scope>
    </dependency>

    <!--quartz依赖-->
    <dependency>
      <groupId>org.quartz-scheduler</groupId>
      <artifactId>quartz-jobs</artifactId>
      <version>2.2.2</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-quartz</artifactId>
    </dependency>

<!--    excel依赖-->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>4.1.2</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-compress</artifactId>
          <groupId>org.apache.commons</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <artifactId>commons-compress</artifactId>
      <groupId>org.apache.commons</groupId>
      <version>1.19</version>
    </dependency>


    <dependency>
      <groupId>net.lingala.zip4j</groupId>
      <artifactId>zip4j</artifactId>
      <version>1.3.3</version>
    </dependency>

    <!--sftp连接依赖-->
    <dependency>
      <groupId>com.jcraft</groupId>
      <artifactId>jsch</artifactId>
      <version>0.1.54</version>
    </dependency>

<!--    udf解密-->
    <dependency>
      <groupId>com.bestpay.seccore</groupId>
      <artifactId>sec-extcrypto-utils</artifactId>
      <version>1.5.27160</version>
    </dependency>

    <!-- 用于解析html并从中提取纯文本 -->
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.20.1</version>
    </dependency>

    <dependency>
      <groupId>com.bestpay.bigdata</groupId>
      <artifactId>bi-usermanage</artifactId>
      <version>1.69.0</version>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <includeSystemScope>true</includeSystemScope>
        </configuration>
        <version>2.2.6.RELEASE</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.3.2</version>
        <configuration>
          <source>8</source>
          <target>8</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <configuration>
          <encoding>UTF-8</encoding>
          <nonFilteredFileExtensions>
            <!--过滤掉不需要编码的文件：过滤后缀为 .doc、.xlsx。。。 的所有文件，不对其进行统一编码-->
            <nonFilteredFileExtension>doc</nonFilteredFileExtension>
            <nonFilteredFileExtension>docx</nonFilteredFileExtension>
            <nonFilteredFileExtension>txt</nonFilteredFileExtension>
            <nonFilteredFileExtension>ftl</nonFilteredFileExtension>
            <nonFilteredFileExtension>xls</nonFilteredFileExtension>
            <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
          </nonFilteredFileExtensions>
        </configuration>
      </plugin>
    </plugins>

  </build>
</project>
