package com.bestpay.bigdata.bi.report.util;

import com.bestpay.bigdata.bi.database.dao.usergroupmanage.UserGroupInfoDO;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName UserGroupDataUtil
 * @description 用户组数据准备工具类
 * @date 2025/7/5
 */
public class UserGroupDataUtil {
  public static final String TEST_ORG_CODE = "20005";
  public static final String TEST_ORG_NAME = "大数据部";

  public static final String TEST_USER_ID = "USER001";


  // 辅助方法：创建单层用户组数据
  public static List<UserGroupInfoDO> createSingleLevelGroups() {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    UserGroupInfoDO group1 = new UserGroupInfoDO();
    group1.setUserGroupCode("GROUP001");
    group1.setUserGroupName("一级用户组1");
    group1.setOrgCode(TEST_ORG_CODE);
    group1.setOrgName(TEST_ORG_NAME);
    group1.setUserGroupParentCode(null);
    groups.add(group1);

    UserGroupInfoDO group2 = new UserGroupInfoDO();
    group2.setUserGroupCode("GROUP002");
    group2.setUserGroupName("一级用户组2");
    group2.setOrgCode(TEST_ORG_CODE);
    group2.setOrgName(TEST_ORG_NAME);
    group2.setUserGroupParentCode(null);
    groups.add(group2);

    return groups;
  }

  // 辅助方法：创建多层用户组数据
  public static List<UserGroupInfoDO> createMultiLevelGroups() {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    // 一级用户组1
    UserGroupInfoDO group1 = new UserGroupInfoDO();
    group1.setUserGroupCode("GROUP001");
    group1.setUserGroupName("一级用户组1");
    group1.setOrgCode(TEST_ORG_CODE);
    group1.setOrgName(TEST_ORG_NAME);
    group1.setUserGroupParentCode(null);
    groups.add(group1);

    // 二级用户组1
    UserGroupInfoDO group2 = new UserGroupInfoDO();
    group2.setUserGroupCode("GROUP003");
    group2.setUserGroupName("二级用户组1");
    group2.setOrgCode(TEST_ORG_CODE);
    group2.setOrgName(TEST_ORG_NAME);
    group2.setUserGroupParentCode("GROUP001");
    groups.add(group2);

    return groups;
  }

  // 辅助方法：创建多组织数据
  public static List<UserGroupInfoDO> createGroupsWithMultipleOrgs() {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    // 组织A
    UserGroupInfoDO groupA1 = new UserGroupInfoDO();
    groupA1.setUserGroupCode("A1");
    groupA1.setUserGroupName("部门A1");
    groupA1.setOrgCode("ORG_A");
    groupA1.setOrgName("组织A");
    groupA1.setUserGroupParentCode(null);
    groups.add(groupA1);

    // 组织B
    UserGroupInfoDO groupB1 = new UserGroupInfoDO();
    groupB1.setUserGroupCode("B1");
    groupB1.setUserGroupName("部门B1");
    groupB1.setOrgCode("ORG_B");
    groupB1.setOrgName("组织B");
    groupB1.setUserGroupParentCode(null);
    groups.add(groupB1);

    return groups;
  }

  // 辅助方法：创建包含特殊字符的数据
  public static List<UserGroupInfoDO> createGroupsWithSpecialChars() {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    UserGroupInfoDO group = new UserGroupInfoDO();
    group.setUserGroupCode("SPECIAL");
    group.setUserGroupName("部门(研发部)");
    group.setOrgCode(TEST_ORG_CODE);
    group.setOrgName(TEST_ORG_NAME);
    group.setUserGroupParentCode(null);
    groups.add(group);

    return groups;
  }

  // 辅助方法：创建叶子节点数据
  public static List<UserGroupInfoDO> createLeafNodes() {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    UserGroupInfoDO leafNode = new UserGroupInfoDO();
    leafNode.setUserGroupCode("LEAF");
    leafNode.setUserGroupName("叶子节点");
    leafNode.setOrgCode(TEST_ORG_CODE);
    leafNode.setOrgName(TEST_ORG_NAME);
    leafNode.setUserGroupParentCode(null);
    groups.add(leafNode);

    return groups;
  }

  // 辅助方法：创建大量数据
  public static List<UserGroupInfoDO> createLargeDataSet(int size) {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    // 创建根节点
    UserGroupInfoDO root = new UserGroupInfoDO();
    root.setUserGroupCode("ROOT");
    root.setUserGroupName("根部门");
    root.setOrgCode(TEST_ORG_CODE);
    root.setOrgName(TEST_ORG_NAME);
    root.setUserGroupParentCode(null);
    groups.add(root);

    // 创建大量子节点
    for (int i = 0; i < size - 1; i++) {
      UserGroupInfoDO child = new UserGroupInfoDO();
      child.setUserGroupCode("CHILD_" + i);
      child.setUserGroupName("子部门_" + i);
      child.setOrgCode(TEST_ORG_CODE);
      child.setOrgName(TEST_ORG_NAME);
      child.setUserGroupParentCode("ROOT");
      groups.add(child);
    }

    return groups;
  }

  // 辅助方法：创建完整3层结构数据
  public static List<UserGroupInfoDO> createThreeLevelGroups() {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    // 一级部门
    UserGroupInfoDO level1 = new UserGroupInfoDO();
    level1.setUserGroupCode("LEVEL1");
    level1.setUserGroupName("一级部门");
    level1.setOrgCode(TEST_ORG_CODE);
    level1.setOrgName(TEST_ORG_NAME);
    level1.setUserGroupParentCode(null);
    groups.add(level1);

    // 二级部门
    UserGroupInfoDO level2 = new UserGroupInfoDO();
    level2.setUserGroupCode("LEVEL2");
    level2.setUserGroupName("二级部门");
    level2.setOrgCode(TEST_ORG_CODE);
    level2.setOrgName(TEST_ORG_NAME);
    level2.setUserGroupParentCode("LEVEL1");
    groups.add(level2);

    // 三级部门
    UserGroupInfoDO level3 = new UserGroupInfoDO();
    level3.setUserGroupCode("LEVEL3");
    level3.setUserGroupName("三级部门");
    level3.setOrgCode(TEST_ORG_CODE);
    level3.setOrgName(TEST_ORG_NAME);
    level3.setUserGroupParentCode("LEVEL2");
    groups.add(level3);

    return groups;
  }

  // 辅助方法：创建部分匹配的3层结构数据
  public static List<UserGroupInfoDO> createThreeLevelGroupsWithPartialMatches() {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    // 一级部门
    UserGroupInfoDO level1 = new UserGroupInfoDO();
    level1.setUserGroupCode("LEVEL1");
    level1.setUserGroupName("一级部门");
    level1.setOrgCode(TEST_ORG_CODE);
    level1.setOrgName(TEST_ORG_NAME);
    level1.setUserGroupParentCode(null);
    groups.add(level1);

    // 二级部门
    UserGroupInfoDO level2 = new UserGroupInfoDO();
    level2.setUserGroupCode("LEVEL2");
    level2.setUserGroupName("二级部门");
    level2.setOrgCode(TEST_ORG_CODE);
    level2.setOrgName(TEST_ORG_NAME);
    level2.setUserGroupParentCode("LEVEL1");
    groups.add(level2);

    // 三级部门（无匹配）
    UserGroupInfoDO level3 = new UserGroupInfoDO();
    level3.setUserGroupCode("LEVEL3");
    level3.setUserGroupName("三级部门（无匹配）");
    level3.setOrgCode(TEST_ORG_CODE);
    level3.setOrgName(TEST_ORG_NAME);
    level3.setUserGroupParentCode("LEVEL2");
    // 不添加到匹配列表中
    // groups.add(level3);

    return groups;
  }

  // 辅助方法：创建兄弟节点数据
  public static List<UserGroupInfoDO> createSiblingNodesGroups() {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    // 一级部门1
    UserGroupInfoDO level1_1 = new UserGroupInfoDO();
    level1_1.setUserGroupCode("LEVEL1_1");
    level1_1.setUserGroupName("一级部门1");
    level1_1.setOrgCode(TEST_ORG_CODE);
    level1_1.setOrgName(TEST_ORG_NAME);
    level1_1.setUserGroupParentCode(null);
    groups.add(level1_1);

    // 一级部门2
    UserGroupInfoDO level1_2 = new UserGroupInfoDO();
    level1_2.setUserGroupCode("LEVEL1_2");
    level1_2.setUserGroupName("一级部门2");
    level1_2.setOrgCode(TEST_ORG_CODE);
    level1_2.setOrgName(TEST_ORG_NAME);
    level1_2.setUserGroupParentCode(null);
    groups.add(level1_2);

    // 一级部门1的子部门
    UserGroupInfoDO child1 = new UserGroupInfoDO();
    child1.setUserGroupCode("CHILD1");
    child1.setUserGroupName("子部门1");
    child1.setOrgCode(TEST_ORG_CODE);
    child1.setOrgName(TEST_ORG_NAME);
    child1.setUserGroupParentCode("LEVEL1_1");
    groups.add(child1);

    // 一级部门2的子部门
    UserGroupInfoDO child2 = new UserGroupInfoDO();
    child2.setUserGroupCode("CHILD2");
    child2.setUserGroupName("子部门2");
    child2.setOrgCode(TEST_ORG_CODE);
    child2.setOrgName(TEST_ORG_NAME);
    child2.setUserGroupParentCode("LEVEL1_2");
    groups.add(child2);

    return groups;
  }

  // 辅助方法：创建混合父节点数据
  public static List<UserGroupInfoDO> createMixedParentNodesGroups() {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    // 一级部门
    UserGroupInfoDO level1 = new UserGroupInfoDO();
    level1.setUserGroupCode("LEVEL1");
    level1.setUserGroupName("一级部门");
    level1.setOrgCode(TEST_ORG_CODE);
    level1.setOrgName(TEST_ORG_NAME);
    level1.setUserGroupParentCode(null);
    groups.add(level1);

    // 二级部门
    UserGroupInfoDO level2 = new UserGroupInfoDO();
    level2.setUserGroupCode("LEVEL2");
    level2.setUserGroupName("二级部门");
    level2.setOrgCode(TEST_ORG_CODE);
    level2.setOrgName(TEST_ORG_NAME);
    level2.setUserGroupParentCode("LEVEL1");
    groups.add(level2);

    // 三级部门
    UserGroupInfoDO level3 = new UserGroupInfoDO();
    level3.setUserGroupCode("LEVEL3");
    level3.setUserGroupName("三级部门");
    level3.setOrgCode(TEST_ORG_CODE);
    level3.setOrgName(TEST_ORG_NAME);
    level3.setUserGroupParentCode("LEVEL2");
    groups.add(level3);

    return groups;
  }

  // 辅助方法：创建节点名称重复的数据
  public static List<UserGroupInfoDO> createDuplicateNameGroups() {
    List<UserGroupInfoDO> groups = new ArrayList<>();

    // 部门A
    UserGroupInfoDO deptA = new UserGroupInfoDO();
    deptA.setUserGroupCode("DEPT_A");
    deptA.setUserGroupName("部门A");
    deptA.setOrgCode(TEST_ORG_CODE);
    deptA.setOrgName(TEST_ORG_NAME);
    deptA.setUserGroupParentCode(null);
    groups.add(deptA);

    // 部门C
    UserGroupInfoDO deptC = new UserGroupInfoDO();
    deptC.setUserGroupCode("DEPT_C");
    deptC.setUserGroupName("部门C");
    deptC.setOrgCode(TEST_ORG_CODE);
    deptC.setOrgName(TEST_ORG_NAME);
    deptC.setUserGroupParentCode(null);
    groups.add(deptC);

    // 部门B（作为部门A的子部门）
    UserGroupInfoDO deptB1 = new UserGroupInfoDO();
    deptB1.setUserGroupCode("DEPT_B1");
    deptB1.setUserGroupName("部门B");
    deptB1.setOrgCode(TEST_ORG_CODE);
    deptB1.setOrgName(TEST_ORG_NAME);
    deptB1.setUserGroupParentCode("DEPT_A");
    groups.add(deptB1);

    // 部门B（作为部门C的子部门）
    UserGroupInfoDO deptB2 = new UserGroupInfoDO();
    deptB2.setUserGroupCode("DEPT_B2");
    deptB2.setUserGroupName("部门B");
    deptB2.setOrgCode(TEST_ORG_CODE);
    deptB2.setOrgName(TEST_ORG_NAME);
    deptB2.setUserGroupParentCode("DEPT_C");
    groups.add(deptB2);

    return groups;
  }
}
