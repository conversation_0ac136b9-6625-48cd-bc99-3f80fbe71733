package com.bestpay.bigdata.bi.report.service.impl.auth;

import static org.junit.Assert.assertEquals;

import com.bestpay.bigdata.bi.database.dao.usergroupmanage.UserManageUserInfoDO;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserInfoTreeResponse;
import com.bestpay.bigdata.bi.report.usermanage.service.DbUserManageService;
import java.util.ArrayList;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class ObjectAuthServiceImplTest {

//  @InjectMocks
//  private ObjectAuthServiceImpl objectAuthServiceImpl;
//
//  @Mock
//  private ObjectAuthDAOService objectAuthDAOService;
//
//  @Mock
//  private UserGroupUserInfoMapper userGroupUserInfoMapper;

  @InjectMocks
  private DbUserManageService userGroupUserInfoService;

  @Before
  public void setUp() throws Exception {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testUserInfoTree() throws Exception {
    UserManageUserInfoDO userGroupUserInfoDO1 = new UserManageUserInfoDO();
    userGroupUserInfoDO1.setAccountName("tangye");
    userGroupUserInfoDO1.setOneId("oneID_1");
    userGroupUserInfoDO1.setUserStatus(1);
    userGroupUserInfoDO1.setOrgCode("20005");
    userGroupUserInfoDO1.setOrgName("人工智能研究院");
    userGroupUserInfoDO1.setId(1L);

    UserManageUserInfoDO userGroupUserInfoDO2 = new UserManageUserInfoDO();
    userGroupUserInfoDO2.setAccountName("test_user");
    userGroupUserInfoDO2.setOneId("oneID_2");
    userGroupUserInfoDO2.setUserStatus(1);
    userGroupUserInfoDO2.setOrgCode("20005");
    userGroupUserInfoDO2.setOrgName("人工智能研究院");
    userGroupUserInfoDO2.setId(2L);

    UserManageUserInfoDO userGroupUserInfoDO3 = new UserManageUserInfoDO();
    userGroupUserInfoDO3.setAccountName("test_user_2");
    userGroupUserInfoDO3.setOneId("oneID_3");
    userGroupUserInfoDO3.setUserStatus(2);
    userGroupUserInfoDO3.setOrgCode("20005");
    userGroupUserInfoDO3.setOrgName("人工智能研究院");
    userGroupUserInfoDO3.setId(3L);

    List<UserManageUserInfoDO> list = new ArrayList<>();
    list.add(userGroupUserInfoDO1);
    list.add(userGroupUserInfoDO2);
    list.add(userGroupUserInfoDO3);
    List<UserInfoTreeResponse> result = userGroupUserInfoService.generateTreeForUserInfo(list);
    //System.out.println(result);
    assertEquals(1, result.size());
    assertEquals("20005", result.get(0).getCode());
    assertEquals("人工智能研究院", result.get(0).getName());
    assertEquals(0, (int) result.get(0).getLevel());
    assertEquals(3, result.get(0).getChildren().size());
    assertEquals("oneID_1", result.get(0).getChildren().get(0).getCode());
    assertEquals("oneID_2", result.get(0).getChildren().get(1).getCode());
    assertEquals("oneID_3", result.get(0).getChildren().get(2).getCode());
  }

}
