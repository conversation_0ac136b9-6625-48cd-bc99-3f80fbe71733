package com.bestpay.bigdata.bi.report.service.impl.embed;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.alibaba.fastjson.JSONObject;
import com.bestpay.bigdata.bi.common.bean.aiapi.AccountInfo;
import com.bestpay.bigdata.bi.common.bean.aiapi.ApiPlusChannelUserInfo;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.api.common.AppEmbedService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.bean.EmbedPageQueryDTO;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.dao.common.AppEmbedDO;
import com.bestpay.bigdata.bi.report.enums.embed.AppEmbedTypeEnum;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedObjectListRequest;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedOrgGroupVO;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedResourceItem;
import com.bestpay.bigdata.bi.report.service.auth.ObjectAuthService;
import com.bestpay.bigdata.bi.report.service.embed.MobileEmailDecryptService;
import com.bestpay.bigdata.bi.report.service.report.UrlGenerateService;
import com.bestpay.bigdata.bi.report.usermanage.service.AiPlusUserManageService;
import com.bestpay.bigdata.bi.report.usermanage.service.DbUserManageService;
import com.bestpay.bigdata.bi.report.usermanage.service.EmailUserManageService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;


/**
 * <AUTHOR>
 * @ClassName ReportAppEmbedServiceImplTest
 * @description ReportAppEmbedServiceImplTest单元测试
 * @date 2025/6/6
 */
public class ReportAppEmbedServiceImplTest {


  @InjectMocks
  ReportAppEmbedServiceImpl reportAppEmbedServiceImpl;

  @Mock
  ApolloRefreshConfig apolloRefreshConfig;

  @Mock
  AiPlusUserManageService aiPlusUserManageService;

  @Mock
  EmailUserManageService emailUserManageService;


  @Mock
  AppEmbedService appEmbedService;


  @Mock
  ObjectAuthService objectAuthService;

  @Mock
  DashboardDaoService dashboardDaoService;

  @Mock
  DbUserManageService dbUserManageService;

  @Mock
  private UrlGenerateService urlGenerateService;

  @Before
  public void setUp() throws Exception {
    MockitoAnnotations.initMocks(this);
//    MockitoAnnotations.openMocks(this);

    String mail = "<EMAIL>";
    when(apolloRefreshConfig.getAppWhiteList()).thenReturn(mail);

    UserInfo userInfo = new UserInfo();
    userInfo.setEmail(mail);
    userInfo.setAccount("limin");
    userInfo.setIsManager(true);

    when(emailUserManageService.getUserInfoByEmail(mail)).thenReturn(userInfo);
    when(appEmbedService.pageQuery(any(EmbedPageQueryDTO.class))).thenReturn(Collections.emptyList());

    //mock 组织
    String orgsString = "[{\"code\":\"20011\",\"name\":\"省公司\"},{\"code\":\"11612\",\"name\":\"权益事业部\"},{\"code\":\"11613\",\"name\":\"新消费事业部\"},{\"code\":\"11006\",\"name\":\"支付事业部\"},{\"code\":\"10804\",\"name\":\"消费金融事业部\"},{\"code\":\"11604\",\"name\":\"零售金融事业部\"},{\"code\":\"20002\",\"name\":\"企业金融事业部\"},{\"code\":\"20001\",\"name\":\"数字科技事业群\"},{\"code\":\"11404\",\"name\":\"市场合作部\"},{\"code\":\"11602\",\"name\":\"风险管理部\"},{\"code\":\"11606\",\"name\":\"技术与大数据平台部\"},{\"code\":\"11802\",\"name\":\"运营核算部\"},{\"code\":\"20005\",\"name\":\"大数据与人工智能研究院\"},{\"code\":\"20006\",\"name\":\"综合部（安全保卫部）\"},{\"code\":\"20009\",\"name\":\"法律合规部\"},{\"code\":\"20007\",\"name\":\"财务资金部\"},{\"code\":\"20008\",\"name\":\"人力资源部\"},{\"code\":\"20004\",\"name\":\"客户权益部\"},{\"code\":\"10087\",\"name\":\"深化改革办公室\"},{\"code\":\"11607\",\"name\":\"网络与信息安全部\"},{\"code\":\"20012\",\"name\":\"党委办公室\"},{\"code\":\"20014\",\"name\":\"纪委办公室\"},{\"code\":\"20015\",\"name\":\"工会委员会\"},{\"code\":\"20016\",\"name\":\"团委\"},{\"code\":\"20017\",\"name\":\"金融科技事业部\"},{\"code\":\"20010\",\"name\":\"管理层\"},{\"code\":\"90001\",\"name\":\"测试组织\"}]";
    List<Org> orgs = JSONObject.parseArray(orgsString, Org.class);
    when(aiPlusUserManageService.getOrgList()).thenReturn(orgs);


    AccountInfo accountInfo = new AccountInfo();
    accountInfo.setAccount("limin");
    accountInfo.setUuid("123456");
    ApiPlusChannelUserInfo apiPlusChannelUserInfo = new ApiPlusChannelUserInfo();
    apiPlusChannelUserInfo.setAccount("limin");
    apiPlusChannelUserInfo.setStatus(1);
    apiPlusChannelUserInfo.setAccountInfoList(Collections.singletonList(accountInfo));
    when(aiPlusUserManageService.getAiPlusChannelUserInfoByIptid(anyString())).thenReturn(apiPlusChannelUserInfo);

    UserInfo userInfo2 = new UserInfo();
    userInfo2.setEmail("<EMAIL>");
    userInfo2.setAccount("limin");
    userInfo2.setIsManager(false);
    when(dbUserManageService.getUserInfoByOneId(anyString())).thenReturn(userInfo2);

  }

  /**
   * 无符合要求的订阅任务，查询订阅任务数据库为空 邮箱为白名单邮箱
   */
  @Test
  public void testQueryAppEmbedObjectList_EmptyResult() {
    AppEmbedObjectListRequest request = new AppEmbedObjectListRequest();
    request.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    request.setIsPublishMobile(1);
    request.setIptid("**************");


    //mock 查询订阅任务为空集合
    when(appEmbedService.pageQuery(any(EmbedPageQueryDTO.class))).thenReturn(Collections.emptyList());

    // Execute
    Response<List<AppEmbedOrgGroupVO>> response = reportAppEmbedServiceImpl.queryAppEmbedObjectList(request);

    // Verify
    assertTrue(response.getData().isEmpty());
    verify(appEmbedService).pageQuery(any());
  }

  /**
   * <p>
   * 非管理员且授权信息为空
   */
  @Test
  public void testQueryAppEmbedObjectList_NoAuthResource() {

    UserInfo userInfo = new UserInfo();
    userInfo.setAccount("limin");
    userInfo.setIsManager(false);

    // 调用入参
    AppEmbedObjectListRequest request = new AppEmbedObjectListRequest();
    request.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    request.setIsPublishMobile(1);
    request.setIptid("**************");

    //mock 订阅任务数据
    AppEmbedDO embed = new AppEmbedDO();
    embed.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    embed.setEmbedObjectId(1234L);
    embed.setOrgCode("org1");
    embed.setOwnerName("test");

    when(appEmbedService.pageQuery(any(EmbedPageQueryDTO.class))).thenReturn(Collections.singletonList(embed));

    //mock 授权信息为空
    when(objectAuthService.getAuthResourceList()).thenReturn(new HashMap<>());

    //mock 当前仪表板的信息
    Dashboard dashboard = new Dashboard();
    dashboard.setOwnerName("test");
    dashboard.setOwnerEmail("<EMAIL>");

    when(dashboardDaoService.getById(anyLong())).thenReturn(dashboard);

    // Execute
    Response<List<AppEmbedOrgGroupVO>> response = reportAppEmbedServiceImpl.queryAppEmbedObjectList(request);

    assertTrue(response.getData().isEmpty());
  }

  /**
   * 当前查询人是仪表板负责人，但是非管理员
   */
  @Test
  public void testQueryAppEmbedObjectList_UserIsOwner() {

    UserInfo userInfo = new UserInfo();
    userInfo.setEmail("<EMAIL>");
    userInfo.setAccount("limin");
    userInfo.setIsManager(false);


    // 调用入参
    AppEmbedObjectListRequest request = new AppEmbedObjectListRequest();
    request.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    request.setIsPublishMobile(1);
    request.setIptid("**************");

    //mock 订阅任务数据
    ArrayList<AppEmbedDO> appEmbedDos = new ArrayList<>();
    AppEmbedDO embed = new AppEmbedDO();
    embed.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    embed.setEmbedObjectId(1234L);
    embed.setEmbedObjectName("测试仪表板");
    embed.setOwnerName("limin");
    appEmbedDos.add(embed);

    AppEmbedDO embed1 = new AppEmbedDO();
    embed1.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    embed1.setEmbedObjectId(1232L);
    embed1.setEmbedObjectName("aa测试仪表板");
    embed1.setOwnerName("limin");
    appEmbedDos.add(embed1);

    when(appEmbedService.pageQuery(any(EmbedPageQueryDTO.class))).thenReturn(appEmbedDos);

    //mock 授权信息为空
    when(objectAuthService.getAuthResourceList()).thenReturn(new HashMap<>());

    //mock 当前仪表板的信息
    Dashboard dashboard = new Dashboard();
    dashboard.setOwnerEn("limin");
    dashboard.setOrgCode("20010");
    dashboard.setId(1232L);
    dashboard.setOwnerEmail("<EMAIL>");
    dashboard.setUpdatedAt(new Date().toInstant());

    Dashboard dashboard1 = new Dashboard();
    dashboard1.setOwnerName("测试");
    dashboard1.setOwnerEn("limin");
    dashboard1.setId(1234L);
    dashboard1.setOrgCode("20005");
    dashboard1.setOwnerEmail("<EMAIL>");
    dashboard1.setUpdatedAt(new Date().toInstant());


    when(dashboardDaoService.getById(1232L)).thenReturn(dashboard);
    when(dashboardDaoService.getById(1234L)).thenReturn(dashboard1);

    // Execute
    Response<List<AppEmbedOrgGroupVO>> response = reportAppEmbedServiceImpl.queryAppEmbedObjectList(request);

    assertEquals(2, response.getData().size());
    System.out.println(response.getData());

  }

  /**
   * 当前查询人非仪表板负责人，是管理员,查看所有仪表板,同时验证分组
   */
  @Test
  public void testQueryAppEmbedObjectList_UserIsManager() {


    // 调用入参
    AppEmbedObjectListRequest request = new AppEmbedObjectListRequest();
    request.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    request.setIsPublishMobile(1);
    request.setIptid("3487398457");

    //mock 订阅任务数据
    ArrayList<AppEmbedDO> appEmbedDos = new ArrayList<>();
    AppEmbedDO embed = new AppEmbedDO();
    embed.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    embed.setEmbedObjectId(1232L);
    embed.setEmbedObjectName("测试仪表板");
    embed.setOrgCode("20005");
    embed.setOwnerName("test");
    appEmbedDos.add(embed);

    AppEmbedDO embed1 = new AppEmbedDO();
    embed1.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    embed1.setEmbedObjectId(1234L);
    embed1.setEmbedObjectName("aa测试仪表板");
    embed1.setOrgCode("20010");
    embed1.setOwnerName("limin");
    appEmbedDos.add(embed1);

    when(appEmbedService.pageQuery(any(EmbedPageQueryDTO.class))).thenReturn(appEmbedDos);

    //mock 授权信息为空
    when(objectAuthService.getAuthResourceList()).thenReturn(new HashMap<>());

    //mock 当前仪表板的信息
    Dashboard dashboard = new Dashboard();
    dashboard.setOwnerName("李民");
    dashboard.setOwnerEn("limin");
    dashboard.setId(1234L);
    dashboard.setOrgCode("20010");
    dashboard.setOwnerEmail("<EMAIL>");
    dashboard.setUpdatedAt(new Date().toInstant());

    Dashboard dashboard1 = new Dashboard();
    dashboard1.setOwnerName("测试");
    dashboard1.setOwnerEn("test");
    dashboard1.setId(1232L);
    dashboard1.setOrgCode("20005");
    dashboard1.setOwnerEmail("<EMAIL>");
    dashboard1.setUpdatedAt(new Date().toInstant());

    when(dashboardDaoService.getById(1234L)).thenReturn(dashboard);
    when(dashboardDaoService.getById(1232L)).thenReturn(dashboard1);

    // Execute
    Response<List<AppEmbedOrgGroupVO>> response = reportAppEmbedServiceImpl.queryAppEmbedObjectList(request);

    assertEquals(1, response.getData().size());
    System.out.println(response.getData());

  }

  /**
   * 移动端优先组织排在最前
   */
  @Test
  public void testQueryAppEmbedObjectList_OrganizationOrderMatch_ReturnsPrioritized() {


    // 调用入参
    AppEmbedObjectListRequest request = new AppEmbedObjectListRequest();
    request.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    request.setIsPublishMobile(1);
    request.setIptid("3247423483");

    //mock 订阅任务数据
    ArrayList<AppEmbedDO> appEmbedDos = new ArrayList<>();
    AppEmbedDO embed = new AppEmbedDO();
    embed.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    embed.setEmbedObjectId(1232L);
    embed.setEmbedObjectName("测试仪表板");
    embed.setOrgCode("20005");
    embed.setOwnerName("test");
    appEmbedDos.add(embed);

    AppEmbedDO embed1 = new AppEmbedDO();
    embed1.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    embed1.setEmbedObjectId(1234L);
    embed1.setEmbedObjectName("aa测试仪表板");
    embed1.setOrgCode("20010");
    embed1.setOwnerName("limin");
    appEmbedDos.add(embed1);

    when(appEmbedService.pageQuery(any(EmbedPageQueryDTO.class))).thenReturn(appEmbedDos);

    //mock 授权信息为空
    when(objectAuthService.getAuthResourceList()).thenReturn(new HashMap<>());

    //mock 当前仪表板的信息
    Dashboard dashboard = new Dashboard();
    dashboard.setOwnerName("李民");
    dashboard.setOwnerEn("limin");
    dashboard.setId(1234L);
    dashboard.setOrgCode("20010");
    dashboard.setOwnerEmail("<EMAIL>");
    dashboard.setUpdatedAt(new Date().toInstant());

    Dashboard dashboard1 = new Dashboard();
    dashboard1.setOwnerName("测试");
    dashboard1.setOwnerEn("test");
    dashboard1.setId(1232L);
    dashboard1.setOrgCode("20005");
    dashboard1.setOwnerEmail("<EMAIL>");
    dashboard1.setUpdatedAt(new Date().toInstant());

    when(dashboardDaoService.getById(1234L)).thenReturn(dashboard);
    when(dashboardDaoService.getById(1232L)).thenReturn(dashboard1);

    //mock Apollo参数，优先级组织
    when(apolloRefreshConfig.getAppEmbedMobileOrderFirstOrgCode()).thenReturn("20010");

    // Execute
    Response<List<AppEmbedOrgGroupVO>> response = reportAppEmbedServiceImpl.queryAppEmbedObjectList(request);

    assertEquals(2, response.getData().size());
    System.out.println(response.getData());
    assertNotNull(response);
    assertTrue(response.isSuccess());
    List<AppEmbedOrgGroupVO> result = response.getData();
    assertNotNull(result);
    assertEquals(2, result.size());
    // 应该排在最前面
    assertEquals("管理层", result.get(0).getOrgName());
    assertEquals("大数据与人工智能研究院", result.get(1).getOrgName());
  }

  /**
   * 验证同一个组织下的仪表板是否按照 objectName 字典顺序排序
   */
  @Test
  public void testQueryAppEmbedObjectList_GroupedItemsSortedByName() {


    // 调用入参
    AppEmbedObjectListRequest request = new AppEmbedObjectListRequest();
    request.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    request.setIsPublishMobile(1);
    request.setIptid("<EMAIL>");

    //mock 订阅任务数据
    ArrayList<AppEmbedDO> appEmbedDos = new ArrayList<>();
    AppEmbedDO embed = new AppEmbedDO();
    embed.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    embed.setEmbedObjectId(1232L);
    embed.setEmbedObjectName("测试仪表板");
    embed.setOwnerName("test");
    appEmbedDos.add(embed);

    AppEmbedDO embed1 = new AppEmbedDO();
    embed1.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    embed1.setEmbedObjectId(1234L);
    embed1.setEmbedObjectName("bb测试仪表板");
    embed1.setOwnerName("limin");
    appEmbedDos.add(embed1);

    AppEmbedDO embed2 = new AppEmbedDO();
    embed2.setEmbedType(AppEmbedTypeEnum.DASHBOARD.getCode());
    embed2.setEmbedObjectId(1231L);
    embed2.setEmbedObjectName("aa测试仪表板2");
    embed2.setOwnerName("limin");
    appEmbedDos.add(embed2);

    when(appEmbedService.pageQuery(any(EmbedPageQueryDTO.class))).thenReturn(appEmbedDos);

    //mock 授权信息为空
    when(objectAuthService.getAuthResourceList()).thenReturn(new HashMap<>());

    //mock 当前仪表板的信息
    Dashboard dashboard = new Dashboard();
    dashboard.setOwnerName("李民");
    dashboard.setOwnerEn("limin");
    dashboard.setId(1234L);
    dashboard.setName("bb测试仪表板");
    dashboard.setOrgCode("20005");
    dashboard.setOwnerEmail("<EMAIL>");
    dashboard.setUpdatedAt(new Date().toInstant());

    Dashboard dashboard1 = new Dashboard();
    dashboard1.setOwnerName("测试");
    dashboard1.setOwnerEn("test");
    dashboard1.setId(1232L);
    dashboard1.setName("测试仪表板");
    dashboard1.setOrgCode("20010");
    dashboard1.setOwnerEmail("<EMAIL>");
    dashboard1.setUpdatedAt(new Date().toInstant());

    Dashboard dashboard2 = new Dashboard();
    dashboard2.setOwnerName("测试2");
    dashboard2.setName("aa测试仪表板2");
    dashboard2.setOwnerEn("test2");
    dashboard2.setId(1231L);
    dashboard2.setOrgCode("20005");
    dashboard2.setOwnerEmail("<EMAIL>");
    dashboard2.setUpdatedAt(new Date().toInstant());

    when(dashboardDaoService.getById(1234L)).thenReturn(dashboard);
    when(dashboardDaoService.getById(1232L)).thenReturn(dashboard1);
    when(dashboardDaoService.getById(1231L)).thenReturn(dashboard2);

    //mock Apollo参数，优先级组织
    when(apolloRefreshConfig.getAppEmbedMobileOrderFirstOrgCode()).thenReturn("20010");

    // Execute
    Response<List<AppEmbedOrgGroupVO>> response = reportAppEmbedServiceImpl.queryAppEmbedObjectList(request);

    assertEquals(2, response.getData().size());
    System.out.println(response.getData());
    assertNotNull(response);
    assertTrue(response.isSuccess());
    List<AppEmbedOrgGroupVO> result = response.getData();
    assertNotNull(result);
    assertEquals(2, result.size());
    // 应该排在最前面
    assertEquals("管理层", result.get(0).getOrgName());
    assertEquals("大数据与人工智能研究院", result.get(1).getOrgName());

    List<AppEmbedResourceItem> items = result.get(1).getAppEmbedResourceItems();
    assertNotNull(items);
    assertEquals(2, items.size());

    // 验证排序是否为字典顺序：Alpha -> Beta -> Zeta
    assertEquals("aa测试仪表板2", items.get(0).getObjectName());
    assertEquals("bb测试仪表板", items.get(1).getObjectName());
  }
}
