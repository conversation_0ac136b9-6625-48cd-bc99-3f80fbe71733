package com.bestpay.bigdata.bi.report.service.impl.report;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.bestpay.bigdata.bi.report.beforeSQL.picker.fixdate.day.TheDayBeforeYesterdayHandler;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TheDayBeforeYesterdayHandler单元测试
 */
class TheDayBeforeYesterdayHandlerTest {

    @Test
    void getTime_shouldReturnTwoDateTimeStrings() {
        // 准备
        TheDayBeforeYesterdayHandler handler = new TheDayBeforeYesterdayHandler();
        
        // 执行
        List<String> result = handler.getTime();
        
        // 验证
        // 1. 验证返回列表有两个元素
        assertEquals(2, result.size(), "应返回两个日期时间字符串");
        
        // 2. 验证第一个元素是前天的00:00:00
        Date theDayBeforeYesterday = DateUtil.offsetDay(new Date(), -2);
        String expectedStart = DateUtil.format(DateUtil.beginOfDay(theDayBeforeYesterday), DatePattern.NORM_DATETIME_PATTERN);
        assertEquals(expectedStart, result.get(0), "第一个元素应是前天的开始时间");
        
        // 3. 验证第二个元素是前天的23:59:59
        String expectedEnd = DateUtil.format(DateUtil.endOfDay(theDayBeforeYesterday), DatePattern.NORM_DATETIME_PATTERN);
        assertEquals(expectedEnd, result.get(1), "第二个元素应是前天的结束时间");
        
        // 4. 验证日期格式
        assertTrue(result.get(0).matches("\\d{4}-\\d{2}-\\d{2} 00:00:00"), "日期格式不正确");
        assertTrue(result.get(1).matches("\\d{4}-\\d{2}-\\d{2} 23:59:59"), "日期格式不正确");
    }
}