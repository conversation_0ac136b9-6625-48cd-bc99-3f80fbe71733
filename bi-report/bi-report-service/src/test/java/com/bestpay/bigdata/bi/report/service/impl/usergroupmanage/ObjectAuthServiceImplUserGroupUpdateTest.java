package com.bestpay.bigdata.bi.report.service.impl.usergroupmanage;

import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.common.ObjectAuthDAOService;
import com.bestpay.bigdata.bi.database.bean.usergroupmanage.ObjectAuthDTO;
import com.bestpay.bigdata.bi.database.dao.common.ObjectAuthDo;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserGroupTreeResponse;
import com.bestpay.bigdata.bi.report.request.auth.UserGroupUpdateAuthRequest;
import com.bestpay.bigdata.bi.report.service.impl.auth.ObjectAuthServiceImpl;
import com.bestpay.bigdata.bi.report.usermanage.service.UserGroupManagerService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * ObjectAuthServiceImpl.userGroupUpdate 方法的单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class ObjectAuthServiceImplUserGroupUpdateTest {

    @InjectMocks
    private ObjectAuthServiceImpl objectAuthService;

    @Mock
    private ObjectAuthDAOService objectAuthDAOService;

    @Mock
    private UserGroupManagerService userGroupManagerService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试权限低于父节点的情况，应抛出异常
     */
    @Test
    public void testUserGroupUpdate_PermissionLowerThanParent_ThrowsException() {
        // 准备数据
        UserGroupUpdateAuthRequest request = new UserGroupUpdateAuthRequest();
        request.setAuthOperate("no");
        request.setAuthResourceId("1");
        request.setAuthResourceType("report");
        request.setUserGroupCode("child");

        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("testUser");
        
        try (MockedStatic<UserContextUtil> mockedUserContextUtil = mockStatic(UserContextUtil.class)) {
            mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(userInfo);

            // 模拟用户组树结构
            List<UserGroupTreeResponse> tree = new ArrayList<>();
            UserGroupTreeResponse parent = UserGroupTreeResponse.builder().orgCode("parent").children(new ArrayList<>()).build();
            UserGroupTreeResponse child = UserGroupTreeResponse.builder().orgCode("child").build();
            parent.getChildren().add(child);
            tree.add(parent);
            when(userGroupManagerService.searchTree()).thenReturn(tree);

            // 模拟父节点权限为 edit
            List<ObjectAuthDo> parentAuths = new ArrayList<>();
            ObjectAuthDo parentAuth = new ObjectAuthDo();
            parentAuth.setAuthUser("parent");
            parentAuth.setAuthOperate("edit");
            parentAuths.add(parentAuth);
            when(objectAuthDAOService.queryByConditions(anyList(), anyString(), anyString())).thenReturn(parentAuths);
            // 这个方法没有返回值，则不用做返回值校验
            objectAuthService.userGroupUpdate(request);

        }
    }

    /**
     * 测试权限等于父节点且存在当前权限记录，应更新状态码为9
     */
    @Test
    public void testUserGroupUpdate_PermissionEqualParentAndExistsCurrentAuth_UpdatesStatusCode() {
        // 准备数据
        UserGroupUpdateAuthRequest request = new UserGroupUpdateAuthRequest();
        request.setAuthOperate("edit");
        request.setAuthResourceId("1");
        request.setAuthResourceType("report");
        request.setUserGroupCode("child");

        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("testUser");
        
        try (MockedStatic<UserContextUtil> mockedUserContextUtil = mockStatic(UserContextUtil.class)) {
            mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(userInfo);

            // 模拟用户组树结构
            List<UserGroupTreeResponse> tree = new ArrayList<>();
            UserGroupTreeResponse parent = UserGroupTreeResponse.builder().orgCode("parent").children(new ArrayList<>()).build();
            UserGroupTreeResponse child = UserGroupTreeResponse.builder().orgCode("child").build();
            parent.getChildren().add(child);
            tree.add(parent);
            when(userGroupManagerService.searchTree()).thenReturn(tree);

            // 模拟父节点权限为 edit
            List<ObjectAuthDo> parentAuths = new ArrayList<>();
            ObjectAuthDo parentAuth = new ObjectAuthDo();
            parentAuth.setAuthUser("parent");
            parentAuth.setAuthOperate("edit");
            parentAuths.add(parentAuth);
            when(objectAuthDAOService.queryByConditions(anyList(), anyString(), anyString())).thenReturn(parentAuths);

            // 模拟当前权限记录
            List<ObjectAuthDo> currentAuths = new ArrayList<>();
            ObjectAuthDo currentAuth = new ObjectAuthDo();
            currentAuth.setId(1L);
            currentAuths.add(currentAuth);
            when(objectAuthDAOService.queryByConditions(eq(Collections.singletonList("child")), anyString(), anyString())).thenReturn(currentAuths);

            // 执行测试
            objectAuthService.userGroupUpdate(request);

            // 验证更新操作
            ArgumentCaptor<ObjectAuthDo> updateCaptor = ArgumentCaptor.forClass(ObjectAuthDo.class);
            verify(objectAuthDAOService).update(updateCaptor.capture());
            ObjectAuthDo updatedAuth = updateCaptor.getValue();
            updatedAuth.setStatusCode(9);
            assertEquals(1L, updatedAuth.getId().longValue());
            assertEquals("edit", updatedAuth.getAuthOperate());
            assertEquals(9, updatedAuth.getStatusCode().intValue());
        }
    }

    /**
     * 测试权限高于父节点且存在当前权限记录，应更新权限
     */
    @Test
    public void testUserGroupUpdate_PermissionHigherParentAndExistsCurrentAuth_UpdatesAuth() {
        // 准备数据
        UserGroupUpdateAuthRequest request = new UserGroupUpdateAuthRequest();
        request.setAuthOperate("edit");
        request.setAuthResourceId("1");
        request.setAuthResourceType("report");
        request.setUserGroupCode("child");

        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("testUser");
        
        try (MockedStatic<UserContextUtil> mockedUserContextUtil = mockStatic(UserContextUtil.class)) {
            mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(userInfo);

            // 模拟用户组树结构
            List<UserGroupTreeResponse> tree = new ArrayList<>();
            UserGroupTreeResponse parent = UserGroupTreeResponse.builder().orgCode("parent").children(new ArrayList<>()).build();
            UserGroupTreeResponse child = UserGroupTreeResponse.builder().orgCode("child").build();
            parent.getChildren().add(child);
            tree.add(parent);
            when(userGroupManagerService.searchTree()).thenReturn(tree);

            // 模拟父节点权限为 queryAndExport
            List<ObjectAuthDo> parentAuths = new ArrayList<>();
            ObjectAuthDo parentAuth = new ObjectAuthDo();
            parentAuth.setAuthUser("parent");
            parentAuth.setAuthOperate("queryAndExport");
            parentAuths.add(parentAuth);
            when(objectAuthDAOService.queryByConditions(anyList(), anyString(), anyString())).thenReturn(parentAuths);

            // 模拟当前权限记录
            List<ObjectAuthDo> currentAuths = new ArrayList<>();
            ObjectAuthDo currentAuth = new ObjectAuthDo();
            currentAuth.setId(1L);
            currentAuths.add(currentAuth);
            when(objectAuthDAOService.queryByConditions(eq(Collections.singletonList("child")), anyString(), anyString())).thenReturn(currentAuths);

            // 执行测试
            objectAuthService.userGroupUpdate(request);

            // 验证更新操作
            ArgumentCaptor<ObjectAuthDo> updateCaptor = ArgumentCaptor.forClass(ObjectAuthDo.class);
            verify(objectAuthDAOService).update(updateCaptor.capture());
            ObjectAuthDo updatedAuth = updateCaptor.getValue();
            assertEquals(1L, updatedAuth.getId().longValue());
            assertEquals("edit", updatedAuth.getAuthOperate());
        }
    }

    /**
     * 测试权限高于父节点且不存在当前权限记录，应插入新权限
     */
    @Test
    public void testUserGroupUpdate_PermissionHigherParentAndNotExistsCurrentAuth_InsertsAuth() {
        // 准备数据
        UserGroupUpdateAuthRequest request = new UserGroupUpdateAuthRequest();
        request.setAuthOperate("edit");
        request.setAuthResourceId("1");
        request.setAuthResourceType("report");
        request.setUserGroupCode("child");

        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("testUser");
        
        try (MockedStatic<UserContextUtil> mockedUserContextUtil = mockStatic(UserContextUtil.class)) {
            mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(userInfo);

            // 模拟用户组树结构
            List<UserGroupTreeResponse> tree = new ArrayList<>();
            UserGroupTreeResponse parent = UserGroupTreeResponse.builder().orgCode("parent").children(new ArrayList<>()).build();
            UserGroupTreeResponse child = UserGroupTreeResponse.builder().orgCode("child").build();
            parent.getChildren().add(child);
            tree.add(parent);
            when(userGroupManagerService.searchTree()).thenReturn(tree);

            // 模拟父节点权限为 queryAndExport
            List<ObjectAuthDo> parentAuths = new ArrayList<>();
            ObjectAuthDo parentAuth = new ObjectAuthDo();
            parentAuth.setAuthUser("parent");
            parentAuth.setAuthOperate("queryAndExport");
            parentAuths.add(parentAuth);
            when(objectAuthDAOService.queryByConditions(anyList(), anyString(), anyString())).thenReturn(parentAuths);

            // 模拟当前无权限记录
            when(objectAuthDAOService.queryByConditions(eq(Collections.singletonList("child")), anyString(), anyString())).thenReturn(new ArrayList<>());

            // 执行测试
            objectAuthService.userGroupUpdate(request);

            // 验证插入操作
            ArgumentCaptor<ObjectAuthDo> insertCaptor = ArgumentCaptor.forClass(ObjectAuthDo.class);
            verify(objectAuthDAOService).insert(insertCaptor.capture());
            ObjectAuthDo insertedAuth = insertCaptor.getValue();
            assertEquals("1", insertedAuth.getAuthResourceId());
            assertEquals("report", insertedAuth.getAuthResourceType());
            assertEquals("userGroup", insertedAuth.getAuthType());
            assertEquals("child", insertedAuth.getAuthUser());
            assertEquals("edit", insertedAuth.getAuthOperate());
        }
    }

    /**
     * 测试无父节点或祖父节点，应默认权限为no
     */
    @Test
    public void testUserGroupUpdate_NoParentOrGrandparent_DefaultsToNo() {
        // 准备数据
        UserGroupUpdateAuthRequest request = new UserGroupUpdateAuthRequest();
        request.setAuthOperate("edit");
        request.setAuthResourceId("1");
        request.setAuthResourceType("report");
        request.setUserGroupCode("orphan");

        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("testUser");
        
        try (MockedStatic<UserContextUtil> mockedUserContextUtil = mockStatic(UserContextUtil.class)) {
            mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(userInfo);

            // 模拟用户组树结构，无父节点
            List<UserGroupTreeResponse> tree = new ArrayList<>();
            UserGroupTreeResponse orphan = UserGroupTreeResponse.builder().orgCode("orphan").build();
            tree.add(orphan);
            when(userGroupManagerService.searchTree()).thenReturn(tree);

            // 模拟当前无权限记录
            when(objectAuthDAOService.queryByConditions(eq(Collections.singletonList("orphan")), anyString(), anyString())).thenReturn(new ArrayList<>());

            // 执行测试
            objectAuthService.userGroupUpdate(request);

            // 验证插入操作
            ArgumentCaptor<ObjectAuthDo> insertCaptor = ArgumentCaptor.forClass(ObjectAuthDo.class);
            verify(objectAuthDAOService).insert(insertCaptor.capture());
            ObjectAuthDo insertedAuth = insertCaptor.getValue();
            assertEquals("1", insertedAuth.getAuthResourceId());
            assertEquals("report", insertedAuth.getAuthResourceType());
            assertEquals("userGroup", insertedAuth.getAuthType());
            assertEquals("orphan", insertedAuth.getAuthUser());
            assertEquals("edit", insertedAuth.getAuthOperate());
        }
    }

    /**
     * 测试无子孙节点，不应执行删除操作
     */
    @Test
    public void testUserGroupUpdate_NoDescendants_DoesNotDelete() {
        // 准备数据
        UserGroupUpdateAuthRequest request = new UserGroupUpdateAuthRequest();
        request.setAuthOperate("edit");
        request.setAuthResourceId("1");
        request.setAuthResourceType("report");
        request.setUserGroupCode("leaf");

        UserInfo userInfo = new UserInfo();
        userInfo.setAccount("testUser");
        
        try (MockedStatic<UserContextUtil> mockedUserContextUtil = mockStatic(UserContextUtil.class)) {
            mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(userInfo);

            // 模拟用户组树结构，叶子节点
            List<UserGroupTreeResponse> tree = new ArrayList<>();
            UserGroupTreeResponse parent = UserGroupTreeResponse.builder().orgCode("parent").children(new ArrayList<>()).build();
            UserGroupTreeResponse leaf = UserGroupTreeResponse.builder().orgCode("leaf").build();
            parent.getChildren().add(leaf);
            tree.add(parent);
            when(userGroupManagerService.searchTree()).thenReturn(tree);

            // 模拟父节点权限为 queryAndExport
            List<ObjectAuthDo> parentAuths = new ArrayList<>();
            ObjectAuthDo parentAuth = new ObjectAuthDo();
            parentAuth.setAuthUser("parent");
            parentAuth.setAuthOperate("queryAndExport");
            parentAuths.add(parentAuth);
            when(objectAuthDAOService.queryByConditions(anyList(), anyString(), anyString())).thenReturn(parentAuths);

            // 模拟当前无权限记录
            when(objectAuthDAOService.queryByConditions(eq(Collections.singletonList("leaf")), anyString(), anyString())).thenReturn(new ArrayList<>());

            // 执行测试
            objectAuthService.userGroupUpdate(request);

            // 验证未执行删除操作
            verify(objectAuthDAOService, never()).deleteByObjectAuthDTO(any(ObjectAuthDTO.class));
        }
    }
}
