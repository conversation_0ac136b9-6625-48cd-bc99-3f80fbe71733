package com.bestpay.bigdata.bi.report.service.impl.ai;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIParamRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAnalysisByAIRequest;
import com.opencsv.CSVReader;
import java.io.StringReader;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class AIServiceImplTest {

  @InjectMocks
  private AIServiceImpl aiServiceImpl;

  @Before
  public void setUp() throws Exception {
    MockitoAnnotations.initMocks(this);

  }

  @Test
  public void tetGetReportInfoForAI_1() throws Exception{
    // 构建请求参数
    String dashboards="{\n"
        + "    \"cardInfo\": {\n"
        + "        \"cardName\": \"表格_非对比_小计\",\n"
        + "        \"chartType\": 0,\n"
        + "        \"reportName\": \"表格_非对比_小计\"\n"
        + "    },\n"
        + "    \"paramsInfo\": {\n"
        + "        \"isRealtime\": false,\n"
        + "        \"cardCode\": \"report-5d80ce87-c9eb-4c5e-8e43-6d4aecdcd24a\",\n"
        + "        \"cardType\": \"report\",\n"
        + "        \"dashboardId\": \"839\",\n"
        + "        \"queryConditions\": [],\n"
        + "        \"paramConditions\": [],\n"
        + "        \"fromFlag\": \"DASHBOARD\",\n"
        + "        \"pageSize\": 30,\n"
        + "        \"pageNum\": 1,\n"
        + "        \"rollAndDownRequest\": {},\n"
        + "        \"noCount\": true,\n"
        + "        \"orderColumns\": null\n"
        + "    },\n"
        + "    \"returnData\": {\n"
        + "        \"columnNameMaps\": [\n"
        + "            {\n"
        + "                \"id\": 18455,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"report9a187aae-d391-4ff2-a1b1-b6e418e6f1dd\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": null,\n"
        + "                \"prop\": \"new_old_customer_0\",\n"
        + "                \"label\": \"新老客\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"dataType\": null,\n"
        + "                \"decimaCarry\": null,\n"
        + "                \"unit\": null,\n"
        + "                \"isShowUnit\": null,\n"
        + "                \"showThousandth\": null,\n"
        + "                \"nickName\": null,\n"
        + "                \"showSubtotal\": true,\n"
        + "                \"showTypeName\": \"CHARACTER_SELECT\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": null,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            },\n"
        + "            {\n"
        + "                \"id\": 18735,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"report9b8e172d-794d-45d9-9de3-540b1e2ae36f\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": null,\n"
        + "                \"prop\": \"application_time_11_format_3\",\n"
        + "                \"label\": \"申请时间(年)\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"dataType\": null,\n"
        + "                \"decimaCarry\": null,\n"
        + "                \"unit\": null,\n"
        + "                \"isShowUnit\": null,\n"
        + "                \"showThousandth\": null,\n"
        + "                \"nickName\": null,\n"
        + "                \"showSubtotal\": true,\n"
        + "                \"showTypeName\": \"DATETIME\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": 3,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            },\n"
        + "            {\n"
        + "                \"id\": 18737,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"report54bb4b9d-acd6-48c9-98d2-cd5994d09cc9\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": null,\n"
        + "                \"prop\": \"operator_13\",\n"
        + "                \"label\": \"运营商\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"dataType\": null,\n"
        + "                \"decimaCarry\": null,\n"
        + "                \"unit\": null,\n"
        + "                \"isShowUnit\": null,\n"
        + "                \"showThousandth\": null,\n"
        + "                \"nickName\": null,\n"
        + "                \"showSubtotal\": false,\n"
        + "                \"showTypeName\": \"CHARACTER_SELECT\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": null,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            },\n"
        + "            {\n"
        + "                \"id\": 18736,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"reporta0c6eceb-c4da-44c3-b897-3653714b6402\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": null,\n"
        + "                \"prop\": \"province_12\",\n"
        + "                \"label\": \"省份\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"dataType\": null,\n"
        + "                \"decimaCarry\": null,\n"
        + "                \"unit\": null,\n"
        + "                \"isShowUnit\": null,\n"
        + "                \"showThousandth\": null,\n"
        + "                \"nickName\": null,\n"
        + "                \"showSubtotal\": false,\n"
        + "                \"showTypeName\": \"CHARACTER_SELECT\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": null,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            },\n"
        + "            {\n"
        + "                \"id\": 18460,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"report3f1ad854-1c5f-4ff8-aaed-22a0ff98e06a\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": \"sum\",\n"
        + "                \"prop\": \"toFloat64OrZero_toNullable__toString__approved_credit_amount_5______indexsum\",\n"
        + "                \"label\": \"成功授信金额\",\n"
        + "                \"reportField\": \"index\",\n"
        + "                \"dataType\": \"数值\",\n"
        + "                \"decimaCarry\": 0,\n"
        + "                \"unit\": 1,\n"
        + "                \"isShowUnit\": true,\n"
        + "                \"showThousandth\": false,\n"
        + "                \"nickName\": \"成功授信金额\",\n"
        + "                \"showSubtotal\": false,\n"
        + "                \"showTypeName\": \"DECIMAL\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": null,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            },\n"
        + "            {\n"
        + "                \"id\": 18461,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"report0bb5db1a-44b7-4df3-8ca9-232d9a23cfaf\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": \"sum\",\n"
        + "                \"prop\": \"toFloat64OrZero_toNullable__toString__user_application_count_6______indexsum_1\",\n"
        + "                \"label\": \"用户申请次数\",\n"
        + "                \"reportField\": \"index\",\n"
        + "                \"dataType\": \"数值\",\n"
        + "                \"decimaCarry\": 0,\n"
        + "                \"unit\": 1,\n"
        + "                \"isShowUnit\": true,\n"
        + "                \"showThousandth\": false,\n"
        + "                \"nickName\": \"用户申请次数\",\n"
        + "                \"showSubtotal\": false,\n"
        + "                \"showTypeName\": \"DECIMAL\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": null,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            }\n"
        + "        ],\n"
        + "        \"data\": [\n"
        + "            {\n"
        + "                \"application_time_11_format_3\": \"--\",\n"
        + "                \"toFloat64OrZero_toNullable__toString__user_application_count_6______indexsum_1\": \"11310\",\n"
        + "                \"new_old_customer_0\": \"总计\",\n"
        + "                \"operator_13\": \"--\",\n"
        + "                \"toFloat64OrZero_toNullable__toString__approved_credit_amount_5______indexsum\": \"13245756910\",\n"
        + "                \"province_12\": \"--\"\n"
        + "            },\n"
        + "            {\n"
        + "                \"application_time_11_format_3\": \"2020\",\n"
        + "                \"toFloat64OrZero_toNullable__toString__user_application_count_6______indexsum_1\": \"260\",\n"
        + "                \"new_old_customer_0\": \"老客\",\n"
        + "                \"operator_13\": \"联通\",\n"
        + "                \"toFloat64OrZero_toNullable__toString__approved_credit_amount_5______indexsum\": \"732618186\",\n"
        + "                \"province_12\": \"浙江省\"\n"
        + "            },\n"
        + "            {\n"
        + "                \"application_time_11_format_3\": \"2022\",\n"
        + "                \"toFloat64OrZero_toNullable__toString__user_application_count_6______indexsum_1\": \"215\",\n"
        + "                \"new_old_customer_0\": \"老客\",\n"
        + "                \"operator_13\": \"其他\",\n"
        + "                \"toFloat64OrZero_toNullable__toString__approved_credit_amount_5______indexsum\": \"4465845\",\n"
        + "                \"province_12\": \"浙江省\"\n"
        + "            }\n"
        + "        ]\n"
        + "    }\n"
        + "}";
    DashboardAnalysisByAIRequest param=JSONUtil.toBean(dashboards,DashboardAnalysisByAIRequest.class);
    List<DashboardAnalysisByAIParamRequest> result=aiServiceImpl.getDashboardInfoForAI(ListUtil.of(param));
    assertEquals(1, result.size());
    assertEquals("表格_非对比_小计", result.get(0).getCardName());
    assertEquals("表格", result.get(0).getChartType());
    String csvStr=result.get(0).getData();
    CSVReader reader = new CSVReader(new StringReader(csvStr));
    String[] header=reader.readNext();
    assertEquals(6, header.length);
    assertEquals("新老客", header[0]);
    assertEquals("申请时间(年)", header[1]);
    assertEquals("运营商", header[2]);
    assertEquals("省份", header[3]);
    assertEquals("成功授信金额", header[4]);
    assertEquals("用户申请次数", header[5]);
    String[] l1=reader.readNext();
    assertEquals(6, l1.length);
    assertEquals(6, header.length);
    assertEquals("总计", l1[0]);
    assertEquals("--", l1[1]);
    assertEquals("--", l1[2]);
    assertEquals("--", l1[3]);
    assertEquals("13245756910", l1[4]);
    assertEquals("11310", l1[5]);
    String[] l2=reader.readNext();
    assertEquals(6, l2.length);
    assertEquals("老客", l2[0]);
    assertEquals("2020", l2[1]);
    assertEquals("联通", l2[2]);
    assertEquals("浙江省", l2[3]);
    assertEquals("732618186", l2[4]);
    assertEquals("260", l2[5]);
    String[] l3=reader.readNext();
    assertEquals(6, l3.length);
    assertEquals("老客", l3[0]);
    assertEquals("2022", l3[1]);
    assertEquals("其他", l3[2]);
    assertEquals("浙江省", l3[3]);
    assertEquals("4465845", l3[4]);
    assertEquals("215", l3[5]);
  }

  @Test
  public void tetGetReportInfoForAI_2() throws Exception{
    // 构建请求参数
    String dashboards="{\n"
        + "    \"cardInfo\": {\n"
        + "        \"cardName\": \"折线图\",\n"
        + "        \"chartType\": 2,\n"
        + "        \"reportName\": \"折线图\"\n"
        + "    },\n"
        + "    \"paramsInfo\": {\n"
        + "        \"isRealtime\": false,\n"
        + "        \"cardCode\": \"report-6787c59e-a2e5-487f-b146-c5f6f46b6205\",\n"
        + "        \"cardType\": \"report\",\n"
        + "        \"dashboardId\": \"839\",\n"
        + "        \"queryConditions\": [],\n"
        + "        \"paramConditions\": [],\n"
        + "        \"fromFlag\": \"DASHBOARD\",\n"
        + "        \"pageSize\": -1,\n"
        + "        \"pageNum\": -1,\n"
        + "        \"rollAndDownRequest\": {},\n"
        + "        \"noCount\": true\n"
        + "    },\n"
        + "    \"returnData\": {\n"
        + "        \"columnNameMaps\": [\n"
        + "            {\n"
        + "                \"id\": 18735,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"reportAB8D38D7-4DBE-42DB-85AE-4B0C9C15481A\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": null,\n"
        + "                \"prop\": \"application_time_11_format_3\",\n"
        + "                \"label\": \"申请时间(年)\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"dataType\": null,\n"
        + "                \"decimaCarry\": null,\n"
        + "                \"unit\": null,\n"
        + "                \"isShowUnit\": null,\n"
        + "                \"showThousandth\": null,\n"
        + "                \"nickName\": null,\n"
        + "                \"showSubtotal\": false,\n"
        + "                \"showTypeName\": \"DATETIME\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": 3,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            },\n"
        + "            {\n"
        + "                \"id\": 18736,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"reportC1684D43-D987-4167-8139-8617502979E7\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": null,\n"
        + "                \"prop\": \"province_12\",\n"
        + "                \"label\": \"省份\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"dataType\": null,\n"
        + "                \"decimaCarry\": null,\n"
        + "                \"unit\": null,\n"
        + "                \"isShowUnit\": null,\n"
        + "                \"showThousandth\": null,\n"
        + "                \"nickName\": null,\n"
        + "                \"showSubtotal\": false,\n"
        + "                \"showTypeName\": \"CHARACTER_SELECT\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": null,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            },\n"
        + "            {\n"
        + "                \"id\": 18737,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"reportF3AF7663-498F-43E3-824F-E20A5BBD2961\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": null,\n"
        + "                \"prop\": \"operator_13\",\n"
        + "                \"label\": \"运营商\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"dataType\": null,\n"
        + "                \"decimaCarry\": null,\n"
        + "                \"unit\": null,\n"
        + "                \"isShowUnit\": null,\n"
        + "                \"showThousandth\": null,\n"
        + "                \"nickName\": null,\n"
        + "                \"showSubtotal\": false,\n"
        + "                \"showTypeName\": \"CHARACTER_SELECT\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": null,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            },\n"
        + "            {\n"
        + "                \"id\": 18460,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"report44B6F729-A045-495B-AA43-7A6A0513FEB8\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": \"sum\",\n"
        + "                \"prop\": \"toFloat64OrZero_toNullable__toString__approved_credit_amount_5______indexsum\",\n"
        + "                \"label\": \"成功授信金额\",\n"
        + "                \"reportField\": \"index\",\n"
        + "                \"dataType\": \"数值\",\n"
        + "                \"decimaCarry\": 0,\n"
        + "                \"unit\": 1,\n"
        + "                \"isShowUnit\": true,\n"
        + "                \"showThousandth\": false,\n"
        + "                \"nickName\": \"成功授信金额\",\n"
        + "                \"showSubtotal\": false,\n"
        + "                \"showTypeName\": \"DECIMAL\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": null,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            },\n"
        + "            {\n"
        + "                \"id\": 569,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"reportB224B8E8-9261-4535-8C18-F0281441D203\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": \"no\",\n"
        + "                \"prop\": \"compute_jjjnhhhc_93322835_indexno_1\",\n"
        + "                \"label\": \"sum金额\",\n"
        + "                \"reportField\": \"index\",\n"
        + "                \"dataType\": \"数值\",\n"
        + "                \"decimaCarry\": 0,\n"
        + "                \"unit\": 1,\n"
        + "                \"isShowUnit\": true,\n"
        + "                \"showThousandth\": false,\n"
        + "                \"nickName\": \"sum金额\",\n"
        + "                \"showSubtotal\": false,\n"
        + "                \"showTypeName\": \"DECIMAL\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": null,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            },\n"
        + "            {\n"
        + "                \"id\": 18461,\n"
        + "                \"enName\": null,\n"
        + "                \"uuid\": \"report3776B50A-E438-4E6C-948C-86A59A98F339\",\n"
        + "                \"isHide\": false,\n"
        + "                \"polymerization\": \"sum\",\n"
        + "                \"prop\": \"toFloat64OrZero_toNullable__toString__user_application_count_6______indexsum_2\",\n"
        + "                \"label\": \"用户申请次数\",\n"
        + "                \"reportField\": \"overlayIndex\",\n"
        + "                \"dataType\": null,\n"
        + "                \"decimaCarry\": null,\n"
        + "                \"unit\": null,\n"
        + "                \"isShowUnit\": true,\n"
        + "                \"showThousandth\": null,\n"
        + "                \"nickName\": \"用户申请次数\",\n"
        + "                \"showSubtotal\": false,\n"
        + "                \"showTypeName\": \"DECIMAL\",\n"
        + "                \"children\": null,\n"
        + "                \"father\": null,\n"
        + "                \"dateGroupType\": null,\n"
        + "                \"isLastShowColumn\": false,\n"
        + "                \"isRowTotal\": false,\n"
        + "                \"styleSelection\": null,\n"
        + "                \"columnConfig\": null,\n"
        + "                \"maxValue\": null,\n"
        + "                \"minValue\": null\n"
        + "            }\n"
        + "        ],\n"
        + "        \"data\": [\n"
        + "            {\n"
        + "                \"field0\": [\n"
        + "                    \"2021\",\n"
        + "                    \"2023\",\n"
        + "                    \"2021\"\n"
        + "                ]\n"
        + "            },\n"
        + "            {\n"
        + "                \"field1\": [\n"
        + "                    \"浙江省\",\n"
        + "                    \"浙江省\",\n"
        + "                    \"浙江省\"\n"
        + "                ]\n"
        + "            },\n"
        + "            {\n"
        + "                \"field2\": [\n"
        + "                    \"其他\",\n"
        + "                    \"其他\",\n"
        + "                    \"移动\"\n"
        + "                ]\n"
        + "            },\n"
        + "            {\n"
        + "                \"index0\": [\n"
        + "                    \"1671550061.0499997\",\n"
        + "                    \"1606098480.6000004\",\n"
        + "                    \"1172882751.7000003\"\n"
        + "                ]\n"
        + "            },\n"
        + "            {\n"
        + "                \"index1\": [\n"
        + "                    \"1671550061.0499997\",\n"
        + "                    \"1606098480.6000004\",\n"
        + "                    \"1172882751.7000003\"\n"
        + "                ]\n"
        + "            },\n"
        + "            {\n"
        + "                \"overlayIndex0\": [\n"
        + "                    \"680\",\n"
        + "                    \"1235\",\n"
        + "                    \"700\"\n"
        + "                ]\n"
        + "            }\n"
        + "        ]\n"
        + "    }\n"
        + "}";
    DashboardAnalysisByAIRequest param=JSONUtil.toBean(dashboards,DashboardAnalysisByAIRequest.class);
    List<DashboardAnalysisByAIParamRequest> result=aiServiceImpl.getDashboardInfoForAI(ListUtil.of(param));
    assertEquals(1, result.size());
    assertEquals("折线图", result.get(0).getCardName());
    assertEquals("折线图", result.get(0).getChartType());
    String csvStr=result.get(0).getData();
    CSVReader reader = new CSVReader(new StringReader(csvStr));
    String[] header=reader.readNext();
    assertEquals(6, header.length);
    assertEquals("申请时间(年)", header[0]);
    assertEquals("省份", header[1]);
    assertEquals("运营商", header[2]);
    assertEquals("成功授信金额", header[3]);
    assertEquals("sum金额", header[4]);
    assertEquals("用户申请次数", header[5]);
    String[] l1=reader.readNext();
    assertEquals(6, l1.length);
    assertEquals(6, header.length);
    assertEquals("2021", l1[0]);
    assertEquals("浙江省", l1[1]);
    assertEquals("其他", l1[2]);
    assertEquals("1671550061.0499997", l1[3]);
    assertEquals("1671550061.0499997", l1[4]);
    assertEquals("680", l1[5]);
    String[] l2=reader.readNext();
    assertEquals(6, l2.length);
    assertEquals("2023", l2[0]);
    assertEquals("浙江省", l2[1]);
    assertEquals("其他", l2[2]);
    assertEquals("1606098480.6000004", l2[3]);
    assertEquals("1606098480.6000004", l2[4]);
    assertEquals("1235", l2[5]);
    String[] l3=reader.readNext();
    assertEquals(6, l3.length);
    assertEquals("2021", l3[0]);
    assertEquals("浙江省", l3[1]);
    assertEquals("移动", l3[2]);
    assertEquals("1172882751.7000003", l3[3]);
    assertEquals("1172882751.7000003", l3[4]);
    assertEquals("700", l3[5]);
  }

  @Test
  public void tetGetReportInfoForAI_3() throws Exception{
    // 构建请求参数
    String dashboards="{\n"
        + "    \"cardInfo\": {\n"
        + "        \"cardName\": \"百分比堆积图\",\n"
        + "        \"chartType\": 11,\n"
        + "        \"reportName\": \"百分比堆积图\"\n"
        + "    },\n"
        + "    \"paramsInfo\": {\n"
        + "        \"isRealtime\": false,\n"
        + "        \"cardCode\": \"report-5d80ce87-c9eb-4c5e-8e43-6d4aecdcd24a\",\n"
        + "        \"cardType\": \"report\",\n"
        + "        \"dashboardId\": \"839\",\n"
        + "        \"queryConditions\": [],\n"
        + "        \"paramConditions\": [],\n"
        + "        \"fromFlag\": \"DASHBOARD\",\n"
        + "        \"pageSize\": 30,\n"
        + "        \"pageNum\": 1,\n"
        + "        \"rollAndDownRequest\": {},\n"
        + "        \"noCount\": true,\n"
        + "        \"orderColumns\": null\n"
        + "    },\n"
        + "    \"returnData\": {\n"
        + "        \"columnNameMaps\": [\n"
        + "    {\n"
        + "        \"id\": 6352,\n"
        + "        \"enName\": null,\n"
        + "        \"uuid\": \"report28EAD6D4-033A-427C-A137-EA92077A4CDB\",\n"
        + "        \"isHide\": false,\n"
        + "        \"polymerization\": null,\n"
        + "        \"prop\": \"apply_dt_format_3\",\n"
        + "        \"label\": \"申请日期(年)\",\n"
        + "        \"reportField\": \"field\",\n"
        + "        \"dataType\": null,\n"
        + "        \"decimaCarry\": null,\n"
        + "        \"unit\": null,\n"
        + "        \"isShowUnit\": null,\n"
        + "        \"showThousandth\": null,\n"
        + "        \"nickName\": null,\n"
        + "        \"showSubtotal\": false,\n"
        + "        \"showTypeName\": \"DATETIME\",\n"
        + "        \"children\": null,\n"
        + "        \"father\": null,\n"
        + "        \"dateGroupType\": 3,\n"
        + "        \"isLastShowColumn\": false,\n"
        + "        \"isRowTotal\": false,\n"
        + "        \"styleSelection\": null,\n"
        + "        \"columnConfig\": null,\n"
        + "        \"maxValue\": null,\n"
        + "        \"minValue\": null\n"
        + "    },\n"
        + "    {\n"
        + "        \"id\": 6351,\n"
        + "        \"enName\": null,\n"
        + "        \"uuid\": \"report271358C4-2D86-4DC1-8AD7-C7068BC62631\",\n"
        + "        \"isHide\": false,\n"
        + "        \"polymerization\": \"sum\",\n"
        + "        \"prop\": \"toFloat64OrZero_toNullable_all_batch_seq_no_td___indexsum\",\n"
        + "        \"label\": \"用户申请次数\",\n"
        + "        \"reportField\": \"index\",\n"
        + "        \"dataType\": \"数值\",\n"
        + "        \"decimaCarry\": 0,\n"
        + "        \"unit\": 1,\n"
        + "        \"isShowUnit\": true,\n"
        + "        \"showThousandth\": false,\n"
        + "        \"nickName\": \"用户申请次数\",\n"
        + "        \"showSubtotal\": false,\n"
        + "        \"showTypeName\": \"DECIMAL\",\n"
        + "        \"children\": null,\n"
        + "        \"father\": null,\n"
        + "        \"dateGroupType\": null,\n"
        + "        \"isLastShowColumn\": false,\n"
        + "        \"isRowTotal\": false,\n"
        + "        \"styleSelection\": null,\n"
        + "        \"columnConfig\": null,\n"
        + "        \"maxValue\": null,\n"
        + "        \"minValue\": null\n"
        + "    },\n"
        + "    {\n"
        + "        \"id\": 6365,\n"
        + "        \"enName\": null,\n"
        + "        \"uuid\": \"report7EF341E2-9A0E-4F64-8DD3-6353D13387C2\",\n"
        + "        \"isHide\": false,\n"
        + "        \"polymerization\": \"sum\",\n"
        + "        \"prop\": \"toFloat64OrZero_toNullable__toString__succe_crdt_lmt______indexsum_1\",\n"
        + "        \"label\": \"成功授信金额\",\n"
        + "        \"reportField\": \"index\",\n"
        + "        \"dataType\": \"数值\",\n"
        + "        \"decimaCarry\": 0,\n"
        + "        \"unit\": 1,\n"
        + "        \"isShowUnit\": true,\n"
        + "        \"showThousandth\": false,\n"
        + "        \"nickName\": \"成功授信金额\",\n"
        + "        \"showSubtotal\": false,\n"
        + "        \"showTypeName\": \"DECIMAL\",\n"
        + "        \"children\": null,\n"
        + "        \"father\": null,\n"
        + "        \"dateGroupType\": null,\n"
        + "        \"isLastShowColumn\": false,\n"
        + "        \"isRowTotal\": false,\n"
        + "        \"styleSelection\": null,\n"
        + "        \"columnConfig\": null,\n"
        + "        \"maxValue\": null,\n"
        + "        \"minValue\": null\n"
        + "    }\n"
        + "],\n"
        + "        \"data\": [\n"
        + "    {\n"
        + "        \"field0\": [\n"
        + "            \"2020\",\n"
        + "            \"2021\",\n"
        + "            \"2022\"\n"
        + "        ]\n"
        + "    },\n"
        + "    {\n"
        + "        \"index0\": [\n"
        + "            \"1\",\n"
        + "            \"58334154\",\n"
        + "            \"45565753\"\n"
        + "        ]\n"
        + "    },\n"
        + "    {\n"
        + "        \"index1\": [\n"
        + "            \"1\",\n"
        + "            \"2837984038394\",\n"
        + "            \"1621366235008\"\n"
        + "        ]\n"
        + "    },\n"
        + "    {\n"
        + "        \"percent0\": [\n"
        + "            \"0.50\",\n"
        + "            \"0.00\",\n"
        + "            \"0.00\"\n"
        + "        ]\n"
        + "    },\n"
        + "    {\n"
        + "        \"percent1\": [\n"
        + "            \"0.50\",\n"
        + "            \"1.00\",\n"
        + "            \"1.00\"\n"
        + "        ]\n"
        + "    }\n"
        + "]\n"
        + "    }\n"
        + "}";
    DashboardAnalysisByAIRequest param=JSONUtil.toBean(dashboards,DashboardAnalysisByAIRequest.class);
    List<DashboardAnalysisByAIParamRequest> result=aiServiceImpl.getDashboardInfoForAI(ListUtil.of(param));
    assertEquals(1, result.size());
    assertEquals("百分比堆积图", result.get(0).getCardName());
    assertEquals("百分比堆积图", result.get(0).getChartType());
    String csvStr=result.get(0).getData();
    CSVReader reader = new CSVReader(new StringReader(csvStr));
    String[] header=reader.readNext();
    assertEquals(5, header.length);
    assertEquals("申请日期(年)", header[0]);
    assertEquals("用户申请次数", header[1]);
    assertEquals("成功授信金额", header[2]);
    assertEquals("用户申请次数百分比", header[3]);
    assertEquals("成功授信金额百分比", header[4]);
    String[] l1=reader.readNext();
    assertEquals(5, l1.length);
    assertEquals("2020", l1[0]);
    assertEquals("1", l1[1]);
    assertEquals("1", l1[2]);
    assertEquals("0.50", l1[3]);
    assertEquals("0.50", l1[4]);
    String[] l2=reader.readNext();
    assertEquals(5, l2.length);
    assertEquals("2021", l2[0]);
    assertEquals("58334154", l2[1]);
    assertEquals("2837984038394", l2[2]);
    assertEquals("0.00", l2[3]);
    assertEquals("1.00", l2[4]);
    String[] l3=reader.readNext();
    assertEquals(5, l3.length);
    assertEquals("2022", l3[0]);
    assertEquals("45565753", l3[1]);
    assertEquals("1621366235008", l3[2]);
    assertEquals("0.00", l3[3]);
    assertEquals("1.00", l3[4]);
  }

  @Test
  public void testGetIndexTextInfoForAI_1() throws Exception{
    // 构建请求参数
    String dashboards="{\n"
        + "    \"cardInfo\": {\n"
        + "        \"cardName\": \"叠加指标\",\n"
        + "        \"chartType\": null,\n"
        + "        \"reportName\": null\n"
        + "    },\n"
        + "    \"paramsInfo\": {\n"
        + "        \"isRealtime\": false,\n"
        + "        \"cardCode\": \"indexText-3464ccf0-7b53-411c-8e29-807e548a7d1e\",\n"
        + "        \"cardType\": \"indexText\",\n"
        + "        \"dashboardId\": \"77\",\n"
        + "        \"queryConditions\": [\n"
        + "            {\n"
        + "                \"showTypeName\": \"DATETIME\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"fieldType\": \"DIMENSION\",\n"
        + "                \"id\": 6352,\n"
        + "                \"fieldName\": \"apply_dt\",\n"
        + "                \"dateType\": \"year\",\n"
        + "                \"filterType\": \"dateRange\",\n"
        + "                \"timeType\": \"relativeTime\",\n"
        + "                \"uuid\": \"filter-5a059f6e-dde5-49a1-82cc-36fac7df3795\",\n"
        + "                \"values\": [\n"
        + "                    \"2023\",\n"
        + "                    \"2025\"\n"
        + "                ],\n"
        + "                \"stringValue\": \"\",\n"
        + "                \"cardName\": \"申请日期\"\n"
        + "            },\n"
        + "            {\n"
        + "                \"showTypeName\": \"CHARACTER_SELECT\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"fieldType\": \"DIMENSION\",\n"
        + "                \"id\": 6360,\n"
        + "                \"fieldName\": \"mobile_prov_nm\",\n"
        + "                \"dateType\": \"\",\n"
        + "                \"filterType\": \"\",\n"
        + "                \"timeType\": \"\",\n"
        + "                \"uuid\": \"filter-eb3af059-870d-4473-adc6-cf08ead755b7\",\n"
        + "                \"values\": [\n"
        + "                    \"上海市\",\n"
        + "                    \"云南省\"\n"
        + "                ],\n"
        + "                \"stringValue\": \"\",\n"
        + "                \"cardName\": \"省份\",\n"
        + "                \"scopeFilterType\": \"in\"\n"
        + "            },\n"
        + "            {\n"
        + "                \"showTypeName\": \"CHARACTER_INPUT\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"fieldType\": \"DIMENSION\",\n"
        + "                \"id\": 6358,\n"
        + "                \"fieldName\": \"lsjr_cust_lvl\",\n"
        + "                \"dateType\": \"\",\n"
        + "                \"filterType\": \"\",\n"
        + "                \"timeType\": \"\",\n"
        + "                \"uuid\": \"filter-2dcf7848-230c-444b-9b7e-a6e5f2837657\",\n"
        + "                \"values\": [],\n"
        + "                \"stringValue\": \"A,B\",\n"
        + "                \"cardName\": \"用户等级\"\n"
        + "            },\n"
        + "            {\n"
        + "                \"showTypeName\": \"DECIMAL\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"fieldType\": \"ALL\",\n"
        + "                \"id\": 6351,\n"
        + "                \"fieldName\": \"toFloat64OrZero(toNullable(all_batch_seq_no_td))\",\n"
        + "                \"dateType\": \"\",\n"
        + "                \"filterType\": \"\",\n"
        + "                \"timeType\": \"\",\n"
        + "                \"uuid\": \"filter-1a862052-7d24-43a3-ba22-3108ba357165\",\n"
        + "                \"values\": [\n"
        + "                    0,\n"
        + "                    8\n"
        + "                ],\n"
        + "                \"stringValue\": \"\",\n"
        + "                \"cardName\": \"用户申请次数\"\n"
        + "            }\n"
        + "        ],\n"
        + "        \"paramConditions\": [],\n"
        + "        \"fromFlag\": \"DASHBOARD\"\n"
        + "    },\n"
        + "    \"returnData\": {\n"
        + "        \"data\": [\n"
        + "            {\n"
        + "                \"enName\": \"toFloat64OrZero_toNullable_all_batch_seq_no_td____index\",\n"
        + "                \"name\": \"用户申请次数\",\n"
        + "                \"id\": 6351,\n"
        + "                \"reportField\": \"index\",\n"
        + "                \"value\": \"11597\",\n"
        + "                \"style\": \"normal\"\n"
        + "            },\n"
        + "            {\n"
        + "                \"enName\": \"toFloat64OrZero_toNullable__toString__succe_crdt_lmt______indexsum_1\",\n"
        + "                \"name\": \"叠加指标\",\n"
        + "                \"id\": 6365,\n"
        + "                \"reportField\": \"overlayIndex\",\n"
        + "                \"value\": \"1,779,554.6K\",\n"
        + "                \"style\": \"normal\"\n"
        + "            }\n"
        + "        ]\n"
        + "    }\n"
        + "}";
    DashboardAnalysisByAIRequest param=JSONUtil.toBean(dashboards,DashboardAnalysisByAIRequest.class);
    List<DashboardAnalysisByAIParamRequest> result=aiServiceImpl.getDashboardInfoForAI(ListUtil.of(param));
    assertEquals(1, result.size());
    assertEquals("叠加指标", result.get(0).getCardName());
    assertEquals("指标文本", result.get(0).getChartType());
    assertNull(result.get(0).getData());
    assertEquals("用户申请次数 为 11597,叠加指标 为 1,779,554.6K", result.get(0).getIndexText());
    assertEquals("当前 申请日期 为 ,当前 省份 为 ,当前 用户等级 为 ,当前 用户申请次数 为 ", result.get(0).getFilter());
  }

  @Test
  public void testGetIndexTextInfoForAI_2() throws Exception{
    // 构建请求参数
    String dashboards="{\n"
        + "    \"cardInfo\": {\n"
        + "        \"cardName\": \"未命名卡片 2024-09-04 17:06:06\",\n"
        + "        \"chartType\": null,\n"
        + "        \"reportName\": null\n"
        + "    },\n"
        + "    \"paramsInfo\": {\n"
        + "        \"isRealtime\": false,\n"
        + "        \"cardCode\": \"indexText-6b4645cf-9603-44a1-879d-affaf302de64\",\n"
        + "        \"cardType\": \"indexText\",\n"
        + "        \"dashboardId\": \"77\",\n"
        + "        \"queryConditions\": [\n"
        + "            {\n"
        + "                \"showTypeName\": \"DATETIME\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"fieldType\": \"DIMENSION\",\n"
        + "                \"id\": 6352,\n"
        + "                \"fieldName\": \"apply_dt\",\n"
        + "                \"dateType\": \"year\",\n"
        + "                \"filterType\": \"dateRange\",\n"
        + "                \"timeType\": \"relativeTime\",\n"
        + "                \"uuid\": \"filter-5a059f6e-dde5-49a1-82cc-36fac7df3795\",\n"
        + "                \"values\": [\n"
        + "                    \"2023\",\n"
        + "                    \"2025\"\n"
        + "                ],\n"
        + "                \"stringValue\": \"\",\n"
        + "                \"cardName\": \"申请日期\"\n"
        + "            },\n"
        + "            {\n"
        + "                \"showTypeName\": \"CHARACTER_SELECT\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"fieldType\": \"DIMENSION\",\n"
        + "                \"id\": 6360,\n"
        + "                \"fieldName\": \"mobile_prov_nm\",\n"
        + "                \"dateType\": \"\",\n"
        + "                \"filterType\": \"\",\n"
        + "                \"timeType\": \"\",\n"
        + "                \"uuid\": \"filter-eb3af059-870d-4473-adc6-cf08ead755b7\",\n"
        + "                \"values\": [\n"
        + "                    \"上海市\",\n"
        + "                    \"云南省\"\n"
        + "                ],\n"
        + "                \"stringValue\": \"\",\n"
        + "                \"cardName\": \"省份\",\n"
        + "                \"scopeFilterType\": \"in\"\n"
        + "            },\n"
        + "            {\n"
        + "                \"showTypeName\": \"CHARACTER_INPUT\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"fieldType\": \"DIMENSION\",\n"
        + "                \"id\": 6358,\n"
        + "                \"fieldName\": \"lsjr_cust_lvl\",\n"
        + "                \"dateType\": \"\",\n"
        + "                \"filterType\": \"\",\n"
        + "                \"timeType\": \"\",\n"
        + "                \"uuid\": \"filter-2dcf7848-230c-444b-9b7e-a6e5f2837657\",\n"
        + "                \"values\": [],\n"
        + "                \"stringValue\": \"A,B\",\n"
        + "                \"cardName\": \"用户等级\"\n"
        + "            },\n"
        + "            {\n"
        + "                \"showTypeName\": \"DECIMAL\",\n"
        + "                \"reportField\": \"field\",\n"
        + "                \"fieldType\": \"ALL\",\n"
        + "                \"id\": 6351,\n"
        + "                \"fieldName\": \"toFloat64OrZero(toNullable(all_batch_seq_no_td))\",\n"
        + "                \"dateType\": \"\",\n"
        + "                \"filterType\": \"\",\n"
        + "                \"timeType\": \"\",\n"
        + "                \"uuid\": \"filter-1a862052-7d24-43a3-ba22-3108ba357165\",\n"
        + "                \"values\": [\n"
        + "                    0,\n"
        + "                    8\n"
        + "                ],\n"
        + "                \"stringValue\": \"\",\n"
        + "                \"cardName\": \"用户申请次数\"\n"
        + "            }\n"
        + "        ],\n"
        + "        \"paramConditions\": [],\n"
        + "        \"fromFlag\": \"DASHBOARD\"\n"
        + "    },\n"
        + "    \"returnData\": {\n"
        + "        \"data\": [\n"
        + "            {\n"
        + "                \"enName\": \"toFloat64OrZero_toNullable__toString__succe_crdt_lmt_______index\",\n"
        + "                \"name\": \"成功授信金额\",\n"
        + "                \"id\": 6365,\n"
        + "                \"reportField\": \"index\",\n"
        + "                \"value\": \"1779554611\",\n"
        + "                \"style\": \"normal\"\n"
        + "            },\n"
        + "            {\n"
        + "                \"enName\": \"toFloat64OrZero_toNullable__toString__succe_crdt_lmt______indexsum13_1\",\n"
        + "                \"name\": \"成功授信金额\",\n"
        + "                \"id\": 6365,\n"
        + "                \"reportField\": \"overlayIndex\",\n"
        + "                \"value\": \"-0.****************\",\n"
        + "                \"style\": \"normal\"\n"
        + "            }\n"
        + "        ]\n"
        + "    }\n"
        + "}";
    DashboardAnalysisByAIRequest param=JSONUtil.toBean(dashboards,DashboardAnalysisByAIRequest.class);
    List<DashboardAnalysisByAIParamRequest> result=aiServiceImpl.getDashboardInfoForAI(ListUtil.of(param));
    assertEquals(1, result.size());
    assertEquals("未命名卡片 2024-09-04 17:06:06", result.get(0).getCardName());
    assertEquals("指标文本", result.get(0).getChartType());
    assertNull(result.get(0).getData());
    assertEquals("成功授信金额 为 1779554611,成功授信金额 为 -0.****************", result.get(0).getIndexText());
    assertEquals("当前 申请日期 为 ,当前 省份 为 ,当前 用户等级 为 ,当前 用户申请次数 为 ", result.get(0).getFilter());
  }

  @Test
  public void testGetEmbedTextIndexInfoForAI_1() throws Exception{
    // 构建请求参数
    String dashboards="{\n"
        + "    \"cardInfo\": {},\n"
        + "    \"paramsInfo\": {\n"
        + "        \"dashboardId\": \"78\",\n"
        + "        \"isRealtime\": false,\n"
        + "        \"cardCode\": \"2452\",\n"
        + "        \"cardType\": \"embedText\",\n"
        + "        \"queryConditions\": [],\n"
        + "        \"paramConditions\": [],\n"
        + "        \"fromFlag\": \"DASHBOARD\"\n"
        + "    },\n"
        + "    \"returnData\": {\n"
        + "        \"text\": \"<p>106381179</p><p>--</p><p>--</p><p>-91.32%</p><p>-0.9132034550546538</p><p>0</p><p><br></p>\"\n"
        + "    }\n"
        + "}";
    DashboardAnalysisByAIRequest param=JSONUtil.toBean(dashboards,DashboardAnalysisByAIRequest.class);
    List<DashboardAnalysisByAIParamRequest> result=aiServiceImpl.getDashboardInfoForAI(ListUtil.of(param));
    assertEquals(1, result.size());
    assertNull(result.get(0).getCardName());
    assertEquals("文本引用指标文本", result.get(0).getChartType());
    assertNull(result.get(0).getData());
    assertEquals("106381179 -- -- -91.32% -0.9132034550546538 0", result.get(0).getIndexText());
  }

}
