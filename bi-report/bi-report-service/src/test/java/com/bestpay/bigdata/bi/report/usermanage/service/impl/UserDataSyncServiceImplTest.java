package com.bestpay.bigdata.bi.report.usermanage.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bestpay.bigdata.bi.common.api.RedisService;
import com.bestpay.bigdata.bi.common.bean.aiapi.ApiPlusUserInfo;
import com.bestpay.bigdata.bi.common.bean.aiapi.NewUserInfoRequest;
import com.bestpay.bigdata.bi.common.common.AIPlusPageable;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.dao.usergroupmanage.UserManageUserInfoDO;
import com.bestpay.bigdata.bi.database.mapper.usergroupmanage.UserManageUserInfoMapper;
import com.bestpay.bigdata.bi.report.usermanage.service.AiPlusUserManageService;
import com.bestpay.bigdata.bi.report.usermanage.service.DbUserManageService;
import com.bestpay.bigdata.bi.report.util.SyncDataUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UserDataSyncServiceImplTest {

  private static final String SYNC_LOCK_KEY = "aiplus:user:sync:lock";

  private static final Long SYNC_LOCK_TTL = 10 * 60 * 1000L;

  @InjectMocks
  private UserDataSyncServiceImpl userDataSyncService;

  @Mock
  private RedisService redisService;

  @Mock
  private UserManageUserInfoMapper userGroupUserInfoMapper;

  @Mock
  private DbUserManageService dbUserManageService;

  @Mock
  private ApolloRefreshConfig apolloRefreshConfig;

  @Mock
  private AiPlusUserManageService aiPlusUserManageService;

  @Spy
  private List<UserManageUserInfoDO> userInfoDos = new ArrayList<>();

  @Before
  public void setUp() {
    // 初始化测试数据
    UserManageUserInfoDO userInfoDO = new UserManageUserInfoDO();
    userInfoDO.setOneId("user1");
    userInfoDos.add(userInfoDO);

    doNothing().when(dbUserManageService).batchInsertAndUpdateUserInfo(anyList(), anyList());
  }

  /**
   * 测试点：分布式锁获取失败的情况
   */
  @Test
  public void testLockAcquisitionFailed() {
    // 模拟锁获取失败
    when(redisService.lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL))).thenReturn(false);

    // 执行测试
    userDataSyncService.userDataSync();

    try {
      Thread.sleep(1000L);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    // 验证锁未获取，方法提前返回
    verify(redisService, times(1)).lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL));
    verify(redisService, never()).unlock(eq(SYNC_LOCK_KEY));
    verify(aiPlusUserManageService, never()).getAiPlusUserInfo(any(NewUserInfoRequest.class));
  }

  /**
   * 测试点：首次查询智加接口失败的情况
   */
  @Test
  public void testFirstQueryFailed() {
    // 模拟锁获取成功
    when(redisService.lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL))).thenReturn(true);

    // 模拟用户信息
    UserInfo userInfo = new UserInfo();
    userInfo.setAccount("testAccount");

    // 使用 try-with-resources 语句管理 mockStatic
    try (MockedStatic<UserContextUtil> mockedStatic = mockStatic(UserContextUtil.class)) {
      // 当调用 UserContextUtil.getUserInfo() 时返回模拟对象
      mockedStatic.when(UserContextUtil::getUserInfo).thenReturn(userInfo);

      // 模拟首次查询失败
      when(aiPlusUserManageService.getAiPlusUserInfo(any(NewUserInfoRequest.class))).thenThrow(new RuntimeException());

      // 执行测试
      userDataSyncService.userDataSync();
    }

    try {
      Thread.sleep(1000L);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    // 验证操作
    verify(redisService, times(1)).lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL));
    verify(redisService, times(1)).unlock(eq(SYNC_LOCK_KEY));
    verify(aiPlusUserManageService, times(1)).getAiPlusUserInfo(any(NewUserInfoRequest.class));
  }

  /**
   * 测试点：正常同步流程,模拟两页数据同步
   */
  @Test
  public void testNormalSyncProcess() {
    // 模拟锁获取成功
    when(redisService.lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL))).thenReturn(true);

    // 模拟用户信息
    UserInfo userInfo = new UserInfo();
    userInfo.setAccount("testAccount");
    try (MockedStatic<UserContextUtil> mockedStatic = mockStatic(UserContextUtil.class)) {
      // 当调用 UserContextUtil.getUserInfo() 时返回模拟对象
      mockedStatic.when(UserContextUtil::getUserInfo).thenReturn(userInfo);
    }

    // 模拟API返回数据
    AIPlusPageable<ApiPlusUserInfo> pageable = new AIPlusPageable<>();
    pageable.setCurrent(1);
    pageable.setSize(2);
    pageable.setPage(2);
    pageable.setTotal(4);
    pageable.setRecords(SyncDataUtil.createTestNewUserInfos());

    when(aiPlusUserManageService.getAiPlusUserInfo(any(NewUserInfoRequest.class))).thenReturn(pageable);

    AIPlusPageable<ApiPlusUserInfo> pageable1 = new AIPlusPageable<>();
    pageable1.setCurrent(2);
    pageable1.setSize(2);
    pageable1.setPage(2);
    pageable1.setTotal(4);
    pageable1.setRecords(SyncDataUtil.createTestNewUserInfos());
    when(aiPlusUserManageService.getAiPlusUserInfo(any(NewUserInfoRequest.class))).thenReturn(pageable1);

    // 模拟批量大小
    when(apolloRefreshConfig.getUserDataSyncBatchSize()).thenReturn(100);

    // 执行测试
    userDataSyncService.userDataSync();

    try {
      Thread.sleep(1000L);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    // 验证操作
    verify(redisService, times(1)).lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL));
    verify(redisService, times(1)).unlock(eq(SYNC_LOCK_KEY));
    verify(aiPlusUserManageService, times(2)).getAiPlusUserInfo(any(NewUserInfoRequest.class));
    verify(dbUserManageService, times(2)).batchInsertAndUpdateUserInfo(anyList(), anyList());
  }

  /**
   * 测试点：数据处理异常的情况
   */
  @Test
  public void testDataProcessingException() {
    // 模拟锁获取成功
    when(redisService.lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL))).thenReturn(true);

    // 模拟用户信息
    UserInfo userInfo = new UserInfo();
    userInfo.setAccount("testAccount");

    // 模拟数据库查询
    when(userGroupUserInfoMapper.query(any(UserManageUserInfoDO.class))).thenReturn(userInfoDos);

    // 模拟API返回数据
    AIPlusPageable<ApiPlusUserInfo> pageable = new AIPlusPageable<>();
    pageable.setCurrent(1);
    pageable.setSize(2);
    pageable.setPage(2);
    pageable.setTotal(4);
    pageable.setRecords(SyncDataUtil.createTestNewUserInfos());

    when(aiPlusUserManageService.getAiPlusUserInfo(any(NewUserInfoRequest.class))).thenReturn(pageable);

    AIPlusPageable<ApiPlusUserInfo> pageable1 = new AIPlusPageable<>();
    pageable1.setCurrent(2);
    pageable1.setSize(2);
    pageable1.setPage(2);
    pageable1.setTotal(4);
    pageable1.setRecords(SyncDataUtil.createTestNewUserInfos());
    when(aiPlusUserManageService.getAiPlusUserInfo(any(NewUserInfoRequest.class))).thenReturn(pageable1);

    // 模拟更新操作抛出异常
    doThrow(new RuntimeException()).when(userGroupUserInfoMapper).batchUpdateUserGroupUserInfoByOneId(anyList());

    // 模拟批量大小
    when(apolloRefreshConfig.getUserDataSyncBatchSize()).thenReturn(100);

    // 执行测试
    userDataSyncService.userDataSync();

    try {
      Thread.sleep(1000L);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    // 验证操作
    verify(redisService, times(1)).lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL));
    verify(redisService, times(1)).unlock(eq(SYNC_LOCK_KEY));
    verify(aiPlusUserManageService, times(2)).getAiPlusUserInfo(any(NewUserInfoRequest.class));
    verify(dbUserManageService, times(2)).batchInsertAndUpdateUserInfo(anyList(), anyList());
    verify(userGroupUserInfoMapper, never()).batchInsertUserGroupUserInfo(anyList());
  }

  /**
   * 测试点：分页查询和处理
   */
  @Test
  public void testPagingQueryAndProcessing() {
    // 模拟锁获取成功
    when(redisService.lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL))).thenReturn(true);

    // 模拟用户信息
    UserInfo userInfo = new UserInfo();
    userInfo.setAccount("testAccount");
    try (MockedStatic<UserContextUtil> mockedStatic = mockStatic(UserContextUtil.class)) {
      // 当调用 UserContextUtil.getUserInfo() 时返回模拟对象
      mockedStatic.when(UserContextUtil::getUserInfo).thenReturn(userInfo);
    }

    // 模拟数据库查询
    when(userGroupUserInfoMapper.query(any(UserManageUserInfoDO.class))).thenReturn(userInfoDos);

    // 模拟API返回数据
    AIPlusPageable<ApiPlusUserInfo> pageable = new AIPlusPageable<>();
    pageable.setCurrent(1);
    pageable.setSize(2);
    pageable.setPage(2);
    pageable.setTotal(4);
    pageable.setRecords(SyncDataUtil.createTestNewUserInfos());

    AIPlusPageable<ApiPlusUserInfo> pageable1 = new AIPlusPageable<>();
    pageable1.setCurrent(2);
    pageable1.setSize(2);
    pageable1.setPage(2);
    pageable1.setTotal(4);
    pageable1.setRecords(SyncDataUtil.createTestNewUserInfos());

    when(aiPlusUserManageService.getAiPlusUserInfo(any(NewUserInfoRequest.class))).thenReturn(pageable, pageable1);

    // 模拟批量大小
    when(apolloRefreshConfig.getUserDataSyncBatchSize()).thenReturn(100);

    // 执行测试
    userDataSyncService.userDataSync();

    try {
      Thread.sleep(1000L);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    // 验证操作
    verify(redisService, times(1)).lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL));
    verify(redisService, times(1)).unlock(eq(SYNC_LOCK_KEY));
    verify(aiPlusUserManageService, times(2)).getAiPlusUserInfo(any(NewUserInfoRequest.class));
    verify(dbUserManageService, times(2)).batchInsertAndUpdateUserInfo(anyList(), anyList());
  }

  /**
   * 测试点：锁释放验证
   */
  @Test
  public void testLockRelease() {
    // 模拟锁获取成功
    when(redisService.lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL))).thenReturn(true);

    // 模拟用户信息
    UserInfo userInfo = new UserInfo();
    userInfo.setAccount("testAccount");
    try (MockedStatic<UserContextUtil> mockedStatic = mockStatic(UserContextUtil.class)) {
      // 当调用 UserContextUtil.getUserInfo() 时返回模拟对象
      mockedStatic.when(UserContextUtil::getUserInfo).thenReturn(userInfo);
    }
    // 模拟数据库查询
    when(userGroupUserInfoMapper.query(any(UserManageUserInfoDO.class))).thenReturn(userInfoDos);

    // 模拟API返回空数据
    AIPlusPageable<ApiPlusUserInfo> pageable = mock(AIPlusPageable.class);
    when(pageable.getPage()).thenReturn(1);
    when(pageable.getRecords()).thenReturn(Collections.emptyList());

    when(aiPlusUserManageService.getAiPlusUserInfo(any(NewUserInfoRequest.class))).thenReturn(pageable);

    // 模拟批量大小
    when(apolloRefreshConfig.getUserDataSyncBatchSize()).thenReturn(100);

    // 执行测试
    userDataSyncService.userDataSync();

    try {
      Thread.sleep(1000L);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    // 验证锁释放
    verify(redisService, times(1)).lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL));
    verify(redisService, times(1)).unlock(eq(SYNC_LOCK_KEY));
  }

  /**
   * 测试点：分页查询和处理,一共分页3页，第二页异常能够正常执行
   */
  @Test
  public void testPagingQueryAndProcessing1() {
    // 模拟锁获取成功
    when(redisService.lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL))).thenReturn(true);

    // 模拟用户信息
    UserInfo userInfo = new UserInfo();
    userInfo.setAccount("testAccount");
    try (MockedStatic<UserContextUtil> mockedStatic = mockStatic(UserContextUtil.class)) {
      // 当调用 UserContextUtil.getUserInfo() 时返回模拟对象
      mockedStatic.when(UserContextUtil::getUserInfo).thenReturn(userInfo);
    }

    // 模拟数据库查询
    when(userGroupUserInfoMapper.query(any(UserManageUserInfoDO.class))).thenReturn(userInfoDos);

    // 模拟API返回数据
    // 第1次和第3次调用返回的分页数据
    AIPlusPageable<ApiPlusUserInfo> pageable1 = createPageable(1, 3, 3, 6);
    AIPlusPageable<ApiPlusUserInfo> pageable3 = createPageable(3, 3, 3, 6);

    // 2. 使用计数器控制调用次数，第二次调用抛出异常
    AtomicInteger callCount = new AtomicInteger(0); // 用于记录调用次数

    when(aiPlusUserManageService.getAiPlusUserInfo(any(NewUserInfoRequest.class))).thenAnswer(invocation -> {
      int count = callCount.incrementAndGet(); // 每次调用自增
      if (count == 2) {
        // 第二次调用抛出异常
        throw new RuntimeException("模拟API调用失败");
      } else if (count == 1) {
        // 第一次调用返回第1页数据
        return pageable1;
      } else {
        // 第三次及以后调用返回第3页数据
        return pageable3;
      }
    });

    // 模拟批量大小
    when(apolloRefreshConfig.getUserDataSyncBatchSize()).thenReturn(100);

    // 执行测试
    userDataSyncService.userDataSync();

    try {
      Thread.sleep(1000L);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }

    // 验证操作
    verify(redisService, times(1)).lock(eq(SYNC_LOCK_KEY), eq(SYNC_LOCK_TTL));
    verify(redisService, times(1)).unlock(eq(SYNC_LOCK_KEY));
    verify(aiPlusUserManageService, times(3)).getAiPlusUserInfo(any(NewUserInfoRequest.class));
    verify(dbUserManageService, times(2)).batchInsertAndUpdateUserInfo(anyList(), anyList());
  }

  /**
   * 创建测试用的分页数据
   */
  private AIPlusPageable<ApiPlusUserInfo> createPageable(int current, int size, int page, int total) {
    AIPlusPageable<ApiPlusUserInfo> pageable = new AIPlusPageable<>();
    // 当前页
    pageable.setCurrent(current);
    // 每页条数
    pageable.setSize(size);
    // 总页数
    pageable.setPage(page);
    // 总条数
    pageable.setTotal(total);
    // 测试数据
    pageable.setRecords(SyncDataUtil.createTestNewUserInfos());
    return pageable;
  }
}