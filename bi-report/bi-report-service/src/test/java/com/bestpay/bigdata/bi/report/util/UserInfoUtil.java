package com.bestpay.bigdata.bi.report.util;

import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.database.dao.usergroupmanage.UserGroupInfoDO;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupInfoRequest;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupUpdateRequest;

/**
 * <AUTHOR>
 * @ClassName UserInfoUtil
 * @description mock用户数据
 * @date 2025/7/7
 */
public class UserInfoUtil {

  /**
   * 创建测试用的UserGroupInfoRequest
   */
  public static UserGroupInfoRequest createTestRequest() {
    UserGroupInfoRequest request = new UserGroupInfoRequest();
    request.setOrgCode("ORG001");
    request.setOrgName("测试组织");
    request.setUserGroupName("测试用户组");
    request.setUserGroupParentCode("PARENT_CODE");
    request.setLevel(1);
    return request;
  }

  /**
   * 创建管理员用户
   */
  public static UserInfo createAdminUser() {
    UserInfo userInfo = new UserInfo();
    userInfo.setAccount("admin");
    userInfo.setIsManager(true);
    return userInfo;
  }

  /**
   * 创建非管理员用户
   */
  public static UserInfo createNonAdminUser() {
    UserInfo userInfo = new UserInfo();
    userInfo.setAccount("user");
    userInfo.setIsManager(false);
    return userInfo;
  }

  // 辅助方法：创建测试用的UserGroupUpdateRequest
  public static UserGroupUpdateRequest createUpdateRequest(String groupCode, String parentCode) {
    UserGroupUpdateRequest request = new UserGroupUpdateRequest();
    request.setUserGroupCode(groupCode);
    request.setUserGroupParentCode(parentCode);
    request.setUserGroupName("新用户组名称");
    request.setOrgCode("ORG_001");
    request.setOrgName("新组织名称");
    return request;
  }

  // 辅助方法：创建测试用的UserGroupInfoDO
  public static UserGroupInfoDO createUserGroupDO(String code, String name, String parentCode, Integer level) {
    UserGroupInfoDO group = new UserGroupInfoDO();
    group.setUserGroupCode(code);
    group.setUserGroupName(name);
    group.setUserGroupParentCode(parentCode);
    group.setLevel(level);
    return group;
  }

  // 辅助方法：创建测试用的UserInfo
  public static UserInfo createUserInfo(String account) {
    UserInfo user = new UserInfo();
    user.setAccount(account);
    return user;
  }
}
