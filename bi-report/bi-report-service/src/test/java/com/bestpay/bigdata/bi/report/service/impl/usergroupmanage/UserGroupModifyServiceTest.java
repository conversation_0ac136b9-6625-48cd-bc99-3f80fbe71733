package com.bestpay.bigdata.bi.report.service.impl.usergroupmanage;

import static com.bestpay.bigdata.bi.report.util.UserInfoUtil.createUserInfo;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.util.UserCodeGenerator;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.dao.usergroupmanage.UserGroupInfoDO;
import com.bestpay.bigdata.bi.database.mapper.usergroupmanage.UserGroupInfoMapper;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupInfoRequest;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupUpdateRequest;
import com.bestpay.bigdata.bi.report.usermanage.service.impl.UserGroupManageServiceImpl;
import com.bestpay.bigdata.bi.report.util.UserInfoUtil;
import java.util.Arrays;
import java.util.Collections;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;


/**
 * <AUTHOR>
 * @ClassName UserGroupModifyServiceTest
 * @description 测试增删改方法
 * @date 2025/7/7
 */
@RunWith(MockitoJUnitRunner.class)
public class UserGroupModifyServiceTest {

  @InjectMocks
  private UserGroupManageServiceImpl userGroupInfoService;

  @Mock
  private UserGroupInfoMapper userGroupInfoMapper;

  @Mock
  private UserCodeGenerator userCodeGenerator;

  @Before
  public void setUp() {
    // 重置静态mock
    resetStaticMocks();
  }

  /**
   * 重置静态mock
   */
  private void resetStaticMocks() {
    // 清除之前的静态mock，避免测试间干扰
    try (MockedStatic<UserContextUtil> mocked = mockStatic(UserContextUtil.class)) {
      mocked.when(UserContextUtil::getUserInfo).thenReturn(null);
    }
  }

  /**
   * 测试点：管理员用户正常新增用户组
   */
  @Test
  public void testAddUserGroupInfo_Admin() {
    // 准备测试数据
    UserGroupInfoRequest request = UserInfoUtil.createTestRequest();
    UserInfo adminUser = UserInfoUtil.createAdminUser();

    // 模拟静态方法返回管理员用户
    try (MockedStatic<UserContextUtil> mocked = mockStatic(UserContextUtil.class)) {
      mocked.when(UserContextUtil::getUserInfo).thenReturn(adminUser);

      // 模拟用户组名称不存在
      when(userGroupInfoMapper.selectByOrgCodeAndName(request.getOrgCode(), request.getUserGroupName())).thenReturn(
          Collections.emptyList());

      // 模拟生成用户组code
      when(userCodeGenerator.generateUserCode(anyString())).thenReturn("UG_202307070001");

      // 执行测试
      userGroupInfoService.addUserGroupInfo(request);

      // 验证数据库操作
      verify(userGroupInfoMapper, times(1)).insertUserGroupInfo(any(UserGroupInfoDO.class));
    }
  }

  /**
   * 测试点：非管理员用户新增用户组（权限校验）
   */
  @Test
  public void testAddUserGroupInfo_NonAdmin() {
    // 准备测试数据
    UserGroupInfoRequest request = UserInfoUtil.createTestRequest();
    UserInfo nonAdminUser = UserInfoUtil.createNonAdminUser();

    // 模拟静态方法返回非管理员用户
    try (MockedStatic<UserContextUtil> mocked = mockStatic(UserContextUtil.class)) {
      mocked.when(UserContextUtil::getUserInfo).thenReturn(nonAdminUser);

      try {
        // 执行测试
        userGroupInfoService.addUserGroupInfo(request);
        // 若没有抛出异常，断言失败
        fail("预期抛出Exception，但实际未抛出");
      } catch (Exception e) {
        // 明确校验异常代码和消息
        assertTrue("异常消息应包含用户账号", e.getMessage().contains(nonAdminUser.getAccount()));
        assertTrue("异常消息应包含权限提示", e.getMessage().contains("无权限"));
      }

      // 验证数据库操作
      verify(userGroupInfoMapper, never()).insertUserGroupInfo(any(UserGroupInfoDO.class));
    }
  }

  /**
   * 测试点：新增已存在的用户组名称（唯一性校验）
   */
  @Test
  public void testAddUserGroupInfo_DuplicateName() {
    // 准备测试数据
    UserGroupInfoRequest request = UserInfoUtil.createTestRequest();
    UserInfo adminUser = UserInfoUtil.createAdminUser();

    // 模拟静态方法返回管理员用户
    try (MockedStatic<UserContextUtil> mocked = mockStatic(UserContextUtil.class)) {
      mocked.when(UserContextUtil::getUserInfo).thenReturn(adminUser);

      // 模拟用户组名称已存在
      UserGroupInfoDO existingGroup = new UserGroupInfoDO();
      existingGroup.setOrgCode(request.getOrgCode());
      existingGroup.setUserGroupName(request.getUserGroupName());
      when(userGroupInfoMapper.selectByOrgCodeAndName(request.getOrgCode(), request.getUserGroupName())).thenReturn(
          Collections.singletonList(existingGroup));

      // 执行测试，预期抛出异常
      try {
        // 执行测试
        userGroupInfoService.addUserGroupInfo(request);
        // 若没有抛出异常，断言失败
        fail("预期抛出Exception，但实际未抛出");
      } catch (Exception e) {
        // 明确校验异常代码和消息
        assertTrue("异常消息应包含用户组名称", e.getMessage().contains(request.getUserGroupName()));
        assertTrue("异常消息应包含错误提示", e.getMessage().contains("已经存在"));
      }

      // 验证数据库操作
      verify(userGroupInfoMapper, never()).insertUserGroupInfo(any(UserGroupInfoDO.class));
    }
  }

  /**
   * 测试点：处理用户组层级（父级编码为空时层级为1）
   */
  @Test
  public void testAddUserGroupInfo_Level_ParentNull() {
    // 准备测试数据 - 父级编码为空
    UserGroupInfoRequest request = UserInfoUtil.createTestRequest();
    request.setUserGroupParentCode(null);
    request.setLevel(null);

    UserInfo adminUser = UserInfoUtil.createAdminUser();

    // 模拟静态方法返回管理员用户
    try (MockedStatic<UserContextUtil> mocked = mockStatic(UserContextUtil.class)) {
      mocked.when(UserContextUtil::getUserInfo).thenReturn(adminUser);

      // 模拟用户组名称不存在
      when(userGroupInfoMapper.selectByOrgCodeAndName(request.getOrgCode(), request.getUserGroupName())).thenReturn(
          Collections.emptyList());

      // 模拟生成用户组code
      when(userCodeGenerator.generateUserCode(anyString())).thenReturn("UG_202307070001");

      ArgumentCaptor<UserGroupInfoDO> captor = ArgumentCaptor.forClass(UserGroupInfoDO.class);

      // 执行测试
      userGroupInfoService.addUserGroupInfo(request);

      // 验证层级设置为1
      // 验证层级设置为3（父级层级2+1）
      verify(userGroupInfoMapper, times(1)).insertUserGroupInfo(captor.capture());
      UserGroupInfoDO savedGroup = captor.getValue();

      // 明确校验层级值
      assertEquals("层级计算错误", 1, savedGroup.getLevel().intValue());

      // 额外校验其他关键字段
      assertEquals("用户组编码未正确设置", "UG_202307070001", savedGroup.getUserGroupCode());
      assertEquals("操作人未正确设置", adminUser.getAccount(), savedGroup.getCreatedBy());
    }
  }

  /**
   * 测试点：处理用户组层级（父级编码存在时层级正确计算）
   */
  @Test
  public void testAddUserGroupInfo_Level_ParentExists() {
    // 准备测试数据 - 父级编码存在，父级层级为2
    UserGroupInfoRequest request = UserInfoUtil.createTestRequest();
    request.setUserGroupParentCode("PARENT_GROUP_CODE");
    request.setLevel(2);

    UserInfo adminUser = UserInfoUtil.createAdminUser();

    // 模拟静态方法返回管理员用户
    try (MockedStatic<UserContextUtil> mocked = mockStatic(UserContextUtil.class)) {
      mocked.when(UserContextUtil::getUserInfo).thenReturn(adminUser);

      // 模拟用户组名称不存在
      when(userGroupInfoMapper.selectByOrgCodeAndName(request.getOrgCode(), request.getUserGroupName())).thenReturn(
          Collections.emptyList());

      // 模拟生成用户组code
      when(userCodeGenerator.generateUserCode(anyString())).thenReturn("UG_202307070001");

      ArgumentCaptor<UserGroupInfoDO> captor = ArgumentCaptor.forClass(UserGroupInfoDO.class);

      // 执行测试
      userGroupInfoService.addUserGroupInfo(request);

      // 验证层级设置为3（父级层级2+1）
      verify(userGroupInfoMapper, times(1)).insertUserGroupInfo(captor.capture());
      UserGroupInfoDO savedGroup = captor.getValue();

      // 明确校验层级值
      assertEquals("层级计算错误", 3, savedGroup.getLevel().intValue());

      // 额外校验其他关键字段
      assertEquals("用户组编码未正确设置", "UG_202307070001", savedGroup.getUserGroupCode());
      assertEquals("操作人未正确设置", adminUser.getAccount(), savedGroup.getCreatedBy());
    }
  }

  /**
   * 测试点：正常更新用户组信息（移动到同级目录）
   */
  @Test
  public void testUpdate_NormalCase() {
    // 准备测试数据
    UserGroupUpdateRequest request = UserInfoUtil.createUpdateRequest("GROUP_001", "PARENT_002");

    // 模拟当前用户
    try (MockedStatic<UserContextUtil> mocked = mockStatic(UserContextUtil.class)) {
      mocked.when(UserContextUtil::get).thenReturn(createUserInfo("admin"));

      // 模拟查询即将转移的用户组（层级3）
      UserGroupInfoDO toMoveGroup = UserInfoUtil.createUserGroupDO("GROUP_001", "用户组1", "PARENT_001", 3);
      // 第一次查询
      when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(
              Collections.singletonList(toMoveGroup))
          // 第二次查询
          .thenReturn(Collections.singletonList(UserInfoUtil.createUserGroupDO("PARENT_002", "父级组2", null, 2)))
          //第三次查询 模拟查询子节点（无子节点）
          .thenReturn(Collections.emptyList());

      // 执行测试
      userGroupInfoService.update(request);

      // 验证更新操作
      verify(userGroupInfoMapper, times(1)).update(argThat(updatedGroup -> {
        assertEquals("GROUP_001", updatedGroup.getUserGroupCode());
        assertEquals("新用户组名称", updatedGroup.getUserGroupName());
        assertEquals("ORG_001", updatedGroup.getOrgCode());
        assertEquals("admin", updatedGroup.getUpdatedBy());
        assertEquals(3, updatedGroup.getLevel().intValue());
        assertEquals("PARENT_002", updatedGroup.getUserGroupParentCode());
        return true;
      }));

      // 验证子节点未更新
      verify(userGroupInfoMapper, never()).updateBatch(anyList(), any(UserGroupInfoDO.class));
    }
  }

  /**
   * 测试点：高级目录向低级目录迁移（应失败）
   */
  @Test
  public void testUpdate_HighLevelToLowLevel() {
    UserGroupUpdateRequest request = UserInfoUtil.createUpdateRequest("GROUP_001", "PARENT_003");

    try (MockedStatic<UserContextUtil> mocked = mockStatic(UserContextUtil.class)) {
      mocked.when(UserContextUtil::get).thenReturn(createUserInfo("admin"));

      // 模拟查询即将转移的用户组（层级2）
      UserGroupInfoDO toMoveGroup = UserInfoUtil.createUserGroupDO("GROUP_001", "用户组1", "PARENT_001", 2);
      when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class)))
          .thenReturn(Collections.singletonList(toMoveGroup))
          // 目标层级3
          .thenReturn(Collections.singletonList(UserInfoUtil.createUserGroupDO("PARENT_003", "父级组3", null, 3)));

      try {
        // 执行测试（预期抛出异常）
        userGroupInfoService.update(request);
        // 若没有抛出异常，断言失败
        fail("预期抛出Exception，但实际未抛出");
      } catch (Exception e) {
        // 明确校验异常代码和消息
        assertTrue("异常消息应包含错误提示", e.getMessage().contains("高级目录不能向低级目录转移"));
      }
    }
  }

  /**
   * 测试点：更新不存在的用户组
   */
  @Test
  public void testUpdate_GroupNotExists() {
    UserGroupUpdateRequest request = UserInfoUtil.createUpdateRequest("NOT_EXISTS", "PARENT_001");

    try (MockedStatic<UserContextUtil> mocked = mockStatic(UserContextUtil.class)) {
      mocked.when(UserContextUtil::get).thenReturn(createUserInfo("admin"));

      // 模拟查询不存在的用户组
      when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(Collections.emptyList());

      try {
        // 执行测试（预期抛出异常）
        userGroupInfoService.update(request);
        // 若没有抛出异常，断言失败
        fail("预期抛出Exception，但实际未抛出");
      } catch (Exception e) {
        // 明确校验异常代码和消息
        assertTrue("异常消息应包含错误提示", e.getMessage().contains("即将转移的用户组不存在"));
      }
    }
  }

  /**
   * 测试点：更新带子节点的用户组
   */
  @Test
  public void testUpdate_WithChildren() {
    UserGroupUpdateRequest request = UserInfoUtil.createUpdateRequest("GROUP_001", "PARENT_002");

    try (MockedStatic<UserContextUtil> mocked = mockStatic(UserContextUtil.class)) {
      mocked.when(UserContextUtil::get).thenReturn(createUserInfo("admin"));

      // 模拟查询即将转移的用户组
      UserGroupInfoDO toMoveGroup = UserInfoUtil.createUserGroupDO("GROUP_001", "用户组1", "PARENT_001", 2);

      when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(
              Collections.singletonList(toMoveGroup))
          .thenReturn(Collections.singletonList(UserInfoUtil.createUserGroupDO("PARENT_002", "父级组2", null, 1)))
          .thenReturn(Arrays.asList(UserInfoUtil.createUserGroupDO("CHILD_001", "子组1", "GROUP_001", 3),
              UserInfoUtil.createUserGroupDO("CHILD_002", "子组2", "GROUP_001", 3)));

      // 执行测试
      userGroupInfoService.update(request);

      // 验证子节点更新
      verify(userGroupInfoMapper, times(1)).updateBatch(eq(Arrays.asList("CHILD_001", "CHILD_002")), argThat(parent -> {
        // 父级新层级2+1=3
        assertEquals(3, parent.getLevel().intValue());
        assertEquals("GROUP_001", parent.getUserGroupParentCode());
        return true;
      }));
    }
  }
}
