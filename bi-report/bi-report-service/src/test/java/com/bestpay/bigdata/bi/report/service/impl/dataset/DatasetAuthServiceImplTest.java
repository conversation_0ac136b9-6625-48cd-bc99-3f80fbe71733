package com.bestpay.bigdata.bi.report.service.impl.dataset;

import com.bestpay.bigdata.bi.common.dto.CurrentAiPlusUserGroup;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetAuthUserDTO;
import com.bestpay.bigdata.bi.report.enums.dataset.AuthUserTypeEnum;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;


public class DatasetAuthServiceImplTest {


    private MockedStatic<UserContextUtil> mockedUserContextUtil;

    @Before
    public void setUp() {
        // Mock静态工具类
        mockedUserContextUtil = mockStatic(UserContextUtil.class);
    }


    @After
    public void tearDown() {
        // 释放mock对象，防止影响其他测试用例
        mockedUserContextUtil.close();
    }

    /**
     * TC001: authUsers为null
     */
    @Test
    public void testIsContainsWithNullAuthUsers() {
        assertFalse(DatasetAuthServiceImpl.isContains(null));
    }

    /**
     * TC002: authUsers为空列表
     */
    @Test
    public void testIsContainsWithEmptyAuthUsers() {
        // Mock当前用户信息
        UserInfo currentUser = mock(UserInfo.class);
        currentUser.setUserGroupsList(Collections.emptyList());
        mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(currentUser);

        List<DatasetAuthUserDTO> authUsers = new ArrayList<>();
        assertFalse(DatasetAuthServiceImpl.isContains(authUsers));
    }

    /**
     * TC003: 用户组匹配（通过newValue）
     */
    @Test
    public void testIsContainsWithUserGroupMatchByNewValue() {
        // 准备测试数据
        List<DatasetAuthUserDTO> authUsers = new ArrayList<>();
        DatasetAuthUserDTO authUser = new DatasetAuthUserDTO();
        authUser.setType(AuthUserTypeEnum.userGroup.getCode());
        authUser.setNewValue("groupCode1");
        authUsers.add(authUser);

        // Mock当前用户信息
        UserInfo currentUser = mock(UserInfo.class);
        List<List<CurrentAiPlusUserGroup>> userGroupsList = new ArrayList<>();
        List<CurrentAiPlusUserGroup> userGroups = new ArrayList<>();
        CurrentAiPlusUserGroup group = new CurrentAiPlusUserGroup();
        group.setCode("groupCode1");
        userGroups.add(group);
        userGroupsList.add(userGroups);

        when(currentUser.getUserGroupsList()).thenReturn(userGroupsList);
        mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(currentUser);

        assertTrue(DatasetAuthServiceImpl.isContains(authUsers));
    }

    /**
     * TC004: 用户组匹配（通过value）
     */
    @Test
    public void testIsContainsWithUserGroupMatchByValue() {
        // 准备测试数据
        List<DatasetAuthUserDTO> authUsers = new ArrayList<>();
        DatasetAuthUserDTO authUser = new DatasetAuthUserDTO();
        authUser.setType(AuthUserTypeEnum.userGroup.getCode());
        authUser.setValue("groupCode2");
        authUsers.add(authUser);

        // Mock当前用户信息
        UserInfo currentUser = mock(UserInfo.class);
        List<List<CurrentAiPlusUserGroup>> userGroupsList = new ArrayList<>();
        List<CurrentAiPlusUserGroup> userGroups = new ArrayList<>();
        CurrentAiPlusUserGroup group = new CurrentAiPlusUserGroup();
        group.setCode("groupCode2");
        userGroups.add(group);
        userGroupsList.add(userGroups);

        when(currentUser.getUserGroupsList()).thenReturn(userGroupsList);
        mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(currentUser);

        assertTrue(DatasetAuthServiceImpl.isContains(authUsers));
    }

    /**
     * TC005: 用户匹配（通过newValue即oneId）
     */
    @Test
    public void testIsContainsWithUserMatchByNewValue() {
        // 准备测试数据
        List<DatasetAuthUserDTO> authUsers = new ArrayList<>();
        DatasetAuthUserDTO authUser = new DatasetAuthUserDTO();
        authUser.setType(AuthUserTypeEnum.user.getCode());
        authUser.setNewValue("oneId123");
        authUsers.add(authUser);

        // Mock当前用户信息
        UserInfo currentUser = mock(UserInfo.class);
        when(currentUser.getOneId()).thenReturn("oneId123");
        mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(currentUser);

        assertTrue(DatasetAuthServiceImpl.isContains(authUsers));
    }

    /**
     * TC007: 无匹配项
     */
    @Test
    public void testIsContainsWithNoMatch() {
        // 准备测试数据
        List<DatasetAuthUserDTO> authUsers = new ArrayList<>();
        DatasetAuthUserDTO authUser = new DatasetAuthUserDTO();
        authUser.setType(AuthUserTypeEnum.user.getCode());
        authUser.setValue("<EMAIL>");
        authUser.setNewValue("oneId123");
        authUsers.add(authUser);

        // Mock当前用户信息
        UserInfo currentUser = mock(UserInfo.class);
        when(currentUser.getEmail()).thenReturn("<EMAIL>");
        when(currentUser.getOneId()).thenReturn("oneId234");
        mockedUserContextUtil.when(UserContextUtil::getUserInfo).thenReturn(currentUser);

        assertFalse(DatasetAuthServiceImpl.isContains(authUsers));
    }

}
