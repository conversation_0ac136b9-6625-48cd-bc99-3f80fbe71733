package com.bestpay.bigdata.bi.report.service.impl.usergroupmanage;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.bestpay.bigdata.bi.common.enums.UserGroupQueryTypeEnum;
import com.bestpay.bigdata.bi.database.dao.usergroupmanage.UserGroupInfoDO;
import com.bestpay.bigdata.bi.database.mapper.usergroupmanage.UserGroupInfoMapper;
import com.bestpay.bigdata.bi.database.mapper.usergroupmanage.UserGroupUserRelationMapper;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserGroupChildrenDirResponse;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserGroupDirResponse;
import com.bestpay.bigdata.bi.report.usermanage.service.impl.UserGroupManageServiceImpl;
import com.bestpay.bigdata.bi.report.util.UserGroupDataUtil;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @ClassName UserGroupTreeBuilderServiceTest
 * @description  测试树结构构建方法
 * @date 2025/7/7
 */
@RunWith(MockitoJUnitRunner.class)
public class UserGroupTreeBuilderServiceTest {

  @Mock
  private UserGroupInfoMapper userGroupInfoMapper;

  @Mock
  private UserGroupUserRelationMapper userGroupUserRelationMapper;

  @InjectMocks
  private UserGroupManageServiceImpl userGroupInfoService;


  @Before
  public void setUp() throws Exception {

  }

  @After
  public void tearDown() throws Exception {
  }

  /**
   * 测试点：通过关键字查询，构建单层树结构
   */
  @Test
  public void testBuildSingleLevelTreeByKeyword() {
    // 模拟数据
    List<UserGroupInfoDO> singleLevelGroups = UserGroupDataUtil.createSingleLevelGroups();

    when(userGroupInfoMapper.queryUserGroupInfoByKeywordAndOrgCode(anyString(),
        eq(UserGroupDataUtil.TEST_ORG_CODE))).thenReturn(singleLevelGroups);
    when(userGroupInfoMapper.batchQueryGroupsByOrgCodes(anySet())).thenReturn(singleLevelGroups);

    // 执行测试
    List<UserGroupDirResponse> result = userGroupInfoService.buildUserGroupTree(UserGroupQueryTypeEnum.USER_GROUP, "测试",
        UserGroupDataUtil.TEST_ORG_CODE);

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    UserGroupDirResponse orgNode = result.get(0);
    assertEquals(UserGroupDataUtil.TEST_ORG_NAME, orgNode.getOrgName());

    // 验证一级节点
    List<UserGroupChildrenDirResponse> groups = orgNode.getGroups();
    assertEquals(2, groups.size());

    // 验证路径
    assertEquals(UserGroupDataUtil.TEST_ORG_NAME+",一级用户组1", groups.get(0).getPathName());
    assertEquals(UserGroupDataUtil.TEST_ORG_NAME+",一级用户组2", groups.get(1).getPathName());
  }

  /**
   * 测试点：通过用户ID查询，构建多层树结构
   */
  @Test
  public void testBuildMultiLevelTreeByUserId() {
    // 模拟数据
    List<UserGroupInfoDO> multiLevelGroups = UserGroupDataUtil.createMultiLevelGroups();
    when(userGroupUserRelationMapper.queryUserGroupCodeByOneId(eq(UserGroupDataUtil.TEST_USER_ID)))
        .thenReturn(Arrays.asList("GROUP001", "GROUP003"));
    when(userGroupInfoMapper.batchQueryGroupsByUserGroupCodes(anyList()))
        .thenReturn(multiLevelGroups);
    when(userGroupInfoMapper.batchQueryGroupsByOrgCodes(anySet()))
        .thenReturn(multiLevelGroups);

    // 执行测试
    List<UserGroupDirResponse> result = userGroupInfoService.buildUserGroupTree(
        UserGroupQueryTypeEnum.USER, UserGroupDataUtil.TEST_USER_ID, UserGroupDataUtil.TEST_ORG_CODE);

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    UserGroupDirResponse orgNode = result.get(0);

    // 验证多级结构
    UserGroupChildrenDirResponse level1Node = orgNode.getGroups().get(0);
    assertEquals(UserGroupDataUtil.TEST_ORG_NAME+",一级用户组1", level1Node.getPathName());

    UserGroupChildrenDirResponse level2Node = level1Node.getChildren().get(0);
    assertEquals(UserGroupDataUtil.TEST_ORG_NAME+",一级用户组1,二级用户组1", level2Node.getPathName());
  }

  /**
   * 测试点：查询无结果的情况
   */
  @Test
  public void testBuildTreeWithEmptyResult() {
    // 模拟空结果
    when(userGroupInfoMapper.queryUserGroupInfoByKeywordAndOrgCode(anyString(), anyString()))
        .thenReturn(Collections.emptyList());

    // 执行测试
    List<UserGroupDirResponse> result = userGroupInfoService.buildUserGroupTree(
        UserGroupQueryTypeEnum.USER_GROUP, "不存在的关键词", UserGroupDataUtil.TEST_ORG_CODE);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  /**
   * 测试点：包含多个组织的树结构
   */
  @Test
  public void testBuildTreeWithMultipleOrganizations() {
    // 模拟多组织数据
    List<UserGroupInfoDO> groupsWithMultipleOrgs = UserGroupDataUtil.createGroupsWithMultipleOrgs();

    when(userGroupInfoMapper.queryUserGroupInfoByKeywordAndOrgCode(anyString(), any()))
        .thenReturn(groupsWithMultipleOrgs);

    when(userGroupInfoMapper.batchQueryGroupsByOrgCodes(anySet()))
        .thenReturn(groupsWithMultipleOrgs);

    // 执行测试（查询所有组织）
    List<UserGroupDirResponse> result = userGroupInfoService.buildUserGroupTree(
        UserGroupQueryTypeEnum.USER_GROUP, "测试", null);

    // 验证结果
    assertNotNull(result);
    assertEquals(2, result.size());

    // 验证不同组织的路径
    UserGroupDirResponse org1 = result.get(0);
    UserGroupDirResponse org2 = result.get(1);

    assertEquals("组织A,部门A1", org1.getGroups().get(0).getPathName());
    assertEquals("组织B,部门B1", org2.getGroups().get(0).getPathName());
  }

  /**
   * 测试点：匹配节点但无子节点的情况(一级目录)
   */
  @Test
  public void testMatchedNodesWithoutChildren() {
    // 模拟数据
    List<UserGroupInfoDO> leafNodes = UserGroupDataUtil.createLeafNodes();
    when(userGroupInfoMapper.queryUserGroupInfoByKeywordAndOrgCode(anyString(), eq(UserGroupDataUtil.TEST_ORG_CODE)))
        .thenReturn(leafNodes);
    when(userGroupInfoMapper.batchQueryGroupsByOrgCodes(anySet()))
        .thenReturn(leafNodes);

    // 执行测试
    List<UserGroupDirResponse> result = userGroupInfoService.buildUserGroupTree(
        UserGroupQueryTypeEnum.USER_GROUP, "叶子", UserGroupDataUtil.TEST_ORG_CODE);

    // 验证结果
    assertNotNull(result);
    UserGroupDirResponse orgNode = result.get(0);

    // 验证叶子节点
    List<UserGroupChildrenDirResponse> groups = orgNode.getGroups();
    assertEquals(1, groups.size());
    assertNull(groups.get(0).getChildren());
    assertEquals("大数据部,叶子节点", groups.get(0).getPathName());
  }

  /**
   * 测试点：完整3层结构的树构建
   */
  @Test
  public void testBuildThreeLevelTreeStructure() {
    // 模拟3层结构数据
    List<UserGroupInfoDO> threeLevelGroups = UserGroupDataUtil.createThreeLevelGroups();
    when(userGroupInfoMapper.queryUserGroupInfoByKeywordAndOrgCode(anyString(), eq(UserGroupDataUtil.TEST_ORG_CODE)))
        .thenReturn(threeLevelGroups);
    when(userGroupInfoMapper.batchQueryGroupsByOrgCodes(anySet()))
        .thenReturn(threeLevelGroups);

    // 执行测试
    List<UserGroupDirResponse> result = userGroupInfoService.buildUserGroupTree(
        UserGroupQueryTypeEnum.USER_GROUP, "测试", UserGroupDataUtil.TEST_ORG_CODE);

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    UserGroupDirResponse orgNode = result.get(0);

    // 验证一级节点
    UserGroupChildrenDirResponse level1Node = orgNode.getGroups().get(0);
    assertEquals("大数据部,一级部门", level1Node.getPathName());

    // 验证二级节点
    UserGroupChildrenDirResponse level2Node = level1Node.getChildren().get(0);
    assertEquals("大数据部,一级部门,二级部门", level2Node.getPathName());

    // 验证三级节点
    UserGroupChildrenDirResponse level3Node = level2Node.getChildren().get(0);
    assertEquals("大数据部,一级部门,二级部门,三级部门", level3Node.getPathName());
  }

  /**
   * 测试点：3层结构中部分层级无匹配节点的情况
   */
  @Test
  public void testBuildThreeLevelTreeWithPartialMatches() {
    // 模拟部分匹配的3层结构数据
    List<UserGroupInfoDO> partialMatchGroups = UserGroupDataUtil.createThreeLevelGroupsWithPartialMatches();
    when(userGroupInfoMapper.queryUserGroupInfoByKeywordAndOrgCode(anyString(), eq(UserGroupDataUtil.TEST_ORG_CODE)))
        .thenReturn(partialMatchGroups);
    when(userGroupInfoMapper.batchQueryGroupsByOrgCodes(anySet()))
        .thenReturn(partialMatchGroups);

    // 执行测试
    List<UserGroupDirResponse> result = userGroupInfoService.buildUserGroupTree(
        UserGroupQueryTypeEnum.USER_GROUP, "测试", UserGroupDataUtil.TEST_ORG_CODE);

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    UserGroupDirResponse orgNode = result.get(0);

    // 验证一级节点
    UserGroupChildrenDirResponse level1Node = orgNode.getGroups().get(0);
    assertEquals("大数据部,一级部门", level1Node.getPathName());

    // 验证二级节点
    UserGroupChildrenDirResponse level2Node = level1Node.getChildren().get(0);
    assertEquals("大数据部,一级部门,二级部门", level2Node.getPathName());

    // 验证三级节点不存在（因为无匹配）
    assertNull(level2Node.getChildren());
  }

  /**
   * 测试点：同一层级多个兄弟节点的路径区分
   */
  @Test
  public void testSiblingNodesPathDifferentiation() {
    // 模拟同一层级多个兄弟节点的数据
    List<UserGroupInfoDO> siblingGroups = UserGroupDataUtil.createSiblingNodesGroups();
    when(userGroupInfoMapper.queryUserGroupInfoByKeywordAndOrgCode(anyString(), eq(UserGroupDataUtil.TEST_ORG_CODE)))
        .thenReturn(siblingGroups);
    when(userGroupInfoMapper.batchQueryGroupsByOrgCodes(anySet()))
        .thenReturn(siblingGroups);

    // 执行测试
    List<UserGroupDirResponse> result = userGroupInfoService.buildUserGroupTree(
        UserGroupQueryTypeEnum.USER_GROUP, "测试", UserGroupDataUtil.TEST_ORG_CODE);

    // 验证结果
    assertNotNull(result);
    UserGroupDirResponse orgNode = result.get(0);
    List<UserGroupChildrenDirResponse> level1Nodes = orgNode.getGroups();

    // 验证兄弟节点路径不同
    assertEquals("大数据部,一级部门1", level1Nodes.get(0).getPathName());
    assertEquals("大数据部,一级部门2", level1Nodes.get(1).getPathName());

    // 验证子节点路径正确
    UserGroupChildrenDirResponse child1 = level1Nodes.get(0).getChildren().get(0);
    UserGroupChildrenDirResponse child2 = level1Nodes.get(1).getChildren().get(0);

    assertEquals("大数据部,一级部门1,子部门1", child1.getPathName());
    assertEquals("大数据部,一级部门2,子部门2", child2.getPathName());
  }

  /**
   * 测试点：3层结构中混合不同父节点的情况
   */
  @Test
  public void testMixedParentNodesInThreeLevelTree() {
    // 模拟混合父节点的3层结构数据
    List<UserGroupInfoDO> mixedParentGroups = UserGroupDataUtil.createMixedParentNodesGroups();
    when(userGroupInfoMapper.queryUserGroupInfoByKeywordAndOrgCode(anyString(), eq(UserGroupDataUtil.TEST_ORG_CODE)))
        .thenReturn(mixedParentGroups);
    when(userGroupInfoMapper.batchQueryGroupsByOrgCodes(anySet()))
        .thenReturn(mixedParentGroups);

    // 执行测试
    List<UserGroupDirResponse> result = userGroupInfoService.buildUserGroupTree(
        UserGroupQueryTypeEnum.USER_GROUP, "测试", UserGroupDataUtil.TEST_ORG_CODE);

    // 验证结果
    assertNotNull(result);
    UserGroupDirResponse orgNode = result.get(0);
    List<UserGroupChildrenDirResponse> level1Nodes = orgNode.getGroups();

    // 验证不同父节点的路径
    UserGroupChildrenDirResponse childOfLevel1 = level1Nodes.get(0).getChildren().get(0);
    UserGroupChildrenDirResponse childOfLevel2 = childOfLevel1.getChildren().get(0);

    assertEquals("大数据部,一级部门,二级部门", childOfLevel1.getPathName());
    assertEquals("大数据部,一级部门,二级部门,三级部门", childOfLevel2.getPathName());
  }

  /**
   * 测试点：3层结构中节点名称重复的情况
   */
  @Test
  public void testDuplicateNodeNamesInThreeLevelTree() {
    // 模拟节点名称重复的3层结构数据
    List<UserGroupInfoDO> duplicateNameGroups = UserGroupDataUtil.createDuplicateNameGroups();
    when(userGroupInfoMapper.queryUserGroupInfoByKeywordAndOrgCode(anyString(), eq(UserGroupDataUtil.TEST_ORG_CODE)))
        .thenReturn(duplicateNameGroups);
    when(userGroupInfoMapper.batchQueryGroupsByOrgCodes(anySet()))
        .thenReturn(duplicateNameGroups);

    // 执行测试
    List<UserGroupDirResponse> result = userGroupInfoService.buildUserGroupTree(
        UserGroupQueryTypeEnum.USER_GROUP, "测试", UserGroupDataUtil.TEST_ORG_CODE);

    // 验证结果
    assertNotNull(result);
    UserGroupDirResponse orgNode = result.get(0);
    List<UserGroupChildrenDirResponse> level1Nodes = orgNode.getGroups();

    // 验证同名节点路径不同
    UserGroupChildrenDirResponse child1 = level1Nodes.get(0).getChildren().get(0);
    UserGroupChildrenDirResponse child2 = level1Nodes.get(1).getChildren().get(0);

    assertEquals("大数据部,部门A,部门B", child1.getPathName());
    assertEquals("大数据部,部门C,部门B", child2.getPathName());
  }
}