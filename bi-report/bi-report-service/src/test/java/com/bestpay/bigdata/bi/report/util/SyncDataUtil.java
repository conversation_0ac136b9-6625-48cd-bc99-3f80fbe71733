package com.bestpay.bigdata.bi.report.util;

import com.bestpay.bigdata.bi.common.bean.aiapi.ApiPlusUserInfo;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SyncDataUtil
 * @description 同步数据的数据准备
 * @date 2025/7/5
 */
public class SyncDataUtil {

  /**
   * 创建测试用的NewUserInfo列表
   */
  public static List<ApiPlusUserInfo> createTestNewUserInfos() {
    List<ApiPlusUserInfo> apiPlusUserInfos = new ArrayList<>();

    ApiPlusUserInfo userInfo1 = new ApiPlusUserInfo();
    userInfo1.setUuid("user1");
    userInfo1.setStatus(1);
    userInfo1.setAccount("<EMAIL>");

    ApiPlusUserInfo userInfo2 = new ApiPlusUserInfo();
    userInfo2.setUuid("user2");
    userInfo2.setStatus(1);
    userInfo2.setAccount("<EMAIL>");

    apiPlusUserInfos.add(userInfo1);
    apiPlusUserInfos.add(userInfo2);

    return apiPlusUserInfos;
  }
}
