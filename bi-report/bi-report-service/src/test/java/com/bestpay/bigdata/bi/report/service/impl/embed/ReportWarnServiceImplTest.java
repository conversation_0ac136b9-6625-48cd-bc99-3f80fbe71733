package com.bestpay.bigdata.bi.report.service.impl.embed;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnRuleDAOService;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnDTO;
import com.bestpay.bigdata.bi.report.response.report.ReportWarnConfigVO;
import com.bestpay.bigdata.bi.report.service.impl.report.ReportWarnServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@Slf4j
@ExtendWith(MockitoExtension.class)
class ReportWarnServiceImplTest {

    @Mock
    private ReportWarnRuleDAOService warnRuleDAOService;

    @InjectMocks
    private ReportWarnServiceImpl reportWarnService;

    private ReportWarnDTO reportWarnDo;

    private List<ReportWarnConfigVO> list = new ArrayList<>();

    @BeforeEach
    void setUp() {
        reportWarnDo = new ReportWarnDTO();
        reportWarnDo.setReportId("123");
        reportWarnDo.setWarnSourceType("REPORT");
        reportWarnDo.setCode("TEST001");
        ReportWarnConfigVO vo  = new ReportWarnConfigVO();
        vo.setUuid("uuid1");
        vo.setName("name1");
        vo.setReportField("field1");
        vo.setShowTypeName("TIME");
        vo.setPolymerization("safd");
        list.add(vo);
    }

    /**
     *  // 模拟isOnline返回true
     */
    @Test
    void judgeWarnModify_WhenReportNotOnline_ShouldReturnTrue() {
        ReportWarnServiceImpl spyService = spy(reportWarnService);
        doReturn(true).when(spyService).isOnline(any());
        Response<Boolean> response = spyService.judgeWarnModify(reportWarnDo);
        assertTrue(response.getData());
    }

    /**
     *   // 模拟isOnline返回false
     */
    @Test
    void judgeWarnModify_WhenReportOnlineAndNoChange_ShouldReturnFalse() {

        ReportWarnServiceImpl spyService = spy(reportWarnService);
        doReturn(false).when(spyService).isOnline(any());
        doReturn(Response.ok(list)).when(spyService).getConfigList(any(), any());

        // 模拟告警规则数据
        ReportWarnRuleDo ruleDo = new ReportWarnRuleDo();
        ruleDo.setConditionDesc("[{\"uuid\":\"uuid1\"}]");
        ruleDo.setExpectationDesc("{\"uuid\":\"uuid2\"}");
        when(warnRuleDAOService.query(any())).thenReturn(Collections.singletonList(ruleDo));

        Response<Boolean> response = spyService.judgeWarnModify(reportWarnDo);
        assertTrue(response.getData());
    }

    /**
     *  // 模拟isOnline返回false  且条件描述和预期描述都改变
     */
    @Test
    void judgeWarnModify_WhenConditionChanged_ShouldReturnFalse() {

        ReportWarnServiceImpl spyService = spy(reportWarnService);
        doReturn(false).when(spyService).isOnline(any());
        doReturn(Response.ok(list)).when(spyService).getConfigList(any(), any());
        // 模拟告警规则数据，其中条件描述包含不存在的UUID
        ReportWarnRuleDo ruleDo = new ReportWarnRuleDo();
        ruleDo.setConditionDesc("[{\"uuid\":\"uuid3\"}]");
        ruleDo.setExpectationDesc("{\"uuid\":\"uuid2\"}");
        when(warnRuleDAOService.query(any())).thenReturn(Collections.singletonList(ruleDo));

        Response<Boolean> response = spyService.judgeWarnModify(reportWarnDo);

        assertTrue(response.getData());

    }

    /**
     *  // 模拟isOnline返回false  且预期描述改变
     */
    @Test
    void judgeWarnModify_WhenExpectationChanged_ShouldReturnFalse() {
        // 模拟isOnline返回true
        ReportWarnServiceImpl spyService = spy(reportWarnService);
        doReturn(false).when(spyService).isOnline(any());
        doReturn(Response.ok(list)).when(spyService).getConfigList(any(), any());
        // 模拟告警规则数据，其中预期描述包含不存在的UUID
        ReportWarnRuleDo ruleDo = new ReportWarnRuleDo();
        ruleDo.setConditionDesc("[{\"uuid\":\"uuid1\"}]");
        ruleDo.setExpectationDesc("{\"uuid\":\"uuid3\"}");
        when(warnRuleDAOService.query(any())).thenReturn(Collections.singletonList(ruleDo));

        Response<Boolean> response = spyService.judgeWarnModify(reportWarnDo);

        assertTrue(response.getData());

    }

}