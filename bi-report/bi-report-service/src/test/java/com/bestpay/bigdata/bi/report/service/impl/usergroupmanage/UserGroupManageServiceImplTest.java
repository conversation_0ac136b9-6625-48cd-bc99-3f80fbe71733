package com.bestpay.bigdata.bi.report.service.impl.usergroupmanage;

import com.bestpay.bigdata.bi.common.error.ErrorCodeSupplier;
import com.bestpay.bigdata.bi.common.util.AssertUtil;
import com.bestpay.bigdata.bi.database.dao.usergroupmanage.UserGroupInfoDO;
import com.bestpay.bigdata.bi.database.mapper.usergroupmanage.UserGroupInfoMapper;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupUpdateRequest;
import com.bestpay.bigdata.bi.report.usermanage.service.impl.UserGroupManageServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025/7/23
 * @desc
 */
@RunWith(MockitoJUnitRunner.class)
public class UserGroupManageServiceImplTest {

    @InjectMocks
    private UserGroupManageServiceImpl userGroupInfoService;

    @Mock
    private UserGroupInfoMapper userGroupInfoMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testUpdate_Success_NoChildrenWithTarget() {
        // 准备测试数据
        UserGroupUpdateRequest request = new UserGroupUpdateRequest();
        request.setOrgCode("org1");
        request.setOrgName("组织1");
        request.setUserGroupCode("group1");
        request.setUserGroupName("用户组1");
        request.setUserGroupParentCode("targetGroup");

        UserGroupInfoDO oldUserGroupInfo = new UserGroupInfoDO();
        oldUserGroupInfo.setOrgCode("org1");
        oldUserGroupInfo.setUserGroupCode("group1");
        oldUserGroupInfo.setUserGroupName("用户组1");
        oldUserGroupInfo.setLevel(1);
        oldUserGroupInfo.setOrgName("组织1");

        UserGroupInfoDO targetParentUserGroupInfo = new UserGroupInfoDO();
        targetParentUserGroupInfo.setUserGroupCode("targetGroup");
        targetParentUserGroupInfo.setLevel(1);

        List<UserGroupInfoDO> targetList = Collections.singletonList(targetParentUserGroupInfo);

        when(userGroupInfoMapper.selectByOrgCodeAndName(anyString(), anyString())).thenReturn(Collections.emptyList());
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(Collections.emptyList());
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(Collections.emptyList());
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(targetList);

        // 执行测试
        assertDoesNotThrow(() -> userGroupInfoService.update(request));


    }

    @Test
    public void testUpdate_Success_WithChildrenWithTarget() {
        // 准备测试数据
        UserGroupUpdateRequest request = new UserGroupUpdateRequest();
        request.setOrgCode("org1");
        request.setOrgName("组织1");
        request.setUserGroupCode("group1");
        request.setUserGroupName("用户组1");
        request.setUserGroupParentCode("targetGroup");

        UserGroupInfoDO oldUserGroupInfo = new UserGroupInfoDO();
        oldUserGroupInfo.setOrgCode("org1");
        oldUserGroupInfo.setUserGroupCode("group1");
        oldUserGroupInfo.setUserGroupName("用户组1");
        oldUserGroupInfo.setLevel(1);
        oldUserGroupInfo.setOrgName("组织1");

        UserGroupInfoDO targetParentUserGroupInfo = new UserGroupInfoDO();
        targetParentUserGroupInfo.setUserGroupCode("targetGroup");
        targetParentUserGroupInfo.setLevel(1);

        UserGroupInfoDO childGroup = new UserGroupInfoDO();
        childGroup.setUserGroupCode("child1");
        childGroup.setUserGroupName("子组1");
        childGroup.setLevel(1);
        childGroup.setOrgCode("org1");

        List<UserGroupInfoDO> targetList = Collections.singletonList(targetParentUserGroupInfo);
        List<UserGroupInfoDO> childList = Collections.singletonList(childGroup);

        Map<String, List<UserGroupInfoDO>> parentUserGroupCodeMap = new HashMap<>();
        parentUserGroupCodeMap.put("group1", childList);

        when(userGroupInfoMapper.selectByOrgCodeAndName(anyString(), anyString())).thenReturn(Collections.emptyList());
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(childList);
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(childList);
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(targetList);
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(childList);

        // 执行测试
        assertDoesNotThrow(() -> userGroupInfoService.update(request));

    }

    @Test
    public void testUpdate_Success_NoTarget() {
        // 准备测试数据
        UserGroupUpdateRequest request = new UserGroupUpdateRequest();
        request.setOrgCode("org1");
        request.setOrgName("组织1");
        request.setUserGroupCode("group1");
        request.setUserGroupName("用户组1");
        request.setUserGroupParentCode(null);

        UserGroupInfoDO oldUserGroupInfo = new UserGroupInfoDO();
        oldUserGroupInfo.setOrgCode("org1");
        oldUserGroupInfo.setUserGroupCode("group1");
        oldUserGroupInfo.setUserGroupName("用户组1");
        oldUserGroupInfo.setLevel(2);
        oldUserGroupInfo.setOrgName("组织1");

        UserGroupInfoDO childGroup = new UserGroupInfoDO();
        childGroup.setUserGroupCode("child1");
        childGroup.setUserGroupName("子组1");
        childGroup.setLevel(2);
        childGroup.setOrgCode("org1");

        List<UserGroupInfoDO> childList = Collections.singletonList(childGroup);

        Map<String, List<UserGroupInfoDO>> parentUserGroupCodeMap = new HashMap<>();
        parentUserGroupCodeMap.put("group1", childList);

        when(userGroupInfoMapper.selectByOrgCodeAndName(anyString(), anyString())).thenReturn(Collections.emptyList());
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(childList);
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(childList);

        // 执行测试
        assertDoesNotThrow(() -> userGroupInfoService.update(request));


    }

    @Test
    public  void testUpdate_Fail_ChildrenLevelExceedsLimit() {
        // 准备测试数据
        UserGroupUpdateRequest request = new UserGroupUpdateRequest();
        request.setOrgCode("org1");
        request.setOrgName("组织1");
        request.setUserGroupCode("group1");
        request.setUserGroupName("用户组1");
        request.setUserGroupParentCode("targetGroup");

        UserGroupInfoDO oldUserGroupInfo = new UserGroupInfoDO();
        oldUserGroupInfo.setOrgCode("org1");
        oldUserGroupInfo.setUserGroupCode("group1");
        oldUserGroupInfo.setUserGroupName("用户组1");
        oldUserGroupInfo.setLevel(1);
        oldUserGroupInfo.setOrgName("组织1");

        UserGroupInfoDO targetParentUserGroupInfo = new UserGroupInfoDO();
        targetParentUserGroupInfo.setUserGroupCode("targetGroup");
        targetParentUserGroupInfo.setLevel(2);

        List<UserGroupInfoDO> targetList = Collections.singletonList(targetParentUserGroupInfo);

        UserGroupInfoDO childGroup = new UserGroupInfoDO();
        childGroup.setUserGroupCode("child1");
        childGroup.setUserGroupName("子组1");
        childGroup.setLevel(2);
        childGroup.setOrgCode("org1");

        List<UserGroupInfoDO> childList = Collections.singletonList(childGroup);

        Map<String, List<UserGroupInfoDO>> parentUserGroupCodeMap = new HashMap<>();
        parentUserGroupCodeMap.put("group1", childList);

        when(userGroupInfoMapper.selectByOrgCodeAndName(anyString(), anyString())).thenReturn(Collections.emptyList());
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(childList);
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(childList);
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(targetList);

        try (MockedStatic<AssertUtil> mockedAssertUtil = mockStatic(AssertUtil.class)) {
            // 设置静态方法的行为
            mockedAssertUtil.when(() -> AssertUtil.isTrue(any(ErrorCodeSupplier.class), anyBoolean(), anyString()))
                    .thenThrow(new RuntimeException("AssertUtil.isTrue called"));

            // 执行测试并验证异常
            assertThrows(RuntimeException.class, () -> userGroupInfoService.update(request));
        }
    }

    @Test
    public  void testUpdate_Fail_TargetGroupNotExist() {
        // 准备测试数据
        UserGroupUpdateRequest request = new UserGroupUpdateRequest();
        request.setOrgCode("org1");
        request.setOrgName("组织1");
        request.setUserGroupCode("group1");
        request.setUserGroupName("用户组1");
        request.setUserGroupParentCode("targetGroup");

        when(userGroupInfoMapper.selectByOrgCodeAndName(anyString(), anyString())).thenReturn(Collections.emptyList());
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(Collections.emptyList());
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(Collections.emptyList());

        try (MockedStatic<AssertUtil> mockedAssertUtil = mockStatic(AssertUtil.class)) {
            // 设置静态方法的行为
            mockedAssertUtil.when(() -> AssertUtil.notEmpty(any(ErrorCodeSupplier.class), anyList(), anyString()))
                    .thenThrow(new RuntimeException("AssertUtil.notEmpty called"));

            // 执行测试并验证异常
            assertThrows(RuntimeException.class, () -> userGroupInfoService.update(request));
        }
    }

    @Test
    public  void testUpdate_Fail_TargetGroupLevelExceedsLimit() {
        // 准备测试数据
        UserGroupUpdateRequest request = new UserGroupUpdateRequest();
        request.setOrgCode("org1");
        request.setOrgName("组织1");
        request.setUserGroupCode("group1");
        request.setUserGroupName("用户组1");
        request.setUserGroupParentCode("targetGroup");

        UserGroupInfoDO targetParentUserGroupInfo = new UserGroupInfoDO();
        targetParentUserGroupInfo.setUserGroupCode("targetGroup");
        targetParentUserGroupInfo.setLevel(2);

        List<UserGroupInfoDO> targetList = Collections.singletonList(targetParentUserGroupInfo);

        when(userGroupInfoMapper.selectByOrgCodeAndName(anyString(), anyString())).thenReturn(Collections.emptyList());
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(Collections.emptyList());
        when(userGroupInfoMapper.queryUserGroupInfos(any(UserGroupInfoDO.class))).thenReturn(targetList);

        try (MockedStatic<AssertUtil> mockedAssertUtil = mockStatic(AssertUtil.class)) {
            // 设置静态方法的行为
            mockedAssertUtil.when(() -> AssertUtil.isTrue(any(ErrorCodeSupplier.class), anyBoolean(), anyString()))
                    .thenThrow(new RuntimeException("AssertUtil.isTrue called"));

            // 执行测试并验证异常
            assertThrows(RuntimeException.class, () -> userGroupInfoService.update(request));
        }
    }


    /**
     * TC001: 正常情况：用户组列表不为空，包含 level == 1 和 level != 1 的情况
     */
    @Test
    public void testGetUserGroupMap_NormalCase() {
        // 准备测试数据
        UserGroupInfoDO group1 = new UserGroupInfoDO();
        group1.setUserGroupCode("group1");
        group1.setUserGroupParentCode("parent1");
        group1.setLevel(1);
        group1.setOrgCode("org1");
        group1.setCreatedAt(LocalDateTime.now());
        group1.setUpdatedAt(LocalDateTime.now());

        UserGroupInfoDO group2 = new UserGroupInfoDO();
        group2.setUserGroupCode("group2");
        group2.setUserGroupParentCode("parent2");
        group2.setLevel(2);
        group2.setOrgCode("org2");
        group2.setCreatedAt(LocalDateTime.now());
        group2.setUpdatedAt(LocalDateTime.now());

        List<UserGroupInfoDO> userGroupList = Arrays.asList(group1, group2);

        // Mock mapper 返回值
        when(userGroupInfoMapper.queryUserGroupInfos(new UserGroupInfoDO())).thenReturn(userGroupList);

        // 执行测试
        Map<String, String> result = userGroupInfoService.getUserGroupMap();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("org1", result.get("group1")); // level == 1 时 parentCode 被替换为 orgCode
        assertEquals("parent2", result.get("group2")); // level != 1 时 parentCode 保持不变
    }

    /**
     * TC002: 边界情况：用户组列表为空
     */
    @Test
    public void testGetUserGroupMap_EmptyList() {
        // Mock mapper 返回空列表
        when(userGroupInfoMapper.queryUserGroupInfos(new UserGroupInfoDO())).thenReturn(Collections.emptyList());

        // 执行测试
        Map<String, String> result = userGroupInfoService.getUserGroupMap();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * TC003: 边界情况：用户组列表中所有用户组 level == 1
     */
    @Test
    public void testGetUserGroupMap_AllLevelOne() {
        // 准备测试数据
        UserGroupInfoDO group1 = new UserGroupInfoDO();
        group1.setUserGroupCode("group1");
        group1.setUserGroupParentCode("parent1");
        group1.setLevel(1);
        group1.setOrgCode("org1");
        group1.setCreatedAt(LocalDateTime.now());
        group1.setUpdatedAt(LocalDateTime.now());

        UserGroupInfoDO group2 = new UserGroupInfoDO();
        group2.setUserGroupCode("group2");
        group2.setUserGroupParentCode("parent2");
        group2.setLevel(1);
        group2.setOrgCode("org2");
        group2.setCreatedAt(LocalDateTime.now());
        group2.setUpdatedAt(LocalDateTime.now());

        List<UserGroupInfoDO> userGroupList = Arrays.asList(group1, group2);

        // Mock mapper 返回值
        when(userGroupInfoMapper.queryUserGroupInfos(new UserGroupInfoDO())).thenReturn(userGroupList);

        // 执行测试
        Map<String, String> result = userGroupInfoService.getUserGroupMap();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("org1", result.get("group1")); // level == 1 时 parentCode 被替换为 orgCode
        assertEquals("org2", result.get("group2")); // level == 1 时 parentCode 被替换为 orgCode
    }
}

