package com.bestpay.bigdata.bi.report.cache;

import com.alibaba.fastjson.JSONObject;
import com.bestpay.bigdata.bi.report.cache.bean.chart.ChartCacheRequestBean;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardDataRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.stream.Collectors;

public class ChartCacheHandlerTest {

    @InjectMocks
    private ChartCacheHandler ChartCacheHandler;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试json转换后的对象转换是否正常 -- 部分字段为null的边界测试
     * 要求解析转换正常不报错
     */
    @Test
    public void cacheRequestTransferQuery_judgeJson() {
        //获取到查询chart数据的传参json对象
        TableCardDataRequest request = JSONObject.parseObject(readCheckNullParamChartRequestModelJson(), TableCardDataRequest.class);

        //执行转换
        ChartCacheHandler.getChartCacheRequestBean(request);
    }

    /**
     * 测试json转换后的对象转换是否正常
     */
    @Test
    public void cacheRequestTransferQuery() {
        //获取到查询chart数据的传参json对象
        TableCardDataRequest request = JSONObject.parseObject(readChartRequestModelJson(), TableCardDataRequest.class);

        //执行转换
        ChartCacheRequestBean chartCacheRequestBean = ChartCacheHandler.getChartCacheRequestBean(request);

        //校验
        ChartCacheRequestBean checkRequest = JSONObject.parseObject(readCheckChartRequestModelJson(), ChartCacheRequestBean.class);
        Assert.assertEquals("转换数据异常",checkRequest, chartCacheRequestBean);
    }

    /**
     * 测试json转换后的对象转换是否正常 -- 无关字段添加 不影响缓存测试
     */
    @Test
    public void cacheRequestTransferQuery_insertParam() {
        //获取到查询chart数据的传参json对象
        TableCardDataRequest request = JSONObject.parseObject(readChartInsertRequestModelJson(), TableCardDataRequest.class);

        //执行转换
        ChartCacheRequestBean chartCacheRequestBean = ChartCacheHandler.getChartCacheRequestBean(request);

        //校验
        ChartCacheRequestBean checkRequest = JSONObject.parseObject(readCheckChartRequestModelJson(), ChartCacheRequestBean.class);
        Assert.assertEquals("转换数据异常",checkRequest, chartCacheRequestBean);
    }


    /**
     * 获取chart数据request的json对象
     *
     * @return
     */
    private String readChartRequestModelJson() {
        String tmpPath = "/src/test/java/com/bestpay/bigdata/bi/report/cache/data/";
        String filePath = System.getProperty("user.dir") + tmpPath + "chart_request_model.json";

        try {
            String content = Files.lines(Paths.get(filePath))
                    .collect(Collectors.joining(System.lineSeparator()));
            return content;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取chart数据request的json对象 在原有基础上添加不关键的字段
     *
     * @return
     */
    private String readChartInsertRequestModelJson() {
        String tmpPath = "/src/test/java/com/bestpay/bigdata/bi/report/cache/data/";
        String filePath = System.getProperty("user.dir") + tmpPath + "chart_request_model.json";

        try {
            String content = Files.lines(Paths.get(filePath))
                    .collect(Collectors.joining(System.lineSeparator()));
            return content;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取chart数据转换后的json目标对象
     *
     * @return
     */
    private String readCheckChartRequestModelJson() {
        String tmpPath = "/src/test/java/com/bestpay/bigdata/bi/report/cache/data/";
        String filePath = System.getProperty("user.dir") + tmpPath + "check_chart_request_model.json";

        try {
            String content = Files.lines(Paths.get(filePath))
                    .collect(Collectors.joining(System.lineSeparator()));
            return content;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取chart数据转换后的json目标对象
     *
     * @return
     */
    private String readCheckNullParamChartRequestModelJson() {
        String tmpPath = "/src/test/java/com/bestpay/bigdata/bi/report/cache/data/";
        String filePath = System.getProperty("user.dir") + tmpPath + "check_chart_request_model.json";

        try {
            String content = Files.lines(Paths.get(filePath))
                    .collect(Collectors.joining(System.lineSeparator()));
            return content;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

}
