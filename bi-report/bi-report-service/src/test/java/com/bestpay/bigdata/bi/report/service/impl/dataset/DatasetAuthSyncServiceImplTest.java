package com.bestpay.bigdata.bi.report.service.impl.dataset;


import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetRowAuthRuleDo;
import com.bestpay.bigdata.bi.database.mapper.dataset.DatasetRowAuthRuleMapper;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetAuthUserDTO;
import com.bestpay.bigdata.bi.report.usermanage.service.DbUserManageService;
import com.bestpay.bigdata.bi.report.usermanage.service.UserGroupManagerService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DatasetAuthSyncServiceImplTest {

  @InjectMocks
  private DatasetAuthSyncServiceImpl datasetAuthSyncService;
  @Mock
  private DatasetRowAuthRuleMapper datasetRowAuthRuleMapper;

  @Mock
  private DbUserManageService dbUserManageService;
  @Mock
  private ApolloRefreshConfig apolloRefreshConfig;
  @Mock
  private UserGroupManagerService userGroupManagerService;

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void checkRowAuthRuleList_test() {

    //造db返回数据
    List<DatasetRowAuthRuleDo> ruleList = new ArrayList<>();

    DatasetRowAuthRuleDo rule = new DatasetRowAuthRuleDo();
    rule.setId(1L);
    rule.setUserType("part");
    DatasetAuthUserDTO user = new DatasetAuthUserDTO();
    user.setValue("<EMAIL>");
    user.setName("maohonghui(大数据人工智能院)");
    user.setType("user");
    rule.setAuthUsers(JSONUtil.toJsonStr(Collections.singletonList(user)));
    ruleList.add(rule);

    when(datasetRowAuthRuleMapper.queryAll()).thenReturn(ruleList);
    when(userGroupManagerService.queryUserGroupInfos(any())).thenReturn(Collections.emptyList());

    //执行
    datasetAuthSyncService.setUserGroupMap("", "");
    System.out.println(datasetAuthSyncService.dataCorrectionRowAuthRuleErrorList());
  }

  @Test
  public void testParseUserWithOrganization_NormalCase() {
    // Given
    String input = "张三(组织A)";
    // When
    DatasetAuthSyncServiceImpl.UserOrg result = datasetAuthSyncService.parseUserWithOrganization(input);
    // Then
    assertNotNull(result);
    assertEquals("张三", result.getUsername());
    assertEquals("组织A", result.getOrganization());
  }

  @Test
  public void testParseUserWithOrganization_InputIsNull() {
    // Given
    String input = null;
    // When & Then
    IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
        () -> datasetAuthSyncService.parseUserWithOrganization(input));
    assertEquals("输入字符串不能为空", exception.getMessage());
  }

  @Test
  public void testParseUserWithOrganization_InputIsEmpty() {
    // Given
    String input = "";
    // When & Then
    IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
        () -> datasetAuthSyncService.parseUserWithOrganization(input));
    assertEquals("输入字符串不能为空", exception.getMessage());
  }

  @Test
  public void testParseUserWithOrganization_InvalidFormat() {
    // When & Then
    String input = "张三)(组织";
    IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
        () -> datasetAuthSyncService.parseUserWithOrganization(input));
    assertTrue(exception.getMessage().contains("输入格式不正确"));
  }

  @Test
  public void testParseUserWithOrganization_UsernameIsEmpty() {
    // Given
    String input = "(组织A)";
    // When & Then
    IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
        () -> datasetAuthSyncService.parseUserWithOrganization(input));
    assertTrue(exception.getMessage().contains("用户名和组织名都不能为空"));
  }

  @Test
  public void testParseUserWithOrganization_OrganizationIsEmpty() {
    // Given
    String input = "张三()";
    // When & Then
    IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
        () -> datasetAuthSyncService.parseUserWithOrganization(input));
    assertTrue(exception.getMessage().contains("用户名和组织名都不能为空"));
  }

  @Test
  public void testUserOrgConstructorAndGetters() {
    // Given
    String username = "测试用户";
    String organization = "测试组织";
    // When
    DatasetAuthSyncServiceImpl.UserOrg userOrg = new DatasetAuthSyncServiceImpl.UserOrg(username, organization);
    // Then
    assertEquals(username, userOrg.getUsername());
    assertEquals(organization, userOrg.getOrganization());
  }
}
