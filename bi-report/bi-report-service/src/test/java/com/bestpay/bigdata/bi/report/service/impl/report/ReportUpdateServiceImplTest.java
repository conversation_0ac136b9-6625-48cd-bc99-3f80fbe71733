package com.bestpay.bigdata.bi.report.service.impl.report;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.report.ReportUuidGenerateUtil;
import com.bestpay.bigdata.bi.common.dto.report.component.CommonComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.DimensionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.dao.report.NewReportDO;
import com.bestpay.bigdata.bi.report.request.report.ReportCopyRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportUuidRequest;
import com.bestpay.bigdata.bi.report.request.report.UpdateReportRequest;
import com.bestpay.bigdata.bi.report.service.impl.report.component.ComponentHandlerRegister;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import com.bestpay.bigdata.bi.report.util.ParamCheckUtil;
import com.bestpay.bigdata.bi.report.util.ReportDateUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @Author: wybStart
 * @Date: 2025/6/6
 * @Description:
 *
 *     测试目标：
 *         1. 正常场景下是否正确生成指定格式的 UUID。
 *         2. 异常输入时是否抛出预期的异常信息。
 */
public class ReportUpdateServiceImplTest {


    @Mock
    private AuthorityCheckUtil authorityCheckUtil;

    @Mock
    private ComponentHandlerRegister componentHandlerRegister;

    @Mock
    private ParamCheckUtil paramCheckUtil;

    @Mock
    private ReportDateUtil reportDateUtil;

    /**
     * 使用 @InjectMocks 注解注入被测试的服务类。
     * ReportUpdateServiceImpl 是我们要进行单元测试的目标类。
     */
    @InjectMocks
    private ReportUpdateServiceImpl reportUpdateService;

    /**
     * 初始化 Mockito 的注解支持。
     * 在 JUnit 测试中使用 @Mock 和 @InjectMocks 注解前必须调用此初始化。
     */
    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 【测试用例】验证当 isConfig = true 且 isCompute = false 时，
     * 是否生成以 "config_" 开头的 configUuid。
     *
     * 目的：
     *     确保在“报表配置”场景下生成的是 configUuid。
     */
    @Test
    void testGenerateReportUuid_ForConfig() {
        // Arrange：构造一个表示“配置字段”的请求对象
        ReportUuidRequest request = new ReportUuidRequest();
        request.setIsConfig(true);
        request.setIsCompute(false);

        // Act：调用被测方法
        String actualUuid = reportUpdateService.generateReportUuid(request);

        // Assert：验证结果是否符合预期
        assertNotNull(actualUuid, "生成的 UUID 不应为空");
        assertTrue(actualUuid.startsWith(ReportUuidGenerateUtil.REPORT_CONFIG_UUID_PREFIX),
                "UUID 应以 '" + ReportUuidGenerateUtil.REPORT_CONFIG_UUID_PREFIX + "' 开头");
    }

    /**
     * 【测试用例】验证当 isConfig = false 且 isCompute = true 时，
     * 是否生成以 "report_compute_" 开头的 computeUuid。
     *
     * 目的：
     *     确保在“创建计算字段”场景下生成的是 computeUuid。
     */
    @Test
    void testGenerateReportUuid_ForCompute() {
        // Arrange：构造一个表示“计算字段”的请求对象
        ReportUuidRequest request = new ReportUuidRequest();
        request.setIsConfig(false);
        request.setIsCompute(true);

        // Act
        String actualUuid = reportUpdateService.generateReportUuid(request);

        // Assert
        assertNotNull(actualUuid, "生成的 UUID 不应为空");
        assertTrue(actualUuid.startsWith(ReportUuidGenerateUtil.REPORT_COMPUTE_UUID_PREFIX),
                "UUID 应以 '" + ReportUuidGenerateUtil.REPORT_COMPUTE_UUID_PREFIX + "' 开头");
    }

    /**
     * 【测试用例】验证当 isConfig = true 且 isCompute = true 时，
     * 是否抛出参数校验异常。
     *
     * 目的：
     *     验证业务逻辑是否拒绝非法参数组合（isConfig 和 isCompute 同时为 true）。
     */
    @Test
    void testGenerateReportUuid_ThrowsException_WhenBothTrue() {
        // Arrange：构造非法参数组合
        ReportUuidRequest request = new ReportUuidRequest();
        request.setIsConfig(true);
        request.setIsCompute(true);

        // Act & Assert：验证是否抛出预期异常
        Exception exception = assertThrows(Exception.class, () -> {
            reportUpdateService.generateReportUuid(request);
        });

        // 断言异常信息包含关键词
        assertTrue(exception.getMessage().contains("参数配置错误"),
                "异常信息应提示 '参数配置错误'");
    }

    /**
     * 【测试用例】验证当 isConfig = false 且 isCompute = false 时，
     * 是否抛出参数校验异常。
     *
     * 目的：
     *     验证业务逻辑是否拒绝非法参数组合（isConfig 和 isCompute 同时为 false）。
     */
    @Test
    void testGenerateReportUuid_ThrowsException_WhenBothFalse() {
        // Arrange：构造非法参数组合
        ReportUuidRequest request = new ReportUuidRequest();
        request.setIsConfig(false);
        request.setIsCompute(false);

        // Act & Assert
        Exception exception = assertThrows(Exception.class, () -> {
            reportUpdateService.generateReportUuid(request);
        });

        // 断言异常信息包含关键词
        assertTrue(exception.getMessage().contains("参数配置错误"),
                "异常信息应提示 '参数配置错误'");
    }



    // 报表名称已存在，应抛出 BusinessException
    @Test
    public void testCopy_ReportNameAlreadyExists_ShouldThrowBusinessException() {
        ReportCopyRequest request = new ReportCopyRequest();
        request.setReportId(1L);
        request.setReportName("Existing Report");

        doThrow(new BusinessException(CodeEnum.REPORT_NAME_EXITING.name())).when(authorityCheckUtil).checkOwnerAndAuth(any());

        assertThrows(BusinessException.class, () -> {
            reportUpdateService.copy(request);
        });
    }


    // ✅ 非计算字段的 UUID 不应被修改
    @Test
    public void testCopy_NonComputeFields_ShouldKeepOriginalUuid() {
        String reportModelJson = readReportModelJson();
        NewReportDO bean = JSONUtil.toBean(reportModelJson, NewReportDO.class);

        System.out.println(bean);
    }


    private String readReportModelJson() {
        String tmpPath = "/src/test/java/com/bestpay/bigdata/bi/report/service/impl/report/";
        String filePath = System.getProperty("user.dir") + tmpPath + "report_model.json";

        try {
            String content = Files.lines(Paths.get(filePath))
                    .collect(Collectors.joining(System.lineSeparator()));
            return content;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
