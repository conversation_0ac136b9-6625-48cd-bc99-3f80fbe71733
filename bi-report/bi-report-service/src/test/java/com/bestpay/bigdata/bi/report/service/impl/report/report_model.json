{"id": 5280, "orgSelected": "20005", "orgCode": "20005", "dirId": 24, "dirName": "Bi项目组生产验证", "reportName": "test_wyb", "orgAuth": null, "reportDesc": "", "dataAuth": "-1.-1.-1", "orderColumnList": [{"id": 2675, "uuid": "report0E32D6CB-C760-4358-8FB0-F7E973D97B53", "configUuid": "config_c2eea918-41c9-44fd-9cab-05cde5772b29", "name": "省份", "originalName": "省份", "showTypeName": "CHARACTER_SELECT", "enName": "mobile_prov_nm", "indexName": null, "type": "ASC", "fun": null, "originEnName": null, "typeName": null, "polymerization": null, "reportField": "field", "customOrderList": [], "calculateLogic": null, "orderParam": null, "isComputeField": null}], "computeColumnList": [{"id": 61392202, "dateGroupType": 1, "calculateLogic": null, "computeFieldLogic": null, "uuid": "report_compute_d5d71c1b-e104-44f7-92af-98793ae43748", "typeName": null, "originEnName": null, "name": "test_计算字段", "indexName": "compute_emgnbgij_51785489_index", "enName": "compute_emgnbgij_51785489", "originalName": null, "showTypeName": "DECIMAL", "fun": "[成功授信金额]", "isFold": null, "reportField": "index", "highFunctions": [], "functionInfo": null, "fieldType": "MEASURE", "fieldName": null, "resourceType": "REPORT", "componentType": null, "values": null, "parentId": null, "valueList": null, "isHide": null, "computeField": false}], "reportStructureList": [{"id": 2668, "name": "申请流水号", "showTypeName": "CHARACTER_INPUT", "dateGroupType": null, "enName": "apply_id", "type": null, "uuid": "reportFEE8549B-3F8E-4331-886F-C136F1E561B6", "parentId": null, "childColumnList": null}, {"id": null, "name": "申请日期_层级结构", "showTypeName": "DATETIME", "dateGroupType": null, "enName": null, "type": "layer", "uuid": "E69F82AE-840A-470F-8EA9-C086A65322C0", "parentId": null, "childColumnList": [{"id": 2669, "name": "申请日期(年)", "showTypeName": "DATETIME", "dateGroupType": 3, "enName": "apply_dt", "type": null, "uuid": "reportC6E54584-7415-41AF-8811-0696DC64A17F", "parentId": "E69F82AE-840A-470F-8EA9-C086A65322C0", "childColumnList": null}, {"id": 2669, "name": "申请日期(季)", "showTypeName": "DATETIME", "dateGroupType": 5, "enName": "apply_dt", "type": null, "uuid": "report8032C6E1-DE61-4578-9865-D60B93F862C3", "parentId": "E69F82AE-840A-470F-8EA9-C086A65322C0", "childColumnList": null}, {"id": 2669, "name": "申请日期(月)", "showTypeName": "DATETIME", "dateGroupType": 2, "enName": "apply_dt", "type": null, "uuid": "reportD088830C-351D-4326-8C60-340E0EED26F0", "parentId": "E69F82AE-840A-470F-8EA9-C086A65322C0", "childColumnList": null}, {"id": 2669, "name": "申请日期(周)", "showTypeName": "DATETIME", "dateGroupType": 4, "enName": "apply_dt", "type": null, "uuid": "report02888348-EBD2-4387-9567-CCB1CB25F352", "parentId": "E69F82AE-840A-470F-8EA9-C086A65322C0", "childColumnList": null}, {"id": 2669, "name": "申请日期(日)", "showTypeName": "DATETIME", "dateGroupType": 1, "enName": "apply_dt", "type": null, "uuid": "report4671A79A-F69A-4BD2-80B0-D6258CDF793F", "parentId": "E69F82AE-840A-470F-8EA9-C086A65322C0", "childColumnList": null}, {"id": 2669, "name": "申请日期(秒)", "showTypeName": "DATETIME", "dateGroupType": 6, "enName": "apply_dt", "type": null, "uuid": "report5B0F081A-883C-4434-B44B-ADEFB6934023", "parentId": "E69F82AE-840A-470F-8EA9-C086A65322C0", "childColumnList": null}]}, {"id": 2670, "name": "合作方名称", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "partner_nm", "type": null, "uuid": "report9B0C0DF7-90EA-469F-85D3-D650114FA991", "parentId": null, "childColumnList": null}, {"id": 2671, "name": "新老客", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "new_or_old", "type": null, "uuid": "reportBAA9AD7F-066E-45E4-83DF-9FEC4A0B794D", "parentId": null, "childColumnList": null}, {"id": 2672, "name": "用户申请次数", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "all_batch_seq_no_td", "type": null, "uuid": "report7C963516-4C1B-4FD9-AE82-076D4CF31E59", "parentId": null, "childColumnList": null}, {"id": 2673, "name": "一级渠道", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "first_channel_name", "type": null, "uuid": "reportDCA98B78-72FF-4094-9EEE-15BA63DDC020", "parentId": null, "childColumnList": null}, {"id": 2674, "name": "二级渠道", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "second_channel_name", "type": null, "uuid": "reportD7886C13-2007-4062-A39E-7295A26ABF9E", "parentId": null, "childColumnList": null}, {"id": 2675, "name": "省份", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "mobile_prov_nm", "type": null, "uuid": "report0E32D6CB-C760-4358-8FB0-F7E973D97B53", "parentId": null, "childColumnList": null}, {"id": 2676, "name": "城市", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "mobile_city_nm", "type": null, "uuid": "report43037DCC-5D8E-4D4A-85A2-719B898653F1", "parentId": null, "childColumnList": null}, {"id": 2677, "name": "用户等级", "showTypeName": "CHARACTER_INPUT", "dateGroupType": null, "enName": "lsjr_cust_lvl", "type": null, "uuid": "reportDA0F38D3-7A88-4936-9976-95ACCD86325C", "parentId": null, "childColumnList": null}, {"id": 2678, "name": "运营商", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "clec_nm", "type": null, "uuid": "report1020A876-76C5-42DB-8097-7ABDFB8E644A", "parentId": null, "childColumnList": null}, {"id": 2679, "name": "星座", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "star_sign", "type": null, "uuid": "report3F0F51DC-E219-4573-9FD7-E9DC91CF6B8B", "parentId": null, "childColumnList": null}, {"id": 2680, "name": "年龄", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "gender", "type": null, "uuid": "report9904E7A8-567F-427F-8A14-9DD7E52D8F07", "parentId": null, "childColumnList": null}, {"id": 2681, "name": "申请客户号", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "lsjr_cust_id", "type": null, "uuid": "report0338E2A7-F08A-48E7-8C76-3E1D7713ADE3", "parentId": null, "childColumnList": null}, {"id": 2682, "name": "成功申请客户号", "showTypeName": "CHARACTER_SELECT", "dateGroupType": null, "enName": "succe_lsjr_cust_id", "type": null, "uuid": "reportD2EF3548-D52B-4BB6-8C13-47F8EDF7A25F", "parentId": null, "childColumnList": null}, {"id": 13897, "name": "数据集计算字段", "showTypeName": "CHARACTER_INPUT", "dateGroupType": null, "enName": "compute_ffcejgdb_31467778", "type": null, "uuid": "reportB036A8B9-13B3-4D7F-8C45-7B5A1375996F", "parentId": null, "childColumnList": null}, {"id": 13898, "name": "计算字段-维度-有内部聚合逻辑", "showTypeName": "DECIMAL", "dateGroupType": null, "enName": "compute_ebnnfhem_29565511", "type": null, "uuid": "report9F8F8D4B-9330-42F5-92CA-4FF3F4B56320", "parentId": null, "childColumnList": null}], "tableConfigurationObj": {"basicFormat": {"pageEnabled": null, "pageSize": 10, "isMergeCell": false, "alignmentType": "", "isWrap": false, "excludeTotalAndSubTotal": false, "showSequenceColumn": false, "columnWidthConfigGroup": "Custom", "customColumnWidth": [{"uuid": "report0E32D6CB-C760-4358-8FB0-F7E973D97B53", "columnConfig": "80", "columnName": "省份", "reportField": "field"}, {"uuid": "report43037DCC-5D8E-4D4A-85A2-719B898653F1", "columnConfig": null, "columnName": "城市", "reportField": "field"}, {"uuid": "report2E7F1D59-6911-46DE-94FB-90744C756E2B", "columnConfig": null, "columnName": "计算字段-度量-有内部聚合逻辑", "reportField": "index"}, {"uuid": "report02BACF5D-B636-4CFB-8A4C-83ECACFE076C", "columnConfig": "120", "columnName": "成功授信金额", "reportField": "index"}, {"uuid": "report2538F3F2-3D53-4EE1-8574-2D6356E62F10", "columnConfig": "100", "columnName": "test_计算字段", "reportField": "index"}], "styleSelection": "num"}, "themeStyle": {"colorConfig": "rgba(235, 238, 245, 1)", "zebraStripes": false}}, "contrastColumnList": [{"id": 1, "dateGroupType": null, "calculateLogic": null, "computeFieldLogic": null, "uuid": "report0da587df-4ac0-4fe2-9f4d-739845c11839", "configUuid": "config_2ea776b3-8aef-48c9-adb6-8922f33f612e", "typeName": null, "originEnName": null, "name": "度量名", "indexName": null, "enName": "measureName", "originalName": "度量名", "showTypeName": "MEASURE", "fun": null, "isFold": null, "reportField": "measure", "highFunctions": [], "functionInfo": null, "fieldType": null, "fieldName": null, "resourceType": "REPORT", "componentType": null, "values": null, "parentId": null, "dateShowFormat": null, "valueList": null, "isHide": null, "showSubtotal": false, "positionIndex": 0, "computeField": false}], "ownerNameCh": "闻一波", "ownerName": "wenyibo", "email": "<EMAIL>", "reportType": 1, "fileContainSensitiveInfo": 1, "sensitiveFields": "[{\"id\":2676,\"name\":\"城市\",\"originalName\":\"城市\",\"enName\":\"mobile_city_nm\",\"showTypeName\":\"CHARACTER_SELECT\",\"reportField\":\"field\",\"fieldType\":\"DIMENSION\",\"dateGroupType\":null,\"type\":null,\"uuid\":\"report43037DCC-5D8E-4D4A-85A2-719B898653F1\",\"childColumnList\":null,\"fun\":null,\"isComputeField\":false}]", "statusCode": 0, "rollupDown": null, "collectionId": null, "chartType": 0, "chartFieldObj": {"showCount": false, "colorGroup": 0, "overlayIndexShowCount": false, "chart": true}, "showIndexTotalObj": {"rowTotal": "no", "colTotal": "no", "subTotal": "no"}, "maxRows": null, "createdAt": "2025-06-09T02:46:49.000+0000", "createdBy": "<EMAIL>", "updatedAt": "2025-06-09T02:46:49.000+0000", "updatedBy": "<EMAIL>", "createdByOrg": null, "showColumnList": [{"id": 2675, "dateGroupType": null, "calculateLogic": null, "computeFieldLogic": null, "uuid": "report0E32D6CB-C760-4358-8FB0-F7E973D97B53", "configUuid": "config_f5c902b7-852b-4633-b1ca-09b9c47524a3", "typeName": null, "originEnName": null, "name": "省份", "indexName": null, "enName": "mobile_prov_nm", "originalName": "省份", "showTypeName": "CHARACTER_SELECT", "fun": null, "isFold": null, "reportField": "field", "highFunctions": [], "functionInfo": null, "fieldType": "DIMENSION", "fieldName": null, "resourceType": "REPORT", "componentType": null, "values": null, "parentId": null, "dateShowFormat": null, "valueList": null, "isHide": false, "showSubtotal": false, "positionIndex": 0, "computeField": false}, {"id": 2676, "dateGroupType": null, "calculateLogic": null, "computeFieldLogic": null, "uuid": "report43037DCC-5D8E-4D4A-85A2-719B898653F1", "configUuid": "config_e53b45df-498d-4b58-9fd1-41fefc442e04", "typeName": null, "originEnName": null, "name": "城市", "indexName": null, "enName": "mobile_city_nm", "originalName": "城市", "showTypeName": "CHARACTER_SELECT", "fun": null, "isFold": null, "reportField": "field", "highFunctions": [], "functionInfo": null, "fieldType": "DIMENSION", "fieldName": null, "resourceType": "REPORT", "componentType": null, "values": null, "parentId": null, "dateShowFormat": null, "valueList": null, "isHide": false, "showSubtotal": false, "positionIndex": 0, "computeField": false}], "indexColumnList": [{"id": 13899, "dateGroupType": null, "calculateLogic": "compute_kdjncfll_57396646", "computeFieldLogic": null, "uuid": "report2E7F1D59-6911-46DE-94FB-90744C756E2B", "configUuid": "config_ece94af8-b3bd-4361-984b-1032124f3489", "typeName": null, "originEnName": null, "name": "计算字段-度量-有内部聚合逻辑", "indexName": "compute_kdjncfll_57396646_indexno", "enName": "compute_kdjncfll_57396646", "originalName": "计算字段-度量-有内部聚合逻辑", "showTypeName": "DECIMAL", "fun": "sum([成功授信金额])", "isFold": null, "reportField": "index", "highFunctions": [], "functionInfo": null, "fieldType": "MEASURE", "fieldName": null, "resourceType": "REPORT", "componentType": null, "values": null, "parentId": null, "nickName": "计算字段-度量-有内部聚合逻辑", "polymerization": "no", "advancedComputing": null, "dataType": "数值", "decimaCarry": 0, "unit": 1, "isShowUnit": true, "showThousandth": false, "valueList": null, "isHide": false, "computeField": false}, {"id": 2683, "dateGroupType": null, "calculateLogic": "sum(toFloat64OrZero(toNullable( toString( succe_crdt_lmt ) )))", "computeFieldLogic": null, "uuid": "report02BACF5D-B636-4CFB-8A4C-83ECACFE076C", "configUuid": "config_ec7097ad-528a-4c16-89ff-4cb3aab746bc", "typeName": null, "originEnName": null, "name": "成功授信金额", "indexName": "toFloat64OrZero(toNullable( toString( succe_crdt_lmt ) ))_indexsum", "enName": "toFloat64OrZero(toNullable( toString( succe_crdt_lmt ) ))", "originalName": "成功授信金额", "showTypeName": "DECIMAL", "fun": null, "isFold": null, "reportField": "index", "highFunctions": [], "functionInfo": null, "fieldType": "MEASURE", "fieldName": null, "resourceType": "REPORT", "componentType": null, "values": null, "parentId": null, "nickName": "成功授信金额", "polymerization": "sum", "advancedComputing": null, "dataType": "数值", "decimaCarry": 0, "unit": 1, "isShowUnit": true, "showThousandth": false, "valueList": null, "isHide": false, "computeField": false}, {"id": 61392202, "dateGroupType": 1, "calculateLogic": "sum([成功授信金额])", "computeFieldLogic": null, "uuid": "report2538F3F2-3D53-4EE1-8574-2D6356E62F10", "configUuid": "config_ea3713ca-2165-4a8c-bd76-3cf197680e86", "typeName": null, "originEnName": null, "name": "test_计算字段", "indexName": "compute_emgnbgij_51785489_indexsum", "enName": "compute_emgnbgij_51785489", "originalName": null, "showTypeName": "DECIMAL", "fun": "[成功授信金额]", "isFold": null, "reportField": "index", "highFunctions": [], "functionInfo": null, "fieldType": "MEASURE", "fieldName": null, "resourceType": "REPORT", "componentType": null, "values": null, "parentId": null, "nickName": "test_计算字段", "polymerization": "sum", "advancedComputing": null, "dataType": "数值", "decimaCarry": 0, "unit": 1, "isShowUnit": true, "showThousandth": false, "valueList": null, "isHide": false, "computeField": false}], "filterColumnList": [], "conditionList": [], "keywordList": [], "datasetInfoList": [{"datasetId": 162, "dataSourceTypeCode": 0, "datasetName": null, "dataSourceName": "ck_2", "dataBaseName": "hive_36_app", "tableName": "app_ck_lsjr_exter_cha_flow_monitor", "partitionName": null, "updateMethod": null, "dataSourceType": "clickhouse", "dataDate": "day_id", "name": "测试ck数据集", "sqlEngine": "CLICKHOUSE"}], "orgName": "大数据与人工智能研究院", "tableFontStyle": {"tableDataStyle": {"fillColor": "", "fontSize": 12, "fontFillColor": "rgb(96, 98, 102)", "bold": false, "italic": false, "underline": false, "strikethrough": false}, "rowHeaderStyle": {"fillColor": null, "fontSize": 12, "fontFillColor": "rgb(96, 98, 102)", "bold": false, "italic": false, "underline": false, "strikethrough": false, "columnsNum": null, "freezeRowHeader": false}, "columnHeaderStyle": {"fillColor": null, "fontSize": 12, "fontFillColor": "rgb(96, 98, 102)", "bold": false, "italic": false, "underline": false, "strikethrough": false, "rowHeight": null, "showColumnHeader": true}, "resourceType": null, "componentType": null}, "coordinateAxisConfigRequest": null, "resourceType": "REPORT", "componentType": null, "watermarkSetting": {"showWatermark": false, "fontSize": 12, "fontColor": "rgb(245, 245, 245)"}, "cardStyleConfigRes": null, "graphicDisplayDTO": null}