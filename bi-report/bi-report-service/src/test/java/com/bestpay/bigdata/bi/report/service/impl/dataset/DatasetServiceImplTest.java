package com.bestpay.bigdata.bi.report.service.impl.dataset;

import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.Dimension;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.DataSetFieldTypeEnum;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetComputeComponentDO;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetConfigDo;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetParamDo;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetConfigDTO;
import com.bestpay.bigdata.bi.report.request.dataset.ComputeColumnPropertyRequest;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetPublishRequest;
import org.junit.Before;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DatasetServiceImpl单元测试类
 * 主要测试UUID生成和数据转换功能
 */
@ExtendWith(MockitoExtension.class)
class DatasetServiceImplTest {


    private MockedStatic<UserContextUtil> mockedUserContextUtil;

    @InjectMocks
    private DatasetServiceImpl datasetService;

    private UserInfo testUser;
    private String testDatasetCode;

    @Before
    public void setUp() {
        testUser = new UserInfo();
        testUser.setEmail("<EMAIL>");
        testDatasetCode = "DS001";
    }


    /**
     * 测试generateConfigDos方法的UUID生成规则
     */
    @Test
    void testGenerateConfigDos_ShouldGenerateUuidWithCorrectFormat() {
        // Given
        DatasetPublishRequest request = new DatasetPublishRequest();
        request.setCode(testDatasetCode);

        DatasetConfigDTO configDTO = new DatasetConfigDTO();
        configDTO.setFieldId(1L);
        configDTO.setLogicEnName("test_field");
        configDTO.setLogicCnName("测试字段");
        request.setConfigList(Arrays.asList(configDTO));

        // When
        List<DatasetConfigDo> result = datasetService.generateConfigDos(request, testUser);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        DatasetConfigDo configDo = result.get(0);
        assertNotNull(configDo.getUuid());
        assertTrue(configDo.getUuid().startsWith(testDatasetCode + "_config_"));

        // 验证UUID格式：应该是 {dataset_code}_config_{uuid}
        String[] parts = configDo.getUuid().split("_config_");
        assertEquals(2, parts.length);
        assertEquals(testDatasetCode, parts[0]);
        assertNotNull(UUID.fromString(parts[1])); // 验证第二部分是有效的UUID
    }

    /**
     * 测试generateParamDos方法的UUID生成规则
     */
    @Test
    void testGenerateParamDos_ShouldGenerateUuidWithCorrectFormat() {
        // Given
        DatasetPublishRequest request = new DatasetPublishRequest();
        request.setCode(testDatasetCode);

        // 创建参数DTO
        com.bestpay.bigdata.bi.common.dto.dataset.DatasetParamDTO paramDTO =
                new com.bestpay.bigdata.bi.common.dto.dataset.DatasetParamDTO();
        paramDTO.setCnName("测试参数");
        paramDTO.setEnName("test_param");
        paramDTO.setValues(Arrays.asList("value1", "value2"));
        request.setParamList(Arrays.asList(paramDTO));

        // When
        List<DatasetParamDo> result = datasetService.generateParamDos(request, testUser);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        DatasetParamDo paramDo = result.get(0);
        assertNotNull(paramDo.getUuid());
        assertTrue(paramDo.getUuid().startsWith(testDatasetCode + "_param_"));

        // 验证UUID格式：应该是 {dataset_code}_param_{uuid}
        String[] parts = paramDo.getUuid().split("_param_");
        assertEquals(2, parts.length);
        assertEquals(testDatasetCode, parts[0]);
        assertNotNull(UUID.fromString(parts[1])); // 验证第二部分是有效的UUID
    }

    /**
     * 测试generateComputeColumnDos方法的UUID生成规则
     */
    @Test
    void testGenerateComputeColumnDos_ShouldGenerateUuidWithCorrectFormat() {
        // Given
        ComputeColumnPropertyRequest computeRequest = new ComputeColumnPropertyRequest();
        computeRequest.setEnName("compute_field");
        computeRequest.setName("计算字段");
        computeRequest.setFieldType("MEASURE");
        List<ComputeColumnPropertyRequest> computeColumnList = Arrays.asList(computeRequest);

        // When
        List<DatasetComputeComponentDO> result = datasetService.generateComputeColumnDos(
                computeColumnList, testDatasetCode, testUser);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        DatasetComputeComponentDO computeDo = result.get(0);
        assertNotNull(computeDo.getUuid());
        assertTrue(computeDo.getUuid().startsWith(testDatasetCode + "_compute_"));

        // 验证UUID格式：应该是 {dataset_code}_compute_{uuid}
        String[] parts = computeDo.getUuid().split("_compute_");
        assertEquals(2, parts.length);
        assertEquals(testDatasetCode, parts[0]);
        assertNotNull(UUID.fromString(parts[1])); // 验证第二部分是有效的UUID
    }

    /**
     * 测试Dimension.getDatasetColumnConfigDTO方法的UUID传递
     */
    @Test
    void testDimensionGetDatasetColumnConfigDTO_ShouldTransferUuid() {
        // Given
        String testUuid = "DS001_config_" + UUID.randomUUID().toString();
        Dimension dimension = new Dimension();
        dimension.setId(1L);
        dimension.setName("测试维度");
        dimension.setEnName("test_dimension");
        dimension.setFieldType(DataSetFieldTypeEnum.DIMENSION);
        dimension.setUuid(testUuid);

        // When
        DatasetColumnConfigDTO result = dimension.getDatasetColumnConfigDTO(dimension);

        // Then
        assertNotNull(result);
        assertEquals(testUuid, result.getUuid());
        assertEquals(dimension.getName(), result.getName());
        assertEquals(dimension.getEnName(), result.getEnName());
    }

    /**
     * 测试DatasetParamDo.getDatasetColumnConfigDTO方法的UUID传递
     */
    @Test
    void testDatasetParamDoGetDatasetColumnConfigDTO_ShouldTransferUuid() {
        // Given
        String testUuid = "DS001_param_" + UUID.randomUUID().toString();
        DatasetParamDo paramDo = new DatasetParamDo();
        paramDo.setId(1L);
        paramDo.setCnName("测试参数");
        paramDo.setEnName("test_param");
        paramDo.setUuid(testUuid);
        paramDo.setValueList("[\"value1\", \"value2\"]");
        paramDo.setDefaultValue("value1");

        Long datasetId = 100L;

        // When
        DatasetColumnConfigDTO result = paramDo.getDatasetColumnConfigDTO(datasetId, paramDo);

        // Then
        assertNotNull(result);
        assertEquals(testUuid, result.getUuid());
        assertEquals(datasetId, result.getDatasetId());
        assertEquals(paramDo.getCnName(), result.getName());
        assertEquals(DataSetFieldTypeEnum.PARAM, result.getFieldType());
    }

    /**
     * 测试coverDoToDTO方法的UUID传递
     */
    @Test
    void testCoverDoToDTO_ShouldTransferUuid() {
        // Given
        String testUuid = "DS001_compute_" + UUID.randomUUID().toString();
        DatasetComputeComponentDO computeDo = new DatasetComputeComponentDO();
        computeDo.setId(1L);
        computeDo.setName("计算字段");
        computeDo.setEnName("compute_field");
        computeDo.setFieldType("MEASURE");
        computeDo.setUuid(testUuid);
        computeDo.setFun("SUM(amount)");
        computeDo.setIndexName("总金额");

        List<DatasetComputeComponentDO> computeList = Arrays.asList(computeDo);
        Long datasetId = 100L;

        // When
        List<DatasetColumnConfigDTO> result = datasetService.coverDoToDTO(computeList, datasetId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        DatasetColumnConfigDTO configDTO = result.get(0);
        assertEquals(testUuid, configDTO.getUuid());
        assertEquals(datasetId, configDTO.getDatasetId());
        assertEquals(computeDo.getName(), configDTO.getName());
        assertEquals(DataSetFieldTypeEnum.valueOf(computeDo.getFieldType()), configDTO.getFieldType());
        assertTrue(configDTO.getIsComputeField());
    }
}
