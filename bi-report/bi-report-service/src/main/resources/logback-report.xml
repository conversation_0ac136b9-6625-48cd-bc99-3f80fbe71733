<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 系统日志打印 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <Pattern>[%date] [%-5level] [%t] [%logger{36}.%method][%line] [%X{Trace-Log-Id}] %replace(%msg){'[\r\n]+', '####'} %replace(%ex){'[\r\n]+', '####'}%nopex%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 系统日志打印 -->
    <appender name="logFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${user.home}/bi-logs/bi-report.log</File>
        <encoder>
            <Pattern>[%date] [%-5level] [%t] [%logger{36}.%method][%line] [%X{Trace-Log-Id}] %replace(%msg){'[\r\n]+', '####'} %replace(%ex){'[\r\n]+', '####'}%nopex%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${user.home}/bi-logs/info/bi-report.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxHistory>30</maxHistory>
            <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>512MB</maxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <appender name="async" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>102400</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="logFile" />
    </appender>
    <!-- 输出到文件，可定义更多的 Appender -->
    <root level="INFO" >
        <appender-ref ref="console" />
        <!--异步输出日志-->
        <appender-ref ref="async"/>
    </root>
</configuration>