package com.bestpay.bigdata.bi.report.refactor.service.impl;

import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.refactor.processor.builder.SceneRequestBuilder;
import com.bestpay.bigdata.bi.report.refactor.service.RequestBuilderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RequestBuilderServiceImpl implements RequestBuilderService {

    private final List<SceneRequestBuilder> sceneRequestBuilders;
    private Map<RequestKey, SceneRequestBuilder> requestBuilderMap;

    @Autowired
    public RequestBuilderServiceImpl(List<SceneRequestBuilder> sceneRequestBuilders) {
        this.sceneRequestBuilders = sceneRequestBuilders;
    }

    @PostConstruct
    public void init() {
        requestBuilderMap = sceneRequestBuilders.stream()
                .collect(Collectors.toMap(SceneRequestBuilder::supportedKey, Function.identity()));
        log.info("Initialized {} scene requestBuilders.", requestBuilderMap.size());
    }

    @Override
    public void build(ProcessContext context) {
        ProcessorType processorType = context.getProcessorType();
        SceneType sceneType = context.getSceneType();

        RequestKey key = new RequestKey(sceneType, processorType);
        log.info("Dispatching requestBuilder for key: {}", key);

        SceneRequestBuilder builder = Optional.ofNullable(requestBuilderMap.get(key))
                .orElseThrow(() -> new IllegalStateException("No request builder found for key: " + key));

        builder.build(context);
    }
}
