package com.bestpay.bigdata.bi.report.refactor.processor.builder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.report.QueryReportConditionInfo;
import com.bestpay.bigdata.bi.common.dto.report.TotalDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ContrastComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.DimensionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.report.bean.report.DownloadColumn;
import com.bestpay.bigdata.bi.report.beforeSQL.QueryParamHandlerService;
import com.bestpay.bigdata.bi.report.cache.CacheHandleFactory;
import com.bestpay.bigdata.bi.report.cache.CommonCacheHandler;
import com.bestpay.bigdata.bi.report.cache.bean.DatasetCacheBean;
import com.bestpay.bigdata.bi.report.cache.enums.SceneEnums;
import com.bestpay.bigdata.bi.report.enums.report.ReportFieldEnum;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.correct.IReportDataCorrect;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetColumnConfigRequest;
import com.bestpay.bigdata.bi.report.request.report.DownloadApplyRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.bestpay.bigdata.bi.report.util.ReportUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.bestpay.bigdata.bi.report.constant.CommonConstant.measureName;
import static com.bestpay.bigdata.bi.report.constant.CommonConstant.measureValue;

@Component
@Slf4j
public abstract class AbstractSceneRequestBuilder implements SceneRequestBuilder {

    @Autowired
    private QueryParamHandlerService queryParamHandlerService;

    @Autowired
    private IReportDataCorrect reportDataCorrect;

    @Autowired
    private CacheHandleFactory cacheHandleFactory;

    @Autowired
    protected DatasetService datasetService;

    @Override
    public final void build(ProcessContext context) {

        // 前置处理下载选择字段
        preHandleDownloadChooseColumn(context);

        // 封装reportRequest
        buildReportRequest(context);

        // 处理参数
        handleParam(context);

        // 处理行列转置字段
        handleTranspositionParam(context);

        // 处理对比字段
        handleContrastParam(context);

        // 后置处理下载选择字段
        postHandleDownloadChooseColumn(context);

        // 数据校验及订正
        handleDataCorrect(context);
    }

    protected void preHandleDownloadChooseColumn(ProcessContext context) {};

    protected abstract void buildReportRequest(ProcessContext context);

    protected void handleParam(ProcessContext context) {
        ReportRequest reportRequest = context.getReportRequest();
        queryParamHandlerService.handlerParam(reportRequest);
    }

    protected void handleDataCorrect(ProcessContext context) {
        ReportRequest reportRequest = context.getReportRequest();
        List<DatasetColumnConfigDTO> datasetColumnConfigList = context.getDatasetColumnConfigList();
        List<QueryReportConditionInfo> queryConditions = reportRequest.getQueryConditions();
        reportDataCorrect.reportDataCorrect(reportRequest, queryConditions, datasetColumnConfigList);
    };

    protected void handleTranspositionParam(ProcessContext context) {
        queryParamHandlerService.moveContrastPosition(context.getReportRequest());
    };

    protected void handleContrastParam(ProcessContext context) {
        ReportRequest reportRequest = context.getReportRequest();

        final List<ContrastComponentPropertyDTO> contrastColumnList = reportRequest.getContrastColumnList();
        final List<DimensionComponentPropertyDTO> showColumnList = reportRequest.getShowColumnList();

        // 检查是否开启了小计功能
        boolean isSubtotalEnabled = showColumnList.stream().anyMatch(DimensionComponentPropertyDTO::getShowSubtotal);

        // 解析总计配置
        final TotalDTO totalDTO = ReportUtil.parseShowTotal(reportRequest.getShowIndexTotalObj());
        final boolean hasSubTotalConfig = !"no".equals(totalDTO.getSubTotal());

        // 对比小计场景
        if (contrastColumnList.size() > 1 && isSubtotalEnabled && hasSubTotalConfig) {
            log.info("Contrast-subtotal scenario. Creating a modified report request...");

            // 深拷贝
            ReportRequest newReport = JSONUtil.toBean(JSONUtil.toJsonStr(reportRequest), ReportRequest.class);

            // 对 showColumnList 进行深度拷贝，在副本上进行修改
            List<DimensionComponentPropertyDTO> newShowColumnList = JSONUtil.toList(JSONUtil.toJsonStr(showColumnList), DimensionComponentPropertyDTO.class);
            newShowColumnList.forEach(col -> col.setShowSubtotal(false));

            // 将修改后的列表设置到新的报表请求中
            newReport.setShowColumnList(newShowColumnList);

            context.setReportRequest(newReport);
        }
    };

    protected void postHandleDownloadChooseColumn(ProcessContext context) {};

    /**
     * 查询数据集信息
     *
     * @param datasetId
     * @return
     */
    public List<DatasetColumnConfigDTO> getDatasetColumnConfig(Long datasetId) {
        DatasetColumnConfigRequest configRequest = new DatasetColumnConfigRequest();
        configRequest.setDatasetId(datasetId);

        CommonCacheHandler handleStrategy = cacheHandleFactory.getHandleStrategy(SceneEnums.DATA_SET);
        DatasetCacheBean<DatasetColumnConfigRequest, Response<List<DatasetColumnConfigDTO>>> cacheBean =
                DatasetCacheBean.<DatasetColumnConfigRequest, Response<List<DatasetColumnConfigDTO>>>builder()
                        .param(configRequest)
                        .loader(req -> datasetService.getColumnConfigList(req))
                        .loaderName(datasetService.getClass().getName() + "getColumnConfigList")
                        .cacheKey(String.valueOf(configRequest.getDatasetId()))
                        .build();
        Response<List<DatasetColumnConfigDTO>> columnConfigList = (Response<List<DatasetColumnConfigDTO>>) handleStrategy.getCacheData(cacheBean);

        return columnConfigList.getData();
    }

    /**
     * 对下载字段按照维度度量重新排序
     *
     * @param downloadApplyRequest
     * @param dbreport
     */
    public void sortChooseColumnList(DownloadApplyRequest downloadApplyRequest, Report dbreport) {
        if (CollUtil.isEmpty(downloadApplyRequest.getChooseColumnList())) {
            return;
        }
        List<DownloadColumn> downloadColumns = downloadApplyRequest.getChooseColumnList();

        // 维度
        List<DimensionComponentPropertyDTO> showColumnList
                = JSONUtil.toList(dbreport.getShowColumn(), DimensionComponentPropertyDTO.class);

        // 度量
        List<IndexComponentPropertyDTO> indexColumnList
                = JSONUtil.toList(dbreport.getIndexColumn(), IndexComponentPropertyDTO.class);

        // 排序
        List<DownloadColumn> sortedDownloadColumns = Lists.newArrayList();
        for (DimensionComponentPropertyDTO showColumn : showColumnList) {
            for (DownloadColumn downloadColumn : downloadColumns) {
                if (StringUtils.isNotBlank(downloadColumn.getConfigUuid())
                        && downloadColumn.getConfigUuid().equals(showColumn.getConfigUuid())) {
                    sortedDownloadColumns.add(downloadColumn);
                }
            }
        }

        // 判断是否是转置的情况
        Boolean isDataTransposition = showColumnList.stream()
                .anyMatch(element -> element.getReportField().equals(ReportFieldEnum.MEASURE.getCode()));

        if (BooleanUtil.isTrue(isDataTransposition)) {
            // 转置情况：支持度量名和度量值可选隐藏
            sortedDownloadColumns.addAll(downloadColumns.stream()
                    .filter(p -> p.getEnName().equals(measureName) || p.getEnName().equals(measureValue))
                    .collect(Collectors.toList()));

            // 转置情况，下载勾选了度量值，将指标加入，做敏感校验
            List<DownloadColumn> measureValueList = downloadColumns.stream()
                    .filter(p -> p.getEnName().equals(measureValue))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(measureValueList)) {
                for (IndexComponentPropertyDTO index : indexColumnList) {
                    DownloadColumn downloadColumn = new DownloadColumn();
                    downloadColumn.setUuid(index.getUuid());
                    downloadColumn.setConfigUuid(index.getConfigUuid());
                    downloadColumn.setId(index.getId());
                    downloadColumn.setLabel(index.getName());
                    downloadColumn.setEnName(index.getEnName());
                    sortedDownloadColumns.add(downloadColumn);
                }
            }
        } else {
            // 非转置情况下：需要按照指标进行隐藏
            for (IndexComponentPropertyDTO index : indexColumnList) {
                for (DownloadColumn downloadColumn : downloadColumns) {
                    if (StringUtils.isNotBlank(downloadColumn.getConfigUuid())
                            && downloadColumn.getConfigUuid().equals(index.getConfigUuid())) {
                        sortedDownloadColumns.add(downloadColumn);
                    }
                }
            }
        }

        downloadApplyRequest.setChooseColumnList(sortedDownloadColumns);
    }

    public void markUnChooseColumnAsHidden(DownloadApplyRequest downloadApplyRequest, ReportRequest oldReport) {
        // 获取选择的字段 configUuidList
        List<DownloadColumn> chooseColumnList = downloadApplyRequest.getChooseColumnList();
        if (CollUtil.isNotEmpty(chooseColumnList)) {
            Set<String> chooseColumnConfigUuidSet = chooseColumnList.stream()
                    .map(DownloadColumn::getConfigUuid)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            // 判断是否选了 measureName
            boolean hasChosenMeasure = chooseColumnList.stream()
                    .anyMatch(p -> measureName.equalsIgnoreCase(p.getEnName()));

            // 隐藏维度字段逻辑
            for (DimensionComponentPropertyDTO dim : oldReport.getShowColumnList()) {
                String uuid = dim.getConfigUuid();
                String enName = dim.getEnName();

                // 优先：维度字段是 measureName 且没选中 -> 隐藏
                if (measureName.equalsIgnoreCase(enName) && !hasChosenMeasure) {
                    dim.setIsHide(true);
                }
                // 否则：不在选择列表中 -> 隐藏
                else if (!chooseColumnConfigUuidSet.contains(uuid)) {
                    dim.setIsHide(true);
                }
            }

            // 隐藏指标字段逻辑
            for (IndexComponentPropertyDTO idx : oldReport.getIndexColumnList()) {
                if (!chooseColumnConfigUuidSet.contains(idx.getConfigUuid())) {
                    idx.setIsHide(true);
                }
            }
        }
    }
}
