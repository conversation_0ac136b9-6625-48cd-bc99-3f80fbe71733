package com.bestpay.bigdata.bi.report.controller.dataset;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetFileUploadTableVO;
import com.bestpay.bigdata.bi.report.bean.dataset.UploadCreateTableVO;
import com.bestpay.bigdata.bi.report.request.dataset.AuthorityDatabaseRequest;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetUploadListRequest;
import com.bestpay.bigdata.bi.report.service.dataset.FileUploadCreateTableService;
import java.util.List;
import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/biReport/fileUpload")
@Api(value = "数据集文件相关", tags = "数据集文件相关")
public class DatasetFileUploadController {

    @Resource
    private FileUploadCreateTableService uploadCreateTableService;

    @PostMapping("/list")
    @ApiOperation(httpMethod = "POST", value = "列表接口", notes = "列表接口")
    public Response<List<DatasetFileUploadTableVO>> uploadDatasetList(@RequestBody DatasetUploadListRequest listRequest) {
        return uploadCreateTableService.uploadDatasetList(listRequest);
    }


    @GetMapping("/delete/{id}")
    @ApiOperation(httpMethod = "GET", value = "删除", notes = "删除")
    public Response<Boolean> deleteUploadDataset(@PathVariable Long id) {
        return uploadCreateTableService.deleteUploadDataset(id);
    }

    @GetMapping("/detail/{id}")
    @ApiOperation(httpMethod = "GET", value = "详情", notes = "详情")
    public Response<UploadCreateTableVO> detail(@PathVariable Long id) {
        return uploadCreateTableService.detail(id);
    }


    @PostMapping("/authorityDatabase")
    @ApiOperation(httpMethod = "POST", value = "获取 ck 指定源名称下的库列表", notes = "获取 ck 指定源名称下的库列表")
    public Response<List<String>> getAuthorityDatabase(@RequestBody AuthorityDatabaseRequest request) {
        return Response.ok(uploadCreateTableService.getAuthorityDatabase(request));
    }

    @PostMapping("/submit")
    @ApiOperation(httpMethod = "POST", value = "提交", notes = "提交")
    public Response<String> submit(@RequestParam("createTableFile") MultipartFile createTableFile,
        @RequestParam("fileUploadRequest") String fileUploadRequest)
        throws Exception {
        return uploadCreateTableService.submit(createTableFile, fileUploadRequest);
    }

    @PostMapping("/update")
    @ApiOperation(httpMethod = "POST", value = "数据更新", notes = "数据更新")
    public Response<String> update(@RequestParam("id") Long id,
                                                 @RequestParam("taskType") String taskType,
                                                 @RequestParam(name = "file", required = false) MultipartFile file) {
        return uploadCreateTableService.updateData(id, taskType, file);
    }

}
