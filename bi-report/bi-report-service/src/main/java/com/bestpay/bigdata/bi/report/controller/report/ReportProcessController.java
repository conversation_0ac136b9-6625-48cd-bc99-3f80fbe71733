package com.bestpay.bigdata.bi.report.controller.report;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.bean.AiPlusUserSearchRequest;
import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.common.dto.dashboard.DashBoardRelatedDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DataSet;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.report.bean.report.DownloadColumn;
import com.bestpay.bigdata.bi.report.chart.handler.util.ReportOrderTypeEnum;
import com.bestpay.bigdata.bi.report.check.computeColumn.ComputeFieldGrammarChecker;
import com.bestpay.bigdata.bi.report.check.computeColumn.bean.ComputeFieldCheckRequest;
import com.bestpay.bigdata.bi.report.request.report.DataSetRequest;
import com.bestpay.bigdata.bi.report.request.report.DimensionValueRequest;
import com.bestpay.bigdata.bi.report.request.report.DownloadColumnRequest;
import com.bestpay.bigdata.bi.report.request.report.RefreshReportRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.request.report.SecurityLevelRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;
import com.bestpay.bigdata.bi.report.service.common.EngineDataQueryService;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.bestpay.bigdata.bi.report.service.report.ReportProcessService;
import com.bestpay.bigdata.bi.report.usermanage.service.EmailUserManageService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import com.facebook.presto.jdbc.internal.guava.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 报表加工
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/biReport/reportProcess")
@Slf4j
@Api(value = "报表加工", tags = "报表加工")
public class ReportProcessController {

  @Resource
  private ReportProcessService reportProcessService;
  @Resource
  private DatasetService datasetService;
  @Resource
  private AuthorityCheckUtil authorityCheckUtil;
  @Resource
  private EngineDataQueryService engineDataQueryService;

  @Resource
  private ComputeFieldGrammarChecker computeFieldGrammarChecker;

  @Resource
  private EmailUserManageService emailUserManageService;

  @PostMapping("/getMaxSecurityLevel")
  @ApiOperation(value="查询数据安全等级", httpMethod="GET",response = Response.class)
  @ApiImplicitParam(paramType = "query",name = "id",value = "报表模板Id",required = true,dataType = "Long")
  public Response<String> getMaxSecurityLevel(@RequestBody SecurityLevelRequest request){
    return reportProcessService.getMaxSecurityLevel(request);
  }


  @PostMapping("/getDownloadColumn")
  @ApiOperation(value="查询报表所有列信息", httpMethod="GET",response = Response.class)
  @ApiImplicitParam(paramType = "query",name = "id",value = "报表模板Id",required = true,dataType = "Long")
  public Response<List<DownloadColumn>> getDownloadColumnList(@RequestBody DownloadColumnRequest request){
    return reportProcessService.getDownloadColumnList(request);
  }

  @PostMapping("/syncShowData")
  @ApiOperation(value="展示数据查询接口", httpMethod="POST",response = Response.class)
  public Response<QueryIndexAndReportResponse> syncShowData(@RequestBody RefreshReportRequest reportRequest) {

    ReportRequest report = getReportRequest(reportRequest);
    DashBoardRelatedDTO dto  = DashBoardRelatedDTO.builder()
        .type(reportRequest.getOwnershipType())
        .id(reportRequest.getOwnershipId()).build();

    if (Objects.nonNull(reportRequest.getChartType())
            && ChartTypeEnum.LIST_TABLE.getCode().equals(reportRequest.getChartType())) {
      if (Objects.isNull(reportRequest.getPageNum())
              && Objects.isNull(reportRequest.getPageSize())
              && StringUtil.isEmpty(reportRequest.getMaxRows())) {
        report.setMaxRows("1");
      }
    }


    if (Objects.isNull(reportRequest.getOrderType())) {
      reportRequest.setOrderType(ReportOrderTypeEnum.DEFAULT.getType());
    }

    return engineDataQueryService.getData(report,Boolean.TRUE,dto);
  }


  @PostMapping("/checkGrammar")
  @ApiOperation(value="校验计算字段语法", httpMethod="POST",response = Response.class)
  public Response computeFieldGrammarChecker(@RequestBody ComputeFieldCheckRequest request) {
    computeFieldGrammarChecker.grammarCheck(request);
    return Response.ok();
  }


  @ApiOperation(value = "智加用户", httpMethod = "GET", response = Response.class)
  @PostMapping("/getModifyOwnerNameLists")
  public Response<List<UserInfo>> getModifyOwnerNameLists(
      @RequestBody AiPlusUserSearchRequest searchRequest) {
    return Response.ok(emailUserManageService.getUserList(searchRequest));
  }

  @PostMapping("/querySelectValue")
  @ApiOperation(value="查询字符型复选值接口", httpMethod="POST",response = Response.class)
  public Response<QueryIndexAndReportResponse> querySelectValue(@RequestBody @Valid DimensionValueRequest dimensionValueRequest) {

    //支持未登录的场景
    return reportProcessService.querySelectValue(dimensionValueRequest);
  }

  @PostMapping("/notFilterRowAuth/querySelectValue")
  @ApiOperation(value="查询字符型复选值接口~不过滤行级权限", httpMethod="POST",response = Response.class)
  public Response<QueryIndexAndReportResponse> querySelectValueNotFilterRowAuth(@RequestBody @Valid DimensionValueRequest dimensionValueRequest) {
    //只针对于此接口，不做行级权限管控
    dimensionValueRequest.setFilterRowAuth(Boolean.FALSE);
    //支持未登录的场景
    return reportProcessService.querySelectValue(dimensionValueRequest);
  }


  @PostMapping("/getDataSetList")
  @ApiOperation(httpMethod = "POST", value = "数据集列表", notes = "数据集列表")
  public Response<List<DataSet>> getDataSetList(@RequestBody DataSetRequest dataSetRequest){
    return datasetService.getDataSetList(dataSetRequest);
  }

  private ReportRequest getReportRequest(RefreshReportRequest reportRequest) {
    // 数据集信息
    List<DatasetInfo> datasets = reportRequest.getDatasetInfoList();

    List<DataSet> dataSets
        = datasetService.getDataSet(Lists.newArrayList(datasets.get(0).getDatasetId()));

    DataSet dataSet = dataSets.get(0);
    authorityCheckUtil.checkAuthOrg(dataSet.getOrgAuth());

    List<DatasetInfo> infos = dataSets.stream().map(p->{
      DatasetInfo info = new DatasetInfo();
      BeanUtils.copyProperties(p, info);
      return info;
    }).collect(Collectors.toList());

    reportRequest.setDatasetInfoList(infos);

    ReportRequest report
        = JSONUtil.toBean(JSONUtil.toJsonStr(reportRequest), ReportRequest.class);

    return report;
  }

}
