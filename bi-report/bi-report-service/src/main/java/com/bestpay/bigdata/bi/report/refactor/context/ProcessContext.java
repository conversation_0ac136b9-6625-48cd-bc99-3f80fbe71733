package com.bestpay.bigdata.bi.report.refactor.context;

import com.bestpay.bigdata.bi.common.dto.dashboard.DashBoardRelatedDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DataSet;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.chart.bean.ChartContext;
import com.bestpay.bigdata.bi.report.chart.bean.ChartResult;
import com.bestpay.bigdata.bi.common.refactor.bean.DataChunk;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import lombok.Data;

import java.util.Iterator;
import java.util.List;
import java.util.function.Consumer;


@Data
public class ProcessContext {

    private Object rawRequest;
    private ReportRequest reportRequest;
    private List<DatasetColumnConfigDTO> datasetColumnConfigList;
    private DashBoardRelatedDTO dashBoardRelated;
    private Iterator<DataChunk> dataChunkIterator;
    private ChartContext chartContext;
    private Consumer<ChartResult> resultConsumer;
    private UserInfo userInfo;
    private boolean isSubscribe;

    private ProcessorType processorType;
    private SceneType sceneType;
    private boolean success = true;
    private String errorInfo;
    private Response response;

    public void setErrorInfo(String errorInfo) {
        this.success = false;
        this.errorInfo = errorInfo;
    }

    private CrossChunkState crossChunkState = new CrossChunkState();

    @Data
    public static class CrossChunkState {

        private List<String> lastRowDimensionValues;
    }
}
