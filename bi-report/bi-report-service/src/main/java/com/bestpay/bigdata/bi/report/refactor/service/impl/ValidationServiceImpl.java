package com.bestpay.bigdata.bi.report.refactor.service.impl;

import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.refactor.processor.validator.SceneValidator;
import com.bestpay.bigdata.bi.report.refactor.service.ValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ValidationServiceImpl implements ValidationService {

    private final List<SceneValidator> sceneValidators;
    private Map<RequestKey, SceneValidator> validatorMap;

    @Autowired
    public ValidationServiceImpl(List<SceneValidator> sceneValidators) {
        this.sceneValidators = sceneValidators;
    }

    @PostConstruct
    public void init() {
        validatorMap = sceneValidators.stream()
                .collect(Collectors.toMap(SceneValidator::supportedKey, Function.identity()));
        log.info("Initialized {} scene validators.", validatorMap.size());
    }

    @Override
    public void validate(ProcessContext context) {
        ProcessorType processorType = context.getProcessorType();
        SceneType sceneType = context.getSceneType();

        RequestKey key = new RequestKey(sceneType, processorType);
        log.info("Dispatching validation for key: {}", key);

        SceneValidator validator = Optional.ofNullable(validatorMap.get(key))
                .orElseThrow(() -> new IllegalStateException("No validator found for key: " + key));

        validator.validate(context);
    }
}
