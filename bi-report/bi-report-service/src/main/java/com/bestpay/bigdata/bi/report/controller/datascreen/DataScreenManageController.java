package com.bestpay.bigdata.bi.report.controller.datascreen;

import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.DataScreenStatusEnum;
import com.bestpay.bigdata.bi.common.error.DataScreenErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.AssertUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.dao.datascreen.DataScreenMainDO;
import com.bestpay.bigdata.bi.report.request.datascreen.DataScreenPreviewRequest;
import com.bestpay.bigdata.bi.report.request.datascreen.DataScreenRequest;
import com.bestpay.bigdata.bi.report.request.datascreen.DataScreenStatusRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.DataScreenPartakeRequest;
import com.bestpay.bigdata.bi.report.response.datascreen.DataScreenListVO;
import com.bestpay.bigdata.bi.report.service.datascreen.DataScreenManageService;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/biReport/datascreen/")
@Api(value = "数据大屏管理", tags = "数据大屏管理")
public class DataScreenManageController {

  @Resource
  private DataScreenManageService dataScreenManageService;

  /**
   * 数据大屏管理界面列表
   * @param request 请求参数
   * @return 列表信息
   */
  @PostMapping("/mange/list")
  @ApiOperation(httpMethod = "POST", value = "数据大屏管理界面列表", notes = "数据大屏管理界面列表")
  public Response<PageQueryVO<DataScreenListVO>> list(@RequestBody DataScreenRequest request) {
    Preconditions.checkArgument(Objects.nonNull(request.getPageNum()),"分页页数不能为空");
    Preconditions.checkArgument(Objects.nonNull(request.getPageSize()),"分页大小不能为空");
    return Response.ok(dataScreenManageService.list(request));
  }


  /**
   * 数据大屏管理界面   列表中  的发布
   * @param request 请求参数
   * @return 列表信息
   */
  @PostMapping("/mange/list/publish")
  @ApiOperation(httpMethod = "POST", value = "数据大屏管理界面列表中的发布", notes = "数据大屏管理界面列表中的发布")
  public Response<Boolean> publish(@RequestBody @Validated DataScreenPreviewRequest request) {
    verifyDSPermissions(request.getDataScreenId());
    return Response.ok(dataScreenManageService.publish(request)) ;
  }

  /**
   * 数据大屏 管理界面分享
   * @param request 请求参数
   * @return 分享的URL
   */
  @PostMapping("/mange/share")
  @ApiOperation(httpMethod = "POST", value = "数据大屏 管理界面分享", notes = "数据大屏 管理界面分享")
  public Response<String> share(@RequestBody @Validated DataScreenPartakeRequest request) {
    verifyDSPermissions( request.getDatsScreenId());
    return Response.ok(dataScreenManageService.share(request));
  }

  private void verifyDSPermissions(Long datsScreenId) {
    UserInfo userInfo = UserContextUtil.get();
    if (!userInfo.getIsManager()){
      // 说明是编辑权限  编辑权限需要校验责任人
      DataScreenMainDO mainDO = dataScreenManageService.queryMainDSById(datsScreenId);
      String createdBy = mainDO.getCreatedBy();
      if (!Objects.equals(userInfo.getAccount(),createdBy)){
        throw new BiException(DataScreenErrorCode.DATA_SCREEN_PERMISSIONS,"您暂无此操作权限");
      }
    }
  }

  /**
   * 数据大屏管理界面更新状态
   * @param request 请求参数
   * @return 更新成功标识
   */
  @PostMapping("/mange/updateStatus")
  @ApiOperation(httpMethod = "POST", value = "数据大屏管理界面更新状态", notes = "数据大屏管理界面更新状态")
  public Response<Boolean> updateStatus(@RequestBody @Validated DataScreenStatusRequest request) {
    Preconditions.checkArgument(DataScreenStatusEnum.OFFLINE_AND_DELETE.contains(request.getStatus()),"状态不存在");
    verifyDSPermissions(request.getDataScreenId());
    dataScreenManageService.updateStatus(request);
    return Response.ok();
  }

}
