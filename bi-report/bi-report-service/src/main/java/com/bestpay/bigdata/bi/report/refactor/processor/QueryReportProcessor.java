package com.bestpay.bigdata.bi.report.refactor.processor;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.chart.bean.ChartResult;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;


@Slf4j
@Component
public class QueryReportProcessor extends AbstractReportProcessor {

    @Override
    protected Consumer<ChartResult> defineResultConsumer(ProcessContext context) {
        return chartResult -> {
            log.info("[Query] Consuming the first and only data chunk.");

            // 1. 将第一块处理好的数据存入 context
            Response<Object> response = Response.ok();
            context.setResponse(response);
        };
    }

    @Override
    public ProcessorType getProcessorType() {
        return ProcessorType.QUERY;
    }
}
