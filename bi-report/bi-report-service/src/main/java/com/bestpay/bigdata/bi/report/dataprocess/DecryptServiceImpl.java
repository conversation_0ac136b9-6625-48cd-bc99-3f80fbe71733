package com.bestpay.bigdata.bi.report.dataprocess;

import com.alibaba.dubbo.config.annotation.Reference;
import com.bestpay.seccore.service.crypto.CryptoService;
import com.bestpay.sechsm.bean.HsmCipherService;
import com.bestpay.sechsm.service.crypto.DataCryptoService;
import com.bestpay.sechsm.service.crypto.KeyManageService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * ClassName: DecryptServiceImpl
 * Package: com.bestpay.bigdata.bi.report.dataprocess
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/12/12 16:20
 * @Version 1.0
 */
@Component
@ConditionalOnProperty(value = "bi.decrypt.mode", havingValue = "NORMAL",matchIfMissing = true)
public class DecryptServiceImpl implements DecryptService
{
    @Reference
    private DataCryptoService dataCryptoService;

    @Reference
    private KeyManageService keyManageService;

    @Reference
    private CryptoService cryptoService;

    private static HsmCipherService hsmCipherService = new HsmCipherService();

    @PostConstruct
    public HsmCipherService init() {
        hsmCipherService.setMainType("APPDATA");
        hsmCipherService.setDataCryptoService(dataCryptoService);
        hsmCipherService.setKeyManageService(keyManageService);
        hsmCipherService.setCryptoService(cryptoService);
        hsmCipherService.setAppId("2");
        hsmCipherService.setMainCode("0001");
        hsmCipherService.initService();
        return hsmCipherService;
    }

    public String encrypt(String data) {
        return hsmCipherService.encrypt(data);
    }

    public String decrypt(String data) {
        return hsmCipherService.decrypt(data);
    }
}
