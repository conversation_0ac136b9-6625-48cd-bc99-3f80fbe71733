package com.bestpay.bigdata.bi.report.controller.usergroupmanage;


import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupUserRelationRequest;
import com.bestpay.bigdata.bi.report.usermanage.service.UserGroupUserRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 用户组用户关系
 * @author: lm
 */
@RestController
@RequestMapping("/biReport/user/group/relation")
@Api(value = "用户组用户关系", tags = "用户组用户关系")
public class UserGroupUserRelationController {

    @Resource
    private UserGroupUserRelationService userGroupUserRelationService;

    /**
     *   新增用户组批量绑定用户关系
     */
    @ApiOperation(httpMethod = "POST",value = "新增用户组批量绑定用户关系", notes = "新增用户组批量绑定用户关系")
    @PostMapping("/batch/add")
    public Response addUserGroupUserRelation(@RequestBody @Validated UserGroupUserRelationRequest request) {
        userGroupUserRelationService.addUserGroupUserRelation(request);
        return Response.ok();
    }
}
