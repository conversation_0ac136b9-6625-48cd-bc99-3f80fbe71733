//package com.bestpay.bigdata.bi.report.correction;
//
//import cn.hutool.json.JSONUtil;
//import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
//import com.bestpay.bigdata.bi.common.dto.report.ColumnProperty;
//import com.bestpay.bigdata.bi.common.dto.report.TotalDTO;
//import com.bestpay.bigdata.bi.common.response.Response;
//import com.bestpay.bigdata.bi.database.api.report.ReportService;
//import com.bestpay.bigdata.bi.database.bean.report.Report;
//import com.bestpay.bigdata.bi.database.bean.report.ReportQueryRequest;
//import java.io.Serializable;
//import java.util.List;
//import java.util.Optional;
//import javax.annotation.Resource;
//import javax.sql.DataSource;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.jdbc.datasource.DriverManagerDataSource;
//import org.springframework.stereotype.Component;
//
///**
// * ClassName: ReportTotalReversion
// * Package: com.bestpay.bigdata.bi.report.controller.dataset.correction
// * Description:
// *
// * <AUTHOR>
// * @Create 2023/11/14 9:54
// * @Version 1.0
// */
//@Slf4j
//@Component
//public class ReportTotalReversion {
//
//    @Resource
//    private ApolloRefreshConfig apolloRefreshConfig;
//
//    @Resource
//    private ReportService reportService;
//
//    public Response reportReversion() {
//        JdbcTemplate biJdbcTemplate = new JdbcTemplate();
//        biJdbcTemplate.setDataSource(biDataSource());
//
//        List<Report> reportList = reportService.getReportLists(new ReportQueryRequest());
//
////        List<Report> reportList = biJdbcTemplate.query("select * from t_report where status_code !=9", new BeanPropertyRowMapper<>(Report.class));
//        log.info("query all t_report records from bi, size :{}", reportList.size());
//
//        for (Report report : reportList) {
//            TotalDTO dto = TotalDTO.builder().colTotal("no").rowTotal("no").subTotal("no").build();
//            if (report.getShowIndexTotal().equals("true") || report.getShowIndexTotal().equals("1")) {
//                dto.setColTotal("down");
//            }
//
//            List<ColumnProperty> showColumnList = JSONUtil.toList(report.getShowColumn(), ColumnProperty.class);
//            Optional<ColumnProperty> first = showColumnList.stream().filter(ColumnProperty::getShowSubtotal).findFirst();
//            if (first.isPresent()) {
//                dto.setSubTotal("down");
//            }
//
//            Report updateReport = new Report();
//            updateReport.setId(report.getId());
//            updateReport.setShowIndexTotal(JSONUtil.toJsonStr(dto));
//
//            reportService.update(updateReport);
//        }
//        return Response.ok();
//    }
//
//
//    private DataSource biDataSource() {
//        DriverManagerDataSource dataSource = new DriverManagerDataSource();
//
//        DataSourceConfig biMysqlConfig = JSONUtil.toBean(apolloRefreshConfig.getBiMysqlDatasource(), DataSourceConfig.class);
//
//        dataSource.setDriverClassName(biMysqlConfig.getDriveClass());
//        dataSource.setUrl(biMysqlConfig.getJdbcUrl());
//        dataSource.setUsername(biMysqlConfig.getUserName());
//        dataSource.setPassword(biMysqlConfig.getPassWord());
//
//        return dataSource;
//    }
//
//    @Data
//    class DataSourceConfig implements Serializable {
//        private String driveClass;
//        private String jdbcUrl;
//        private String userName;
//        private String passWord;
//        private String validationQuery;
//        private Integer maxTotal;
//        private Long maxWaitMillis;
//        private Boolean testOnBorrow;
//        private String jmxBeanName;
//    }
//
//}
