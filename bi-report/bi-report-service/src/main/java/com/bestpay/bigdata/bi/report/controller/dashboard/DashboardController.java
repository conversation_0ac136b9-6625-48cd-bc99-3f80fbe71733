package com.bestpay.bigdata.bi.report.controller.dashboard;

import static com.bestpay.bigdata.bi.common.constant.BIConstant.EMAIL;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dashboard.BgSetting;
import com.bestpay.bigdata.bi.common.dto.dashboard.DashboardGridSetting;
import com.bestpay.bigdata.bi.common.dto.dashboard.DashboardPageSettingModel;
import com.bestpay.bigdata.bi.common.dto.dashboard.WatermarkSetting;
import com.bestpay.bigdata.bi.common.entity.Org;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.AssertUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardConfigService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDirectoryDAOService;
import com.bestpay.bigdata.bi.database.bean.AuthOperateEnum;
import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashBoardConfigDO;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashboardQuery;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardCardDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardDirectoryDo;
import com.bestpay.bigdata.bi.report.enums.dashboard.DisplayTypeEnum;
import com.bestpay.bigdata.bi.report.request.auth.ObjectAuthRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashBoardCopyRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardAddRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardPublishRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.MoveDashboardRequest;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardDetailVO;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardDirectoryResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardResponse;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardVo;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardService;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardTableCardService;
import com.bestpay.bigdata.bi.report.usermanage.service.AiPlusUserManageService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import com.bestpay.bigdata.bi.thirdparty.api.DatasbpService;
import com.bestpay.bigdata.bi.thirdparty.dto.UserReportVO;
import com.bestpay.bigdata.bi.thirdparty.dto.UserReportsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 仪表板/数据大屏
 * 数据大屏没有目录
 */
@Slf4j
@RestController
@RequestMapping("/biReport/dashboard")
@Api(value = "仪表板相关", tags = "仪表板相关")
public class DashboardController {

    public static final String grid_default
        = "{\"rowInterval\":8,\"colInterval\":8,\"cardUnitHeight\":18,\"gridColNum\":24}";

    public static final String bgColorDefault
        = "{\"bgColor\":\"rgba(244, 244, 245, 1)\"}";

    public static final String watermarkSettingDefault
        = "{\"showWatermark\":false,\"fontSize\":12,\"fontColor\":\"rgb(245, 245, 245)\"}";

    @Resource
    private DashboardService dashboardService;
    @Resource
    private DashboardDirectoryDAOService directoryService;
    @Resource
    private AuthorityCheckUtil authorityCheckUtil;
    @Resource
    private DatasbpService datasbpService;
    @Resource
    private DashboardTableCardService tableCardService;
    @Resource
    private DashboardDaoService dashboardDaoService;
    @Resource
    private AiPlusUserManageService aiPlusUserManageService;
    @Resource
    private DashboardConfigService dashboardConfigService;
    @Resource
    private DashboardCardService cardService;



    /**
     * 仪表板/数据大屏新增接口
     * @param dashboard
     * @return
     */
    @PostMapping
    @Transactional(rollbackFor = Throwable.class)
    @ApiOperation(httpMethod = "POST", value = "保存仪表板", notes = "保存仪表板")
    public Response<Boolean> saveDashboard(@RequestBody DashboardAddRequest dashboard) {
        checkDashboardParameter(dashboard);

        dashboard.setCreatedBy(UserContextUtil.getUserInfo().getEmail());
        dashboard.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());

        Dashboard update = new Dashboard();
        BeanUtils.copyProperties(dashboard, update);

        Response<Long> result = dashboardService.insert(update);

        DashBoardConfigDO dashBoardConfigDO = new DashBoardConfigDO();
        dashBoardConfigDO.setDashboardId(result.getData());
        dashBoardConfigDO.setCreatedBy(UserContextUtil.getUserInfo().getEmail());
        dashBoardConfigDO.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
        dashBoardConfigDO.setUpdatedAt(new Date());
        dashBoardConfigDO.setCreatedAt(new Date());

        // 默认值
        dashBoardConfigDO.setBgSetting(bgColorDefault);
        dashBoardConfigDO.setWatermarkSetting(watermarkSettingDefault);
        dashBoardConfigDO.setGridSetting(grid_default);
        dashBoardConfigDO.setPageWidthSetting(1);

        dashboardConfigService.insert(dashBoardConfigDO);

        return Response.ok(true);
    }

    @PostMapping("copy")
    @ApiOperation(value="复制仪表板/数据大屏", response = Response.class)
    public Response<Long> copy(@RequestBody @Validated DashBoardCopyRequest request){
        // 权限校验
        checkOwnerAndAuth(request.getId());
        return Response.ok(dashboardService.copy(request));
    }
    @GetMapping
    @ApiOperation(value="仪表板/数据大屏", httpMethod="GET",response = Response.class)
    public Response<List<DashboardVo>> queryDashboard(DashboardQuery dashboardQuery,
        HttpServletRequest request) {
        log.debug("start to query dashboard,param:{}", JSONUtil.toJsonStr(dashboardQuery));

        return dashboardService.query(dashboardQuery, request);
    }



    @PostMapping("publish")
    @ApiOperation(value="仪表板发布", response = Response.class)
    public Response<Integer> publish(@RequestBody @Validated DashboardPublishRequest request){
        checkOwnerAndAuth(request.getId());
        return Response.ok(dashboardService.publish(request));
    }

    @GetMapping("offLine/{id}")
    @ApiOperation(value="仪表板下线", httpMethod="GET",response = Response.class)
    public Response<Integer> downLine(@PathVariable Long id){
        // 权限校验
        checkOwnerAndAuth(id);
        return Response.ok(dashboardService.offLine(id));
    }


    /**
     * 校验仅限责任人 ｜｜ 授权编辑 ｜｜ 管理权限
     * @param id ID
     */
    private void checkOwnerAndAuth(Long id ){
        ObjectAuthRequest request = new ObjectAuthRequest();
        request.setAuthResourceId(id+"");
        request.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());
        request.setAuthOperate(AuthOperateEnum.edit.name());
        authorityCheckUtil.checkOwnerAndAuth(request);
    }

    /**
     * 应用嵌入，嵌入对象为仪表板，嵌入对象列表
     * @param dashboardQuery
     * @return
     */
    @GetMapping("/justList")
    @ApiOperation(value="仪表板/数据大屏", httpMethod="GET",response = Response.class)
    public Response<List<DashboardVo>> queryDashboardList(DashboardQuery dashboardQuery) {
        log.debug("start to query dashboard,param:{}", JSONUtil.toJsonStr(dashboardQuery));
        return dashboardService.queryList(dashboardQuery);
    }


    @GetMapping("/{id}")
    @ApiOperation(value="仪表板/数据大屏详情", httpMethod="GET",response = Response.class)
    @ApiImplicitParam(paramType = "query",name = "id",value = "仪表板Id",required = true,dataType = "Long")
    public Response<DashboardDetailVO> queryDashboardDetail(@PathVariable Long id,
        @RequestParam(value="source",required = false) String source) {

        // 权限校验
        ObjectAuthRequest request = new ObjectAuthRequest();
        request.setAuthResourceId(id+"");
        request.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());
        authorityCheckUtil.checkOwnerAndAuth(request);

        // 清空缓存
        tableCardService.clearRedis(id);
        Dashboard dashboard = dashboardDaoService.getById(id);
        if(dashboard==null){
            return Response.ok();
        }

        Response<Dashboard> result= dashboardService.queryById(id,source);
        Dashboard dashboardDetail = result.getData();
        DashboardDetailVO detailVO = new DashboardDetailVO();
        BeanUtils.copyProperties(dashboardDetail, detailVO);

        // 组织中文名
        Optional<Org> orgIfPresent = aiPlusUserManageService.getOrgList().stream()
                .filter(org -> org.getCode().equalsIgnoreCase(detailVO.getOrgCode()))
                .findFirst();

        orgIfPresent.ifPresent(org -> detailVO.setOrgName(org.getName()));

        DashBoardConfigDO dashBoardConfigDO = dashboardConfigService.queryByDashboardId(id);
        if (Objects.nonNull(dashBoardConfigDO)){
            DashboardPageSettingModel build = DashboardPageSettingModel
                .builder()
                .pageWidthSetting(dashBoardConfigDO.getPageWidthSetting())
                .widthPx(dashBoardConfigDO.getWidthPx())
                .watermarkSetting(JSONUtil.toBean(dashBoardConfigDO.getWatermarkSetting(),
                    WatermarkSetting.class))
                .bgSetting(StrUtil.isNotBlank(dashBoardConfigDO.getBgSetting()) ? JSONUtil.toBean(
                    dashBoardConfigDO.getBgSetting(), BgSetting.class) : null)
                .gridSetting(
                    JSONUtil.toBean(dashBoardConfigDO.getGridSetting(), DashboardGridSetting.class))
                .build();

            detailVO.setPageSettingModel(build);
        }

        // 获取tab数量
        List<DashboardCardDO> cardDOS
            = cardService.findById(id, NewCardTypeEnum.TAB.getCode(), null);

        detailVO.setTabNum(CollUtil.isNotEmpty(cardDOS)?cardDOS.size():0L);
        Response response = Response.ok(detailVO);

        return response;
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value="仪表板/数据大屏删除", httpMethod="DELETE",response = Response.class)
    public Response<Boolean> deleteDashboard(@PathVariable Long id) {
        log.debug("start to delete dashboard id:{},email:{}",id,UserContextUtil.getUserInfo().getEmail());

        // 权限校验 -- 仅限制责任人才能删除
        Dashboard dbDashboard
            = dashboardDaoService.getById(id);
        AssertUtil.notNull(dbDashboard,CodeEnum.DASHBOARD_NOT_EXISTS);
        AssertUtil.isTrue(dbDashboard.getStatusCode() != StatusCodeEnum.ONLINE.getCode(),CodeEnum.DASHBOARD_PLEASE_DOWN_LINE);

        authorityCheckUtil.checkOwner(EMAIL, dbDashboard.getOwnerEmail());

        return dashboardService.deleteById(id,UserContextUtil.getUserInfo());
    }

    @PutMapping("/{id}")
    @ApiOperation(value="仪表板/数据大屏修改", httpMethod="PUT",response = Response.class)
    public Response<Boolean> updateDashboard(@PathVariable Long id,@RequestBody Dashboard dashboard) {
        log.debug("start to update dashboard id:{}",id);

        // 权限校验
        ObjectAuthRequest request = new ObjectAuthRequest();
        request.setAuthResourceId(dashboard.getId()+"");
        request.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());
        request.setAuthOperate(AuthOperateEnum.edit.name());
        authorityCheckUtil.checkOwnerAndAuth(request);

        if (dashboard.getDirId()!=null) {
            DashboardDirectoryDo directoryDo = directoryService.queryDbById(dashboard.getDirId());
            if(directoryDo == null) {
                log.warn("save dashboard failed. dashboard dirId:{} is illegal.",dashboard.getDirId());
                return Response.error("-1","仪表盘所属目录Id不存在");
            }
        }

        dashboard.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
        dashboard.setId(id);

        return dashboardService.updateById(dashboard);
    }

    /**
     * 仪表板位置移动， 从1目录到另一目录
     * @param moveDashboardRequest
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/move")
    @ApiOperation(httpMethod = "POST", value = "仪表板位置移动", notes = "仪表板位置移动")
    public Response<Boolean> moveDashboardLocation(@RequestBody  MoveDashboardRequest moveDashboardRequest,
                                                   HttpServletRequest httpServletRequest) {
        return dashboardService.moveDashboardLocation(moveDashboardRequest, httpServletRequest);
    }

    /**
     * 仪表板目录列表
     * @param dashboard
     * @return
     */
    @PostMapping("/getDashboardDirectoryList")
    @ApiOperation(httpMethod = "POST", value = "仪表板目录列表", notes = "仪表板目录列表")
    public Response<List<DashboardDirectoryResponse>> queryDashboardDirectoryList(@RequestBody Dashboard dashboard) {
        return dashboardService.queryDashboardDirectoryList(dashboard);
    }


    @PostMapping("/getDashboardListByDirId")
    @ApiOperation(httpMethod = "POST", value = "根据目录ID获取仪表板列表", notes = "根据目录ID获取仪表板列表")
    public Response<List<DashboardResponse>> queryDashboardListByDirId(@RequestBody Dashboard dashboard) {
        return dashboardService.queryDashboardListByDirId(dashboard);
    }

    @GetMapping("/getDataSbpUserReports")
    @ApiOperation(value="获取观星台报表列表", httpMethod="GET",response = Response.class)
    @ApiImplicitParam(paramType = "query",name = "name",value = "报表路径",required = true,dataType = "String")
    public Response<UserReportsVO> getDataSbpUserReports(@RequestParam("name") String name) {

        log.debug("name:{}",name);
        UserReportsVO userReportsVO=datasbpService.getDatasbpUserReports(name);
        if(userReportsVO!=null) {
            return Response.ok(userReportsVO);
        }else{
            return Response.error(CodeEnum.DASHBOARD_SBP_RESULT_NULL);
        }
    }

    @GetMapping("/getDataSbpReport/{id}")
    @ApiOperation(value="获取观星台地址", httpMethod="GET",response = Response.class)
    @ApiImplicitParam(paramType = "query",name = "id",value = "观星台报表Id",required = true,dataType = "Long")
    public Response<UserReportVO> getDataSbpReport(@PathVariable Integer id) {

        UserReportVO userReportVO=datasbpService.getDatasbpReport(id);
        if(userReportVO!=null) {
            return Response.ok(userReportVO);
        }else{
            return Response.error(CodeEnum.DASHBOARD_SBP_RESULT_NULL);
        }
    }

    /**
     * 检查仪表板/数据大屏参数
     * @param dashboard
     */
    private void checkDashboardParameter(DashboardAddRequest dashboard) {
        AssertUtil.notBlank(dashboard.getName(),CodeEnum.DASHBOARD_NAME_NOT_BLANK);

        if (Objects.equals(DisplayTypeEnum.DATA_BIG_SCREEN.getCode(), dashboard.getDisplayType())) {
            AssertUtil.isFalse(dashboard.getName().length()>50, CodeEnum.DASHBOARD_NAME_OVER_LENGTH);
        }

        if (Objects.equals(DisplayTypeEnum.DASHBOARD.getCode(), dashboard.getDisplayType())) {
            AssertUtil.notNull(dashboard.getDirId(),CodeEnum.DASHBOARD_DIR_NOT_BLANK);
            DashboardDirectoryDo directoryDo = directoryService.queryDbById(dashboard.getDirId());
            AssertUtil.notNull(directoryDo,CodeEnum.DASHBOARD_DIR_ID_NOT_EXIST);
        }

        AssertUtil.notBlank(dashboard.getOwnerEmail(),CodeEnum.OWNER_EMAIL_NOT_NULL);
    }
}
