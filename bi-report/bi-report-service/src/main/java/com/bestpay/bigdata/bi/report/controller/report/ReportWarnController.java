package com.bestpay.bigdata.bi.report.controller.report;

import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnListVO;
import com.bestpay.bigdata.bi.report.bean.warn.ReportWarnVO;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnEnumValueRequest;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnListRequest;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnRequest;
import com.bestpay.bigdata.bi.report.request.warn.ReportWarnStatusRequest;
import com.bestpay.bigdata.bi.report.response.common.QueryIndexAndReportResponse;
import com.bestpay.bigdata.bi.report.response.report.ReportWarnConfigVO;
import com.bestpay.bigdata.bi.report.service.report.ReportWarnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/biReport/warn")
@Api(value = "报表告警", tags = "报表告警")
public class ReportWarnController {

    @Resource
    private ReportWarnService reportWarnService;

    @GetMapping("/configList")
    @ApiOperation(value = "告警报表过滤项", httpMethod = "GET", response = Response.class)
    public Response<List<ReportWarnConfigVO>> configList(@RequestParam("reportId") String reportId,
        @RequestParam(name = "warnSourceType", required = false) String warnSourceType) {
        return reportWarnService.getConfigList(reportId,warnSourceType);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增报表告警", httpMethod = "POST", response = Response.class)
    public Response<String> add(@RequestBody ReportWarnRequest request) {
        return reportWarnService.addOrUpdate(request);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "报表告警详情", httpMethod = "GET", response = Response.class)
    public Response<ReportWarnVO> detail(@PathVariable Long id) {
        return reportWarnService.detail(id);
    }

    @GetMapping("/retry/{id}")
    @ApiOperation(value = "重试报表预警", httpMethod = "GET", response = Response.class)
    public Response<Boolean> retry(@PathVariable Long id) {
        return reportWarnService.retry(id);
    }

    @PostMapping("/update")
    @ApiOperation(value = "编辑报表告警", httpMethod = "POST", response = Response.class)
    public Response<String> update(@RequestBody ReportWarnRequest request) {
        return reportWarnService.addOrUpdate(request);
    }

    @PostMapping("/list")
    @ApiOperation(value = "报表告警列表", httpMethod = "POST", response = Response.class)
    public Response<PageQueryVO<ReportWarnListVO>> list(@RequestBody ReportWarnListRequest request) {
        Response<PageQueryVO<ReportWarnListVO>> data = reportWarnService.list(request);
        return data;
    }

    @PostMapping("/status")
    @ApiOperation(value = "状态更新", httpMethod = "POST", response = Response.class)
    public Response<String> status(@RequestBody @Valid ReportWarnStatusRequest request) {
        return reportWarnService.status(request);
    }

    @PostMapping("/querySelectValue")
    @ApiOperation(value = "状态更新", httpMethod = "POST", response = Response.class)
    public Response<QueryIndexAndReportResponse> querySelectValue(@RequestBody ReportWarnEnumValueRequest request) {
        return reportWarnService.querySelectValue(request);
    }

}
