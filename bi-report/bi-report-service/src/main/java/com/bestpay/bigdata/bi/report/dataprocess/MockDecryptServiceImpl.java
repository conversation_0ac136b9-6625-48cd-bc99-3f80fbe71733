package com.bestpay.bigdata.bi.report.dataprocess;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * @Author: den<PERSON><PERSON><PERSON>
 * @CreateDate: 2022/10/13 15:15
 */
@Component
@ConditionalOnProperty(value = "bi.decrypt.mode", havingValue = "MOCK")
public class MockDecryptServiceImpl implements DecryptService {

  @Override
  public String encrypt(String data) {
    return data;
  }

  @Override
  public String decrypt(String data) {
    return data;
  }

}
