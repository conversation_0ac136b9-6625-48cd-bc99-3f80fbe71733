package com.bestpay.bigdata.bi.report.refactor.correct;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.Dimension;
import com.bestpay.bigdata.bi.common.dto.report.AdvancedComputing;
import com.bestpay.bigdata.bi.common.dto.report.QueryReportConditionInfo;
import com.bestpay.bigdata.bi.common.dto.report.component.*;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.enums.ScopeFilterTypeEnum;
import com.bestpay.bigdata.bi.common.error.DashboardErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.report.check.computeColumn.ReportComputeColumnParser;
import com.bestpay.bigdata.bi.report.check.computeColumn.bean.ComputeColumnParseInfo;
import com.bestpay.bigdata.bi.report.dataprocess.DecryptService;
import com.bestpay.bigdata.bi.report.enums.report.ComputeTypeEnum;
import com.bestpay.bigdata.bi.report.enums.report.FieldDisplayTypeEnum;
import com.bestpay.bigdata.bi.report.enums.report.NewDateGroupTypeEnum;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IReportDataCorrect {

    @Resource
    private ReportComputeColumnParser reportComputeColumnParser;

    @Resource
    private DecryptService decryptService;

    /**
     * 构建一个以 uuid 为键的元数据 Map (metadataMap)，包含所有数据集字段和报表计算字段
     * 遍历报表配置的各个部分（维度、指标、排序等）
     * 使用 uuid 从 metadataMap 中直接查找对应的元数据信息，并用其修正当前字段对象
     */
    public ReportRequest reportDataCorrect(ReportRequest report,
                                           List<QueryReportConditionInfo> queryConditions,
                                           List<DatasetColumnConfigDTO> datasetColumns) {

        log.debug("Starting data correction for report: {}", JSONUtil.toJsonStr(report));

        // 解析计算字段并构建统一的元数据
        ComputeColumnParseInfo parseInfo = reportComputeColumnParser.computeColumnParseInfo(
                report.getDatasetInfoList().get(0).getDatasetId(),
                datasetColumns,
                report.getComputeColumnList()
        );

        // 创建一个以 UUID 为 Key 的元数据 Map
        final Map<String, CommonComponentPropertyDTO> metadataMap = buildMetadataMap(parseInfo);

        // 遍历并修正 ReportRequest 的各个部分

        // 修正维度
        if (CollUtil.isNotEmpty(report.getShowColumnList())) {
            log.info("Correcting showColumn list...");
            List<DimensionComponentPropertyDTO> fieldList = report.getShowColumnList();
            columnPropertyDataCorrect(fieldList, metadataMap);
            if (CollUtil.isNotEmpty(fieldList)) {
                fieldList.get(fieldList.size() - 1).setShowSubtotal(false);
            }
        }

        // 修正对比维度
        if (CollUtil.isNotEmpty(report.getContrastColumnList())) {
            log.info("Correcting contrastColumn list...");
            columnPropertyDataCorrect(report.getContrastColumnList(), metadataMap);
        }

        // 修正指标
        if (CollUtil.isNotEmpty(report.getIndexColumnList())) {
            log.info("Correcting indexColumn list...");
            columnPropertyDataCorrect(report.getIndexColumnList(), metadataMap);
        }

        // 修正筛选器
        if (CollUtil.isNotEmpty(report.getConditionList()) && CollUtil.isNotEmpty(queryConditions)) {
            log.info("Correcting queryConditions list...");
            queryConditionsDataCorrect(queryConditions, report.getConditionList(), metadataMap);
        }

        // 修正过滤器
        if (CollUtil.isNotEmpty(report.getFilterColumnList())) {
            log.info("Correcting filterColumn list...");
            filterConditionDataCorrect(report.getFilterColumnList(), metadataMap);
        }

        // 修正关键字
        if (CollUtil.isNotEmpty(report.getKeywordList())) {
            log.info("Correcting keyword list...");
            columnPropertyDataCorrect(report.getKeywordList(), metadataMap);
        }

        // 修正报表内排序
        if (CollUtil.isNotEmpty(report.getOrderColumnList())) {
            log.info("Correcting report orderColumn list...");
            orderColumnDataCorrect(report.getOrderColumnList(), metadataMap);
        }

        log.debug("Finished data correction successfully.");
        return report;
    }

    /**
     * 构建一个以 UUID 为键的统一元数据 Map
     * 这个 Map 是所有后续查找操作的唯一数据源
     */
    private Map<String, CommonComponentPropertyDTO> buildMetadataMap(ComputeColumnParseInfo parseInfo) {
        Map<String, CommonComponentPropertyDTO> metadataMap = new HashMap<>();

        if (CollUtil.isNotEmpty(parseInfo.getDimensionList())) {
            for (Dimension dimension : parseInfo.getDimensionList()) {

                DimensionComponentPropertyDTO prop = new DimensionComponentPropertyDTO();
                prop.setUuid(dimension.getUuid());
                prop.setEnName(dimension.getEnName());
                prop.setOriginEnName(dimension.getOriginEnName());
                prop.setShowTypeName(dimension.getShowTypeName());
                prop.setTypeName(dimension.getTypeName());
                metadataMap.put(dimension.getUuid(), prop);
            }
        }

        // 添加报表级计算字段的元数据
        if (StringUtils.isNotBlank(parseInfo.getParsedCompute())) {
            List<ComputeComponentPropertyDTO> computedFields = JSONUtil.toList(parseInfo.getParsedCompute(), ComputeComponentPropertyDTO.class);
            for (ComputeComponentPropertyDTO computedField : computedFields) {
                metadataMap.put(computedField.getUuid(), computedField);
            }
        }

        return metadataMap;
    }

    /**
     * 通用的字段列表修正方法（适用于维度、指标、对比、关键字等）
     */
    private <T extends CommonComponentPropertyDTO> void columnPropertyDataCorrect(List<T> fieldList,
                                                                                  final Map<String, CommonComponentPropertyDTO> metadataMap) {
        for (CommonComponentPropertyDTO currentField : fieldList) {
            String uuid = currentField.getUuid();
            if (StringUtils.isBlank(uuid)) {
                log.warn("Field has a blank UUID, skipping correction. Field: {}", JSONUtil.toJsonStr(currentField));
                continue;
            }

            // 通过 UUID 从 Map 中查找元数据
            CommonComponentPropertyDTO metadata = metadataMap.get(uuid);
            if (metadata == null) {
                throw new BiException(DashboardErrorCode.DASHBOARD_DEL_FIELD_TIP,
                        String.format("字段(uuid=%s, enName=%s)在数据源中已不存在，请检查报表配置。", uuid, currentField.getEnName()));
            }

            // 使用元数据修正当前字段
            currentField.setOriginEnName(metadata.getOriginEnName());
            currentField.setTypeName(metadata.getShowTypeName());

            // 如果是日期类型，设置默认粒度
            if (Objects.equals(metadata.getShowTypeName(), FieldType.DATETIME.name()) && currentField.getDateGroupType() == null) {
                currentField.setDateGroupType(NewDateGroupTypeEnum.DAYCLICKHOUSE.getCode());
            }

            // 如果元数据是计算字段，则补充计算相关信息
            if (metadata instanceof ComputeComponentPropertyDTO) {
                currentField.setFun(((ComputeComponentPropertyDTO) metadata).getFun());
                currentField.setCalculateLogic(((ComputeComponentPropertyDTO) metadata).getCalculateLogic());
            }

            // 特殊处理：指标的高级计算
            if (currentField instanceof IndexComponentPropertyDTO) {
                handleAdvancedComputing((IndexComponentPropertyDTO) currentField, metadataMap);
            }
        }
    }

    /**
     * 处理指标中的高级计算
     */
    private void handleAdvancedComputing(IndexComponentPropertyDTO indexField, final Map<String, CommonComponentPropertyDTO> metadataMap) {
        AdvancedComputing advancedComputing = indexField.getAdvancedComputing();
        if (advancedComputing == null) {
            return;
        }

        // 百分比高级计算，其 enName 依赖于指标自身
        if (ComputeTypeEnum.BAIFEN_BI.getCode().equals(advancedComputing.getType())) {
            advancedComputing.setEnName(indexField.getEnName());
        }

        String advancedFieldUuid = advancedComputing.getUuid();
        if (StringUtils.isBlank(advancedFieldUuid)) {
            return;
        }

        CommonComponentPropertyDTO advancedMetadata = metadataMap.get(advancedFieldUuid);
        if (advancedMetadata != null) {
            advancedComputing.setOriginEnName(advancedMetadata.getOriginEnName());
            advancedComputing.setTypeName(advancedMetadata.getShowTypeName());
            advancedComputing.setShowTypeName(advancedMetadata.getShowTypeName());
        } else {
            log.warn("Advanced computing field with UUID '{}' not found in metadata.", advancedFieldUuid);
        }
    }

    /**
     * 修正排序列表
     */
    private void orderColumnDataCorrect(List<OrderComponentDTO> orderColumns, final Map<String, CommonComponentPropertyDTO> metadataMap) {
        for (OrderComponentDTO orderColumn : orderColumns) {
            String uuid = orderColumn.getUuid();
            if (StringUtils.isBlank(uuid)) continue;

            CommonComponentPropertyDTO metadata = metadataMap.get(uuid);
            if (metadata == null) {
                throw new BiException(DashboardErrorCode.DASHBOARD_DEL_FIELD_TIP,
                        String.format("排序字段(uuid=%s, enName=%s)在数据源中已不存在。", uuid, orderColumn.getEnName()));
            }

            orderColumn.setOriginEnName(metadata.getOriginEnName());
            orderColumn.setTypeName(metadata.getShowTypeName());
            orderColumn.setShowTypeName(metadata.getShowTypeName());

            boolean isCompute = metadata instanceof ComputeComponentPropertyDTO;
            orderColumn.setIsComputeField(isCompute);
            if (isCompute) {
                orderColumn.setFun(((ComputeComponentPropertyDTO) metadata).getFun());
            }
        }
    }

    /**
     * 修正过滤器列表
     */
    private void filterConditionDataCorrect(List<FilterComponentPropertyDTO> filterInfos, final Map<String, CommonComponentPropertyDTO> metadataMap) {
        for (FilterComponentPropertyDTO filterInfo : filterInfos) {
            String uuid = filterInfo.getUuid();
            if (StringUtils.isBlank(uuid)) continue;

            CommonComponentPropertyDTO metadata = metadataMap.get(uuid);
            if (metadata == null) {
                throw new BiException(DashboardErrorCode.DASHBOARD_DEL_FIELD_TIP,
                        String.format("过滤字段(uuid=%s, enName=%s)在数据源中已不存在。", uuid, filterInfo.getFieldName()));
            }

            filterInfo.setOriginEnName(metadata.getOriginEnName());
            filterInfo.setTypeName(metadata.getShowTypeName());

            if (metadata instanceof ComputeComponentPropertyDTO) {
                filterInfo.setFun(((ComputeComponentPropertyDTO) metadata).getFun());
            }
        }
    }

    /**
     * 修正筛选器列表 (Query Conditions)
     */
    private void queryConditionsDataCorrect(List<QueryReportConditionInfo> conditionInfos,
                                            List<ConditionComponentPropertyDTO> conditionInfoList,
                                            final Map<String, CommonComponentPropertyDTO> metadataMap) {

        // 预处理，构建 ID 到聚合方式和加密标记的映射
        Map<Long, String> polymerizationMap = conditionInfoList.stream()
                .filter(c -> c.getId() != null)
                .collect(Collectors.toMap(ConditionComponentPropertyDTO::getId, ConditionComponentPropertyDTO::getPolymerization, (v1, v2) -> v1));

        Map<Long, Boolean> isEncryptMap = conditionInfoList.stream()
                .filter(c -> c.getId() != null && c.getScreeningCondition() != null)
                .collect(Collectors.toMap(ConditionComponentPropertyDTO::getId,
                        c -> Boolean.TRUE.equals(c.getScreeningCondition().getIsEncrypt()), (v1, v2) -> v1));

        for (QueryReportConditionInfo conditionInfo : conditionInfos) {
            // 设置默认的过滤类型
            if (Objects.isNull(conditionInfo.getScopeFilterType()) || !ScopeFilterTypeEnum.isInclude(conditionInfo.getScopeFilterType())) {
                conditionInfo.setScopeFilterType(
                        conditionInfo.getShowTypeName().toUpperCase().contains(FieldType.CHARACTER.name()) ?
                                ScopeFilterTypeEnum.IN.getCode() : ScopeFilterTypeEnum.INTERVAL.getCode()
                );
            }

            String uuid = conditionInfo.getUuid();
            if (StringUtils.isBlank(uuid)) {
                continue;
            }

            CommonComponentPropertyDTO metadata = metadataMap.get(uuid);
            if (metadata == null) {
                throw new BiException(DashboardErrorCode.DASHBOARD_DEL_FIELD_TIP,
                        String.format("筛选字段(uuid=%s, enName=%s)在数据源中已不存在。", uuid, conditionInfo.getFieldName()));
            }

            // 修正元数据
            conditionInfo.setOriginEnName(metadata.getOriginEnName());
            conditionInfo.setTypeName(metadata.getShowTypeName());
            if (Objects.isNull(conditionInfo.getPolymerization())) {
                conditionInfo.setPolymerization(polymerizationMap.get(conditionInfo.getId()));
            }

            if (metadata instanceof ComputeComponentPropertyDTO) {
                conditionInfo.setFun(((ComputeComponentPropertyDTO) metadata).getFun());
            }

            // 处理加密
            if (Boolean.TRUE.equals(isEncryptMap.get(conditionInfo.getId()))) {
                encryptConditionValue(conditionInfo);
            }
        }
    }

    private void encryptConditionValue(QueryReportConditionInfo conditionInfo) {
        if (FieldDisplayTypeEnum.CHARACTER_INPUT.name().equals(conditionInfo.getShowTypeName())) {
            String stringValue = conditionInfo.getStringValue();
            if (StringUtils.isNotBlank(stringValue)) {
                String encryptedValue = Arrays.stream(stringValue.split(","))
                        .filter(StringUtils::isNotBlank)
                        .map(decryptService::encrypt)
                        .collect(Collectors.joining(","));
                conditionInfo.setStringValue(encryptedValue);
            }
        }
    }
}
