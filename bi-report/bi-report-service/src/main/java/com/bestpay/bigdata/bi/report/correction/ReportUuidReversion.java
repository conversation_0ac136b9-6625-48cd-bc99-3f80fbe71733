//package com.bestpay.bigdata.bi.report.correction;
//
//import cn.hutool.json.JSONUtil;
//import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
//import com.bestpay.bigdata.bi.common.dto.report.ColumnProperty;
//import com.bestpay.bigdata.bi.common.dto.report.QueryReportConditionInfo;
//import com.bestpay.bigdata.bi.common.response.Response;
//import com.bestpay.bigdata.bi.common.util.StringUtil;
//import com.bestpay.bigdata.bi.database.api.report.ReportService;
//import com.bestpay.bigdata.bi.database.bean.report.Report;
//import java.io.Serializable;
//import java.util.List;
//import java.util.UUID;
//import javax.annotation.Resource;
//import javax.sql.DataSource;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.jdbc.core.BeanPropertyRowMapper;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.jdbc.datasource.DriverManagerDataSource;
//import org.springframework.stereotype.Component;
//
///**
// * ClassName: ReportUuidReversion
// * Package: com.bestpay.bigdata.bi.report.datasetCorrection
// * Description:
// *
// * <AUTHOR>
// * @Create 2023/8/11 11:05
// * @Version 1.0
// */
//@Slf4j
//@Component
//public class ReportUuidReversion {
//
//    @Resource
//    private ApolloRefreshConfig apolloRefreshConfig;
//
//    @Resource
//    private ReportService reportService;
//
//    public Response reportReversion() {
//        JdbcTemplate biJdbcTemplate = new JdbcTemplate();
//        biJdbcTemplate.setDataSource(biDataSource());
//
//        List<Report> reportList = biJdbcTemplate.query("select * from t_report where status_code !=9", new BeanPropertyRowMapper<>(Report.class));
//        log.info("query all t_report records from bi, size :{}", reportList.size());
//
//        for (Report report : reportList) {
//            List<ColumnProperty> conditionList = JSONUtil.toList(report.getCondition(), ColumnProperty.class);
//            setReportUuidKey(conditionList);
//            report.setCondition(JSONUtil.toJsonStr(conditionList));
//
//            List<ColumnProperty> showColumnList = JSONUtil.toList(report.getShowColumn(), ColumnProperty.class);
//            setReportUuidKey(showColumnList);
//            report.setShowColumn(JSONUtil.toJsonStr(showColumnList));
//
//            List<ColumnProperty> contrastColumnList = JSONUtil.toList(report.getContrastColumn(), ColumnProperty.class);
//            setReportUuidKey(contrastColumnList);
//            report.setContrastColumn(JSONUtil.toJsonStr(contrastColumnList));
//
//            List<ColumnProperty> indexColumnList = JSONUtil.toList(report.getIndexColumn(), ColumnProperty.class);
//            setReportUuidKey(indexColumnList);
//            report.setIndexColumn(JSONUtil.toJsonStr(indexColumnList));
//
//            List<ColumnProperty> keywordList = JSONUtil.toList(report.getKeyword(), ColumnProperty.class);
//            setReportUuidKey(keywordList);
//            report.setKeyword(JSONUtil.toJsonStr(keywordList));
//
//            List<QueryReportConditionInfo> queryReportConditionInfos = JSONUtil.toList(report.getFilterColumn(), QueryReportConditionInfo.class);
//            setReportUuidKeyCondition(queryReportConditionInfos);
//            report.setFilterColumn(JSONUtil.toJsonStr(queryReportConditionInfos));
//
//            reportService.update(report);
//        }
//
//        return Response.ok();
//    }
//
//    private void setReportUuidKey(List<ColumnProperty> columnList) {
//        for (ColumnProperty columnProperty : columnList) {
//            if (StringUtil.isEmpty(columnProperty.getUuid())) {
//                columnProperty.setUuid("report" + UUID.randomUUID());
//            }
//        }
//    }
//
//
//    private void setReportUuidKeyCondition(List<QueryReportConditionInfo> filterConditions) {
//        for (QueryReportConditionInfo filterCondition : filterConditions) {
//            if (StringUtil.isEmpty(filterCondition.getUuid())) {
//                filterCondition.setUuid("report" + UUID.randomUUID());
//            }
//        }
//    }
//
//
//    private DataSource biDataSource() {
//        DriverManagerDataSource dataSource = new DriverManagerDataSource();
//
//        IndexProcessDatasetReversion.DataSourceConfig biMysqlConfig = JSONUtil.toBean(apolloRefreshConfig.getBiMysqlDatasource(), IndexProcessDatasetReversion.DataSourceConfig.class);
//
//        dataSource.setDriverClassName(biMysqlConfig.getDriveClass());
//        dataSource.setUrl(biMysqlConfig.getJdbcUrl());
//        dataSource.setUsername(biMysqlConfig.getUserName());
//        dataSource.setPassword(biMysqlConfig.getPassWord());
//
//        return dataSource;
//    }
//
//    @Data
//    class DataSourceConfig implements Serializable {
//        private String driveClass;
//        private String jdbcUrl;
//        private String userName;
//        private String passWord;
//        private String validationQuery;
//        private Integer maxTotal;
//        private Long maxWaitMillis;
//        private Boolean testOnBorrow;
//        private String jmxBeanName;
//    }
//}
