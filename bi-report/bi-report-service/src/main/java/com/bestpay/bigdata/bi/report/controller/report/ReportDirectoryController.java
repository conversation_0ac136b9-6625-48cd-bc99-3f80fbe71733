package com.bestpay.bigdata.bi.report.controller.report;

import static com.bestpay.bigdata.bi.common.constant.BIConstant.EMAIL;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.api.report.ReportDirectoryDAOService;
import com.bestpay.bigdata.bi.database.dao.report.ReportDirectoryDo;
import com.bestpay.bigdata.bi.report.bean.report.ReportDirectoryVO;
import com.bestpay.bigdata.bi.report.request.report.MoveReportDirectoryRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportDirectoryRequest;
import com.bestpay.bigdata.bi.report.service.report.ReportDirectoryService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import java.util.List;
import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2023-06-02-14:01
 */
@RestController
@RequestMapping("/biReport/report/directory")
@Api(value = "报表目录", tags = "报表目录")
public class ReportDirectoryController
{

    @Resource
    private ReportDirectoryService directoryService;
    @Resource
    private AuthorityCheckUtil authorityCheckUtil;
    @Resource
    private ReportDirectoryDAOService directoryDAOService;

    /**
     * 新增
     */
    @PostMapping("/add")
    @ApiOperation(httpMethod = "POST", value = "新增", notes = "新增")
    public Response<ReportDirectoryVO> addReportDirectory(@RequestBody ReportDirectoryRequest reportDirectoryRequest) {
        return directoryService.addReportDirectory(reportDirectoryRequest);
    }


    /**
     * 修改
     */
    @PutMapping("/update")
    @ApiOperation(httpMethod = "PUT", value = "修改", notes = "修改")
    public Response<ReportDirectoryVO> updateReportDirectory(@RequestBody ReportDirectoryRequest reportDirectoryRequest) {
        ReportDirectoryDo directoryDo
            = directoryDAOService.queryDbById(reportDirectoryRequest.getId());

        // 权限校验
        authorityCheckUtil.checkOwner(EMAIL, directoryDo.getCreatedBy());
        return directoryService.updateReportDirectory(reportDirectoryRequest);
    }


    /**
     * 删除
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(httpMethod = "DELETE", value = "删除", notes = "删除")
    public Response<Boolean> deleteReportDirectory(@PathVariable Long id) {
        ReportDirectoryDo directoryDo
            = directoryDAOService.queryDbById(id);

        // 权限校验
        authorityCheckUtil.checkOwner(EMAIL, directoryDo.getCreatedBy());
        return directoryService.deleteReportDirectory(id);
    }


    /**
     * 通过组织编码查询目录列表
     *
     * @param orgCode 组织编码
     * @return
     */
    @GetMapping("/getReportDirectoryList")
    @ApiOperation(httpMethod = "GET", value = "通过组织编码查询目录列表", notes = "通过组织编码查询目录列表")
    public Response<List<ReportDirectoryVO>> getReportDirectoryList(@RequestParam("orgCode") String orgCode) {
        return this.directoryService.getReportDirectoryList(orgCode);
    }

    /**
     * report directory info
     * @param id directory id
     * @return
     */
    @GetMapping("/info/{id}")
    @ApiOperation(httpMethod = "GET", value = "报表目录信息", notes = "报表目录信息")
    public Response<ReportDirectoryVO> getReportDirectoryInfo(@PathVariable Long id) {

        return directoryService.getReportDirectoryInfo(id);
    }


    /**
     * 获取能够加入目录的目录列表
     * @param orgCode
     * @return
     */
    @GetMapping("/getReportAddDirectoryList")
    @ApiOperation(httpMethod = "GET", value = "获取能够加入目录的目录列表", notes = "获取能够加入目录的目录列表")
    public Response<List<ReportDirectoryVO>> getReportAddDirectoryList(@RequestParam("orgCode") String orgCode) {
        return this.directoryService.getReportAddDirectoryList(orgCode);
    }


    /**
     * 移动
     */
    @PostMapping("/move")
    @ApiOperation(httpMethod = "POST", value = "移动", notes = "移动")
    public Response<Boolean> moveReportDirectory(@RequestBody MoveReportDirectoryRequest moveReportDirectoryRequest) {
        return directoryService.moveReportDirectory(moveReportDirectoryRequest);
    }
}
