package com.bestpay.bigdata.bi.report.refactor.processor.builder;

import cn.hutool.core.collection.CollUtil;
import com.bestpay.bigdata.bi.common.dto.datascreen.ComponentDTO;
import com.bestpay.bigdata.bi.common.dto.datascreen.component.IndexComponentInfoDTO;
import com.bestpay.bigdata.bi.common.dto.datascreen.component.ReportComponentInfoDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DataSet;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.report.component.OrderComponentDTO;
import com.bestpay.bigdata.bi.common.enums.ComponentDataQueryType;
import com.bestpay.bigdata.bi.common.enums.ComponentTypeEnum;
import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.request.new_datascreen.component.ComponentDataQueryRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.bestpay.bigdata.bi.report.service.impl.datascreen.AbstractComponent;
import com.bestpay.bigdata.bi.report.service.impl.datascreen.ComponentFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DatascreenRequestBuilder extends AbstractSceneRequestBuilder {

    @Autowired
    private ComponentFactory componentFactory;

    @Autowired
    private DatasetService datasetService;

    @Override
    public RequestKey supportedKey() {
        return new RequestKey(SceneType.DATASCREEN, ProcessorType.QUERY);
    }

    @Override
    protected void buildReportRequest(ProcessContext context) {
        ComponentDataQueryRequest request = (ComponentDataQueryRequest) context.getRawRequest();
        ComponentTypeEnum componentTypeEnum = ComponentTypeEnum.getByCode(request.getComponentType());
        AbstractComponent component = componentFactory.getComponent(request.getComponentType());

        ReportRequest reportRequest;
        switch (componentTypeEnum) {
            case REPORT:
                reportRequest = buildRequest(request, component, ReportComponentInfoDTO.class, ReportComponentInfoDTO::getOrderColumnList);
                break;
            case INDEX:
                reportRequest = buildRequest(request, component, IndexComponentInfoDTO.class, IndexComponentInfoDTO::getOrderColumnList);
                break;
            default:
                log.warn("Component type [{}] does not require building report request.", componentTypeEnum);
                return;
        }

        // 设置上下文
        DatasetInfo datasetInfo = reportRequest.getDatasetInfoList().get(0);
        context.setDatasetColumnConfigList(getDatasetColumnConfig(datasetInfo.getDatasetId()));
        context.setReportRequest(reportRequest);
    }

    private <T extends ReportComponentInfoDTO> ReportRequest buildRequest(
            ComponentDataQueryRequest request,
            AbstractComponent component,
            Class<T> clazz,
            Function<T, List<OrderComponentDTO>> orderColumnGetter) {

        // 获取组件信息
        ComponentDTO componentData = component.getComponentInfo(
                request.getDataScreenUUID(),
                request.getComponentCode(),
                request.getVersionType(),
                ComponentDataQueryType.REDIS_THEN_MYSQL);

        // 类型转换
        T componentInfoDTO = clazz.cast(componentData.getComponentInfo());

        // 构建 ReportRequest
        ReportRequest reportRequest = convertReportRequest(componentInfoDTO);

        BeanUtils.copyProperties(request, reportRequest);
        reportRequest.setQueryConditions(new ArrayList<>());
        reportRequest.setParamConditions(new ArrayList<>());

        // 设置排序列
        List<OrderComponentDTO> orderColumns = orderColumnGetter.apply(componentInfoDTO);
        if (CollUtil.isNotEmpty(orderColumns)) {
            reportRequest.setOrderColumnList(orderColumns);
        }

        return reportRequest;
    }

    private ReportRequest convertReportRequest(ReportComponentInfoDTO reportComponentInfoDTO) {
        ReportRequest report = new ReportRequest();
        BeanUtils.copyProperties(reportComponentInfoDTO, report);

        // 数据集信息
        List<DatasetInfo> datasets = reportComponentInfoDTO.getDatasetInfoList();
        List<DataSet> dataSets = datasetService.getDataSet(Lists.newArrayList(datasets.get(0).getDatasetId()));
        List<DatasetInfo> infos = dataSets.stream().map(p -> {
            DatasetInfo info = new DatasetInfo();
            BeanUtils.copyProperties(p, info);
            return info;
        }).collect(Collectors.toList());

        report.setDatasetInfoList(infos);
        return report;
    }
}
