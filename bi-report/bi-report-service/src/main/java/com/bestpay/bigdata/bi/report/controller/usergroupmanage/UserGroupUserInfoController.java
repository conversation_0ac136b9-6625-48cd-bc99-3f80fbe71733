package com.bestpay.bigdata.bi.report.controller.usergroupmanage;


import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.StringUtil;

import com.bestpay.bigdata.bi.report.usermanage.service.UserDataSyncService;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserInfoResponse;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserInfoTreeResponse;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserSearchRequest;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserSearchTreeRequest;
import com.bestpay.bigdata.bi.report.usermanage.service.DbUserManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户组用户信息
 *
 * @author: lm
 */
@RestController
@RequestMapping("/biReport/user/group/user")
@Api(value = "用户组用户信息", tags = "用户组用户信息")
public class UserGroupUserInfoController {

  @Resource
  private DbUserManageService dbUserManageService;

    @Resource
  private UserDataSyncService userSyncService;

  @RequestMapping("/search")
  @ApiOperation(httpMethod = "POST", value = "用户查询", notes = "用户查询")
  public Response<List<UserInfoResponse>> search(@RequestBody @Validated UserSearchRequest request) {
    List<UserInfoResponse> userInfoResponses = dbUserManageService.search(request);
    return Response.ok(userInfoResponses);
  }

  @RequestMapping("/searchTree")
  @ApiOperation(httpMethod = "POST", value = "用户信息树状查询", notes = "用户信息树状查询")
  public Response<List<UserInfoTreeResponse>> searchTree(@RequestBody UserSearchTreeRequest request) {
    List<UserInfoTreeResponse> userInfoResponses = dbUserManageService.searchTree(request);
    return Response.ok(userInfoResponses);
  }

  @RequestMapping("/sync")
  @ApiOperation(httpMethod = "POST", value = "用户信息同步", notes = "用户信息同步，可指定账户名进行单用户同步")
  public Response<List<UserInfoResponse>> sync(
      @RequestParam(value = "accountName", required = false) String accountName) {

    // 根据是否有账户名参数调用不同的同步方法
    if (StringUtil.isNotEmpty(accountName)) {
      userSyncService.syncUserByAccount(accountName);
    } else {
      userSyncService.userDataSync();
    }

    return Response.ok();
  }

}
