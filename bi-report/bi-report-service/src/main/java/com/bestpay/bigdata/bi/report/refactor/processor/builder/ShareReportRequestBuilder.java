package com.bestpay.bigdata.bi.report.refactor.processor.builder;

import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ShareReportRequestBuilder extends ReportRequestBuilder {

    @Override
    public RequestKey supportedKey() {
        return new RequestKey(SceneType.SHARE_REPORT, ProcessorType.QUERY);
    }

    @Override
    protected void buildReportRequest(ProcessContext context) {
        super.buildReportRequest(context);
    }
}
