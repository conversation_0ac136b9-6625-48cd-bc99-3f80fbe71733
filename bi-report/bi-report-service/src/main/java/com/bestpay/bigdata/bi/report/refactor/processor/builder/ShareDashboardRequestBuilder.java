package com.bestpay.bigdata.bi.report.refactor.processor.builder;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.request.dashboard.OutNetDataRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardDataRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ShareDashboardRequestBuilder extends DashboardRequestBuilder {

    @Override
    public RequestKey supportedKey() {
        return new RequestKey(SceneType.SHARE_DASHBOARD, ProcessorType.QUERY);
    }

    @Override
    protected void buildReportRequest(ProcessContext context) {
        OutNetDataRequest outNetDataRequest = (OutNetDataRequest) context.getRawRequest();
        TableCardDataRequest cardDataRequest = JSONUtil.toBean(JSONUtil.toJsonStr(outNetDataRequest.getData()), TableCardDataRequest.class);
        context.setRawRequest(cardDataRequest);
        super.buildReportRequest(context);
    }
}
