package com.bestpay.bigdata.bi.report.correction;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-07-14-10:27
 */
@Data
public class DataSet implements Serializable {
    private static final long serialVersionUID = -61298867735072670L;

    private Long id;
    /**
     * 指标分类
     */
    private Long businessDomainId;
    /**
     * 数据域
     */
    private Long dataDomainId;

    /**
     * 中文名字
     */
    private String name;
    /**
     * 英文名字
     */
    private String enName;
    /**
     * 描述
     */
    private String datasetDesc;
    /**
     * 类型
     */
    private Integer typeCode;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 过滤条件
     */
    private String filterCondition;
    /**
     * 分区字段Id
     */
    private String partitionColumnId;
    /**
     * 分区字段名称
     */
    private String partitionColumnName;
    /**
     * 分区字段日期格式
     */
    private String dateFormat;
    /**
     * 状态
     */
    private Integer statusCode;
    /**
     * 组织编码
     */
    private String orgCode;
    /**
     * 可访问组织编码
     */
    private String accessOrgCode;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

}