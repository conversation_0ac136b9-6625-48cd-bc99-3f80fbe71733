package com.bestpay.bigdata.bi.report.refactor.processor;

import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;


@Component
public class ReportProcessorFactory {

    private final Map<ProcessorType, AbstractReportProcessor> processors = new EnumMap<>(ProcessorType.class);

    @Autowired
    public ReportProcessorFactory(List<AbstractReportProcessor> processorList) {
        for (AbstractReportProcessor processor : processorList) {
            processors.put(processor.getProcessorType(), processor);
        }
    }

    public AbstractReportProcessor getProcessor(ProcessorType type) {
        return processors.get(type);
    }
}
