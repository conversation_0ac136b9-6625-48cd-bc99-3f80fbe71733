package com.bestpay.bigdata.bi.report.correction;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.api.report.ReportService;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.database.bean.report.ReportQueryDTO;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * ClassName: ReportOrderNumReversion
 * Package: com.bestpay.bigdata.bi.report.controller.dataset.correction
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/1/2 14:57
 * @Version 1.0
 */
@Service
@Slf4j
public class ReportOrderNumReversion
{
    @Resource
    private ReportService reportService;

    public Response reportReversion() {
        ReportQueryDTO reportQueryDTO = new ReportQueryDTO();
        List<Report> reports = reportService.queryAll(reportQueryDTO);

        reports = reports.stream()
                .sorted(Comparator.comparing(Report::getReportName))
                .collect(Collectors.toList());

        long orderNum = 1;

        for (Report report : reports) {
            log.info("start report()");
            Report re = new Report();
            re.setId(report.getId());
            re.setOrderNum(orderNum);
            orderNum++;
            reportService.update(re);
        }
        return Response.ok();
    }

}
