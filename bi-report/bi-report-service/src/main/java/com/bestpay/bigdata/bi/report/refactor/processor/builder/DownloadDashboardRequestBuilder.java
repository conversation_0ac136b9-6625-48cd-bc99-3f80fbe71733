package com.bestpay.bigdata.bi.report.refactor.processor.builder;


import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dashboard.TableCardInfoDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewReportCardDTO;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.request.report.DownloadApplyRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

@Component
public class DownloadDashboardRequestBuilder extends DashboardRequestBuilder {

    @Override
    public RequestKey supportedKey() {
        return new RequestKey(SceneType.DASHBOARD, ProcessorType.DOWNLOAD);
    }

    @Override
    protected void preHandleDownloadChooseColumn(ProcessContext context) {

        DownloadApplyRequest reportRequest = (DownloadApplyRequest) context.getRawRequest();
        // 兼容直接下载场景
        if (reportRequest.getChooseColumnList() == null) {
            reportRequest.setChooseColumnList(new ArrayList<>());
        }

        // 获取报表卡片信息
        TableCardInfoDTO reportCardDTO = dashboardTableCardService.getReportCard(
                reportRequest.getDashboardId(), reportRequest.getCardCode());
        NewReportCardDTO cardDTO = (NewReportCardDTO) reportCardDTO.getCardInfo();

        // 根据下载选择字段对报表进行排序
        Report report = new Report();
        report.setShowColumn(JSONUtil.toJsonStr(cardDTO.getShowColumnList()));
        report.setIndexColumn(JSONUtil.toJsonStr(cardDTO.getIndexColumnList()));
        sortChooseColumnList(reportRequest, report);
    }

    @Override
    protected void buildReportRequest(ProcessContext context) {

        DownloadApplyRequest reportRequest = (DownloadApplyRequest) context.getRawRequest();

        // 处理分页参数
        reportRequest.setPageNum(null);
        reportRequest.setPageSize(null);

        context.setRawRequest(reportRequest);

        super.buildReportRequest(context);
    }

    @Override
    protected void postHandleDownloadChooseColumn(ProcessContext context) {

        DownloadApplyRequest downloadApplyRequest = (DownloadApplyRequest) context.getRawRequest();
        ReportRequest reportRequest = context.getReportRequest();

        // 标记非选择字段为隐藏字段
        markUnChooseColumnAsHidden(downloadApplyRequest, reportRequest);
    }

}
