package com.bestpay.bigdata.bi.report.refactor.processor.validator;

import com.bestpay.bigdata.bi.common.error.DashboardErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.request.auth.ObjectAuthRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardDataRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DashboardValidator extends AbstractScenceValidator {

    @Autowired
    private DashboardDaoService dashboardService;

    @Override
    public RequestKey supportedKey() {
        return new RequestKey(SceneType.DASHBOARD, ProcessorType.QUERY);
    }

    @Override
    protected boolean isAuthCheckEnabled() {
        return true;
    }

    @Override
    protected void beforeValidate(ProcessContext context) {
        TableCardDataRequest request = (TableCardDataRequest) context.getRawRequest();
        Dashboard dashboard = dashboardService.getById(request.getDashboardId());
        if (dashboard == null) {
            throw new BiException(DashboardErrorCode.DASHBOARD_NOT_EXISTS, "仪表板不存在,id=" + request.getDashboardId());
        }
    }

    @Override
    protected ObjectAuthRequest buildAuthRequest(ProcessContext context) {
        TableCardDataRequest request = (TableCardDataRequest) context.getRawRequest();
        ObjectAuthRequest authRequest = new ObjectAuthRequest();
        authRequest.setAuthResourceId(request.getDashboardId() + "");
        authRequest.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());
        return authRequest;
    }
}
