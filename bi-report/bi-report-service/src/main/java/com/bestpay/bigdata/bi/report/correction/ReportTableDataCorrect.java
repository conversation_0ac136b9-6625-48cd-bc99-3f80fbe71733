package com.bestpay.bigdata.bi.report.correction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dashboard.ReportCardQueryDTO;
import com.bestpay.bigdata.bi.common.dto.report.ReportSimpleColumn;
import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardReportCardService;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import com.bestpay.bigdata.bi.report.request.report.UpdateReportRequest;
import com.bestpay.bigdata.bi.report.service.report.ReportProcessService;
import com.bestpay.bigdata.bi.report.service.report.ReportUpdateService;
import com.google.common.collect.Lists;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReportTableDataCorrect {

  @Resource
  private ReportUpdateService updateService;

  @Resource
  private DashboardReportCardService reportCardService;

  @Resource
  private ReportProcessService reportProcessService;

  public void correct(){

    for(Long i=0L;i < 10000;i++){
      try {
        log.info("processing i={}", i );
        Response<ReportDetailVO> detail
            =  reportProcessService.queryReportTemplate(i);

        if(detail!=null && detail.getData()!=null){
          UpdateReportRequest report
              = JSONUtil.toBean(JSONUtil.toJsonStr(detail.getData()), UpdateReportRequest.class);
          updateService.insertReportTemplate(report,false);
        }
      }catch (Exception e){
        log.error("correct error, reportId={}",i, e);
      }
    }

    for(Long i=0L;i < 10000;i++){
      try {

        ReportCardQueryDTO cardQuery = new ReportCardQueryDTO();
        cardQuery.setIdList(Lists.newArrayList(i));

        List<DashboardReportCardDO> reports = reportCardService.find(cardQuery);
        if(CollUtil.isEmpty(reports)){
          continue;
        }

        List<ReportSimpleColumn> ReportSimpleColumns
            = JSONUtil.toList(reports.get(0).getReportStructure(), ReportSimpleColumn.class);

        if(CollUtil.isEmpty(ReportSimpleColumns)){
          continue;
        }

        addParentId(ReportSimpleColumns, null);

        DashboardReportCardDO reportDO = new DashboardReportCardDO();
        reportDO.setId(reports.get(0).getId());
        reportDO.setReportStructure(JSONUtil.toJsonStr(ReportSimpleColumns));
        reportCardService.update(reportDO);

      }catch (Exception e){
        log.error("correct error, reportId={}",i, e);
      }
    }
  }

  private void addParentId(List<ReportSimpleColumn> ReportSimpleColumns,String parentId){
    for (ReportSimpleColumn reportSimpleColumn : ReportSimpleColumns) {
      reportSimpleColumn.setParentId(parentId);
      if(CollUtil.isEmpty(reportSimpleColumn.getChildColumnList())){
        continue;
      }
      addParentId(reportSimpleColumn.getChildColumnList(),reportSimpleColumn.getUuid());
    }
  }
}
