//package com.bestpay.bigdata.bi.report.correction;
//
//import cn.hutool.json.JSONArray;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
//import com.bestpay.bigdata.bi.common.dto.dataset.DatasetElementQueryDTO;
//import com.bestpay.bigdata.bi.common.dto.dataset.DatasetFieldConfigQueryDTO;
//import com.bestpay.bigdata.bi.common.dto.dataset.DatasetFieldQueryDTO;
//import com.bestpay.bigdata.bi.common.dto.report.ColumnProperty;
//import com.bestpay.bigdata.bi.common.response.Response;
//import com.bestpay.bigdata.bi.database.api.dashboard.DashboardFilterCardService;
//import com.bestpay.bigdata.bi.database.api.dashboard.DashboardIndexTextCardService;
//import com.bestpay.bigdata.bi.database.api.dataset.DatasetConfigDAOService;
//import com.bestpay.bigdata.bi.database.api.dataset.DatasetDAOService;
//import com.bestpay.bigdata.bi.database.api.dataset.DatasetElementDAOService;
//import com.bestpay.bigdata.bi.database.api.dataset.DatasetFieldDAOService;
//import com.bestpay.bigdata.bi.database.api.report.ReportService;
//import com.bestpay.bigdata.bi.database.bean.report.Report;
//import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO;
//import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO;
//import com.bestpay.bigdata.bi.database.dao.dataset.DatasetConfigDo;
//import com.bestpay.bigdata.bi.database.dao.dataset.DatasetDo;
//import com.bestpay.bigdata.bi.database.dao.dataset.DatasetElementDo;
//import com.bestpay.bigdata.bi.database.dao.dataset.DatasetFieldDo;
//import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
//import com.google.common.collect.Lists;
//import java.util.ArrayList;
//import java.util.Comparator;
//import java.util.HashMap;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//import javax.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
///**
// * ClassName: ReportLogicCnNameReversion
// * Package: com.bestpay.bigdata.bi.report.datasetCorrection
// * Description:
// *
// * <AUTHOR>
// * @Create 2023/9/5 11:18
// * @Version 1.0
// */
//@Slf4j
//@Component
//public class ReportLogicCnNameReversion {
//
//
//    @Resource
//    private ApolloRefreshConfig apolloRefreshConfig;
//
//    @Resource
//    private ReportService reportService;
//
//    @Resource
//    private DatasetConfigDAOService datasetConfigDAOService;
//
//    @Resource
//    private DatasetDAOService datasetDAOService;
//
//    @Resource
//    private DatasetElementDAOService datasetElementDAOService;
//
//    @Resource
//    private DatasetFieldDAOService datasetFieldDAOService;
//
//    @Resource
//    private DashboardIndexTextCardService dashboardIndexTextCardService;
//
//    @Resource
//    private DatasetService datasetService;
//
//    @Resource
//    private DashboardFilterCardService filterCardService;
//
//    public Response filterReversion() {
//        List<DashboardFilterCardDO> filterCardDOS = filterCardService.find();
//        int i=0;
//        for (DashboardFilterCardDO filterCardDO : filterCardDOS) {
//            try {
//                if(filterCardDO.getRelateCardsInfo()!=null){
//                    JSONArray array = JSONUtil.parseArray(filterCardDO.getRelateCardsInfo());
//                    for (Object o : array) {
//                        JSONObject object = (JSONObject) o;
//                        String value = object.getStr("value");
//
//                        if(StringUtils.isNotBlank(value) && value.contains("editorText")) {
//                            String[] args = value.split("editorText");
//                            object.put("value", args[0]);
//                        }
//
//                        Object cardType = object.get("cardType");
//                        if(cardType!=null){
//                            if(cardType.toString().equals("1")){
//                                object.put("cardType", "report");
//                            }
//                            if(cardType.toString().equals("2")){
//                                object.put("cardType", "filter");
//                            }
//                            if(cardType.toString().equals("3")){
//                                object.put("cardType", "text");
//                            }
//                            if(cardType.toString().equals("4")){
//                                object.put("cardType", "indexText");
//                            }
//                        }
//
//                        JSONObject data = JSONUtil.parseObj(filterCardDO.getDataSetInfo());
//
//                        filterCardDO.setDatasetId(data.getLong("datasetId"));
//                        Object cardId = object.get("cardId");
//                        Object value11 = object.get("value");
//                        if(cardId==null){
//                            cardId=value11;
//                        }
//
//                        object.put("cardId", value11);
//                        object.put("value", cardId);
//                    }
//
//
//                        DashboardFilterCardDO update = new DashboardFilterCardDO();
//                        update.setId(filterCardDO.getId());
//                        update.setRelateCardsInfo(array.toString());
//                        update.setDatasetId(filterCardDO.getDatasetId());
//                        filterCardService.updateAll(update);
//
//                }
//            }catch (Exception e){
//                log.info("filterReversion error, id={}",filterCardDO.getId(), e);
//            }
//
//            i++;
//            log.info("processing......， total={}, num={}",filterCardDOS.size(), i);
//        }
//
//        return Response.ok();
//    }
//
//
//    public Response indexTextReversion() {
//        List<DashboardIndexTextCardDO> indexTextCardDOList = dashboardIndexTextCardService.find(null);
//        log.info("ready to index text computeColumn reversion, size : {}", indexTextCardDOList.size());
//
//        long count = 0;
//        for (DashboardIndexTextCardDO dashboardIndexTextCardDO : indexTextCardDOList) {
//
////            try {
////                JSONObject jsonObject = JSONUtil.parseObj(dashboardIndexTextCardDO.getSearchData());
////                Object computeColumn = jsonObject.get("computeColumn");
////
////                long dataSetId = Long.parseLong(JSONUtil.parseObj(dashboardIndexTextCardDO.getIndexInfo()).get("dataSet").toString());
////                DimensionRequest request = new DimensionRequest();
////                request.setDatasetId(dataSetId);
////                List<Dimension> dimensionList = datasetService.getDimensionListV2(request);
////
////                if (Objects.nonNull(computeColumn)) {
////                    List<ColumnProperty> computeColumnList = JSONUtil.toList(computeColumn.toString(), ColumnProperty.class);
////
////                    for (ColumnProperty columnProperty : computeColumnList) {
////                        columnProperty.setFieldType("MEASURE");
////                    }
////                    jsonObject.set("computeColumn", JSONUtil.toJsonStr(computeColumnList));
////                    DashboardIndexTextCardDO indexTextCardDO = new DashboardIndexTextCardDO();
////                    indexTextCardDO.setId(dashboardIndexTextCardDO.getId());
////                    indexTextCardDO.setSearchData(jsonObject.toString());
////                    dashboardIndexTextCardService.update(indexTextCardDO);
////                    count++;
////                }
////            } catch (Exception e) {
////                e.printStackTrace();
////            }
//        }
//        return Response.ok(count);
//    }
//
//
//    public Response reportReversion() {
//
//        List<Report> reportList = reportService.queryAll(null);
//        log.info("query all t_report records from bi, size :{}", reportList.size());
//
//        long count = 0;
//        List<Long> failedIdList = new ArrayList<>();
//        for (Report report : reportList) {
//            String computeColumn = report.getComputeColumn();
//            try {
//                if (StringUtils.isNotEmpty(computeColumn)) {
//
//                    List<ColumnProperty> computeColumnList = JSONUtil.toList(computeColumn, ColumnProperty.class);
//                    List<Long> idList = computeColumnList.stream().map(ColumnProperty::getId).collect(Collectors.toList());
//
//                    String showColumn = report.getShowColumn();
//                    if (StringUtils.isNotEmpty(showColumn) && !CollectionUtils.isEmpty(idList)) {
//                        List<ColumnProperty> showColumnPropertyList = JSONUtil.toList(showColumn, ColumnProperty.class);
//                        processComputeColumn(computeColumnList, showColumnPropertyList, idList);
//                    }
//
//                    String contrastColumn = report.getContrastColumn();
//                    if (StringUtils.isNotEmpty(contrastColumn) && !CollectionUtils.isEmpty(idList)) {
//                        List<ColumnProperty> contrastColumnList = JSONUtil.toList(contrastColumn, ColumnProperty.class);
//                        processComputeColumn(computeColumnList, contrastColumnList, idList);
//                    }
//
//                    for (Long id : idList) {
//                        for (ColumnProperty columnProperty : computeColumnList) {
//                            if (columnProperty.getId().equals(id)) {
//                                columnProperty.setFieldType("MEASURE");
//                            }
//                        }
//                    }
//
//                    Report newReport = new Report();
//                    newReport.setId(report.getId());
//                    newReport.setComputeColumn(JSONUtil.toJsonStr(computeColumnList));
//                    count++;
//                    reportService.update(newReport);
//                }
//            } catch (Exception e) {
//                failedIdList.add(report.getId());
//            }
//        }
//        return Response.ok(failedIdList);
//    }
//
//
//
//    private void processComputeColumn(List<ColumnProperty> computeColumnList, List<ColumnProperty> configList, List<Long> idList) {
//        for (ColumnProperty columnProperty : configList) {
//            for (ColumnProperty property : computeColumnList) {
//                if (columnProperty.getId().equals(property.getId()) && StringUtils.isNoneEmpty(property.getFun())) {
//                    property.setFieldType(columnProperty.getFieldType());
//                    idList.remove(columnProperty.getId());
//                }
//            }
//        }
//    }
//
//
//    public Response datasetConfigLogicEnNameReversion() {
//
//        log.info("ready to dataset config logicCnName reversion");
//        List<DatasetDo> datasetDoList = datasetDAOService.query(null);
//        log.info("need to reversion dataset list size : {}", datasetDoList.size());
//
//        long count = 0;
//        for (DatasetDo datasetDo : datasetDoList) {
//            List<DatasetElementDo> datasetElementDoList = datasetElementDAOService.query(
//                    DatasetElementQueryDTO.builder().datasetCodeList(Lists.newArrayList(datasetDo.getCode())).build());
//
//            for (DatasetElementDo datasetElementDo : datasetElementDoList) {
//
//                try {
//                    DatasetFieldQueryDTO fieldQueryDTO = new DatasetFieldQueryDTO();
//                    fieldQueryDTO.setDatasourceName(datasetDo.getDatasourceName());
//                    fieldQueryDTO.setDatasourceType(datasetDo.getDatasourceType());
//                    String[] split = datasetElementDo.getName().split("\\.");
//                    fieldQueryDTO.setDatabaseName(split[0]);
//                    fieldQueryDTO.setTableName(split[1]);
//                    List<DatasetFieldDo> datasetFieldDos = datasetFieldDAOService.find(fieldQueryDTO);
//                    Map<String, Integer> cnNameListToMap = parseCnNameListToMap(datasetFieldDos);
//
//                    DatasetFieldConfigQueryDTO configQueryDTO = DatasetFieldConfigQueryDTO.builder()
//                            .datasetCode(datasetDo.getCode()).elementCode(datasetElementDo.getCode()).build();
//                    List<DatasetConfigDo> datasetConfigDoList = datasetConfigDAOService.query(configQueryDTO);
//                    datasetConfigDoList = datasetConfigDoList.stream().sorted(Comparator.comparing(DatasetConfigDo::getId)).collect(Collectors.toList());
//
//                    for (DatasetConfigDo datasetConfigDo : datasetConfigDoList) {
//                        Long fieldId = datasetConfigDo.getFieldId();
//                        DatasetFieldQueryDTO fieldDTO = new DatasetFieldQueryDTO();
//                        HashSet<Long> set = new HashSet<>();
//                        set.add(fieldId);
//                        fieldDTO.setIdList(set);
//                        List<DatasetFieldDo> fieldDos = datasetFieldDAOService.find(fieldDTO);
//                        if (!CollectionUtils.isEmpty(fieldDos)) {
//                            String logicCnName = getLogicCnName(cnNameListToMap, fieldDos.get(0));
//                            DatasetConfigDo configDo = new DatasetConfigDo();
//                            configDo.setId(datasetConfigDo.getId());
//                            configDo.setLogicCnName(logicCnName);
//                            datasetConfigDAOService.updateById(configDo);
//                            count++;
//                        }
//                    }
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//
//        return Response.ok(count);
//    }
//
//    private Map<String, Integer> parseCnNameListToMap(List<DatasetFieldDo> metaDataFieldList) {
//        Map<String, Integer> map = new HashMap<>();
//        for (DatasetFieldDo datasetFieldDo : metaDataFieldList) {
//            String cnName = null;
//            if (StringUtils.isNotEmpty(datasetFieldDo.getFieldCnName())) {
//                cnName = datasetFieldDo.getFieldCnName();
//            } else {
//                cnName = datasetFieldDo.getFieldComment();
//            }
//            Integer count = map.getOrDefault(cnName, 0);
//            count++;
//            map.put(datasetFieldDo.getFieldCnName(), count);
//        }
//        return map;
//    }
//
//
//    private String getLogicCnName(Map<String, Integer> parseCnNameMap, DatasetFieldDo datasetFieldDo) {
//        String cnName = null;
//        if (StringUtils.isNotEmpty(datasetFieldDo.getFieldCnName())) {
//            cnName = datasetFieldDo.getFieldCnName();
//        } else {
//            cnName = datasetFieldDo.getFieldComment();
//        }
//
//        Integer count = parseCnNameMap.get(cnName);
//        if (count == null || count == 1) {
//            parseCnNameMap.remove(cnName);
//            return cnName;
//        } else {
//            count--;
//            parseCnNameMap.put(cnName, count);
//            return cnName + "_" + count;
//        }
//    }
//}
