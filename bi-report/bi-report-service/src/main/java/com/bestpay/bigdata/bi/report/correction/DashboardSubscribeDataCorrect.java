package com.bestpay.bigdata.bi.report.correction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.subscribe.ObjectSubscribeService;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardCardDO;
import com.bestpay.bigdata.bi.report.enums.dashboard.DashboardTypeCodeEnum;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DashboardSubscribeDataCorrect {
    @Resource
    private ObjectSubscribeService objectSubscribeService;

    @Resource
    private DashboardDaoService dashboardDaoService;

    @Resource
    private DashboardCardService dashboardCardService;

    public void dataCorrection() {

        String tab = "tab-";
        List<DashboardCardDO>  cardDOList =  dashboardCardService.queryCardUniqueKeyIsNull();
        if (CollUtil.isNotEmpty(cardDOList)){
            Map<Long, List<DashboardCardDO>> dashboardIdMap = cardDOList.stream().collect(Collectors.groupingBy(DashboardCardDO::getDashboardId));
            dashboardIdMap.forEach((dashboardId,cardDOS)->{
                log.info("start dataCorrection()");
                for (int i = 0; i < cardDOS.size(); i++) {
                    DashboardCardDO dashboardCardDO = cardDOS.get(i);
                    dashboardCardDO.setCardUniqueKey(tab + UUID.randomUUID());
                    if (Objects.isNull(dashboardCardDO.getOrderNo())){
                        dashboardCardDO.setOrderNo(i);
                    }
                    dashboardCardService.updateById(dashboardCardDO);
                }
            });
        }

        log.info("数据订正中......");
        List<ObjectSubScribeDO> objectSubScribeDOList = objectSubscribeService.queryAll();
        if (CollUtil.isNotEmpty(objectSubScribeDOList)){
            List<Long> dashboardIdList = objectSubScribeDOList.stream().map(ObjectSubScribeDO::getObjectId).distinct().collect(Collectors.toList());
            List<Dashboard> dashboardList = dashboardDaoService.queryByIdList(dashboardIdList);
            if (CollUtil.isNotEmpty(dashboardList)){
                // 单仪表板
                List<Long> singleTabDashboardIdList = new ArrayList<>();
                // 多tab仪表板
                List<Long> moreTabDashboardIdList = new ArrayList<>();
                for (Dashboard dashboard : dashboardList) {
                    Long dashboardId = dashboard.getId();
                    if (Objects.equals(dashboard.getTypeCode(), DashboardTypeCodeEnum.DASHBOARD.getCode())){
                        // 单仪表板
                        if (dashboard.getIsTabDashboard()== 0){
                            singleTabDashboardIdList.add(dashboardId);
                        }else {
                            // 多仪表板
                            moreTabDashboardIdList.add(dashboardId);
                        }
                    }
                }
                List<ObjectSubScribeDO> toUpdateSub = new ArrayList<>();
                // 处理 单仪表板
                if (CollUtil.isNotEmpty(singleTabDashboardIdList)){
                    List<DashboardCardDO> dashboardCardDOList = dashboardCardService.findByIdsAndCardType(singleTabDashboardIdList, NewCardTypeEnum.TAB);
                    for (DashboardCardDO dashboardCardDO : dashboardCardDOList) {
                        toUpdateSub.add(buildDashboardSubScribeDO(dashboardCardDO));
                    }
                }
                // 处理 多仪表板
                if (CollUtil.isNotEmpty(moreTabDashboardIdList)){
                    List<DashboardCardDO> dashboardCardDOList = dashboardCardService.findByIdsAndCardType(moreTabDashboardIdList, NewCardTypeEnum.TAB);
                    Map<Long, List<DashboardCardDO>> dashboardIdCardMap = dashboardCardDOList.stream().collect(Collectors.groupingBy(DashboardCardDO::getDashboardId));
                    dashboardIdCardMap.forEach((dashboardId,dashboardCarList)-> toUpdateSub.add(buildDashboardSubScribeDO(dashboardCarList.get(0))));
                }
                for (ObjectSubScribeDO subScribeDO : toUpdateSub) {
                    objectSubscribeService.updateByDashboardId(subScribeDO);
                }

            }
        }
        log.info("数据订正已完成...");


    }


    private ObjectSubScribeDO buildDashboardSubScribeDO(DashboardCardDO dashboardCardDO){
        ObjectSubScribeDO subScribeDO = new ObjectSubScribeDO();
        subScribeDO.setObjectId(dashboardCardDO.getDashboardId());
        subScribeDO.setUpdatedAt(new Date());
        subScribeDO.setSubscribeContent(JSONUtil.toJsonStr(Stream.of(dashboardCardDO.getCardUniqueKey()).collect(Collectors.toList())));
        return subScribeDO;
    }
}
