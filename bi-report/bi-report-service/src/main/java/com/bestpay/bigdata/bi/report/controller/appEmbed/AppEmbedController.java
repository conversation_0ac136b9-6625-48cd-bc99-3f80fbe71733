package com.bestpay.bigdata.bi.report.controller.appEmbed;

import static com.bestpay.bigdata.bi.common.constant.BIConstant.EMAIL;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedListRequest;
import com.bestpay.bigdata.bi.report.request.embed.AppEmbedRequest;
import com.bestpay.bigdata.bi.report.response.embed.AppEmbedVO;
import com.bestpay.bigdata.bi.report.service.embed.ReportAppEmbedService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import com.google.common.base.Throwables;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: bj
 * @date: 2022/07/27
 */
@Slf4j
@RestController
@RequestMapping("/biReport/appEmbed")
@Api(value = "应用嵌入", tags = "应用嵌入")
public class AppEmbedController {

    @Resource
    private ReportAppEmbedService reportAppEmbedService;
    @Resource
    private AuthorityCheckUtil authorityCheckUtil;
    @Resource
    private DashboardDaoService dashboardDaoService;


    @PostMapping(value = {"/getAppKey/{id}"})
    @ApiOperation(value="获取AppKey接口", httpMethod="POST",response = Response.class)
    public Response<String> getAppKey(@PathVariable Long id) {
        return reportAppEmbedService.getKey(id);
    }

    @PostMapping("t_datascreen_main")
    @ApiOperation(value="数据大屏分享", httpMethod="POST",response = Response.class)
    public Response<String> completeAppEmbed(@RequestBody AppEmbedRequest appEmbedRequest) {
        log.debug("completeAppEmbed:{}", appEmbedRequest.toString());

        // 只有责任人才能分享
        Dashboard dashboard = dashboardDaoService.getById(appEmbedRequest.getEmbedObjectId());
        authorityCheckUtil.checkOwner(EMAIL, dashboard.getOwnerEmail());

        return reportAppEmbedService.completeAppEmbed(appEmbedRequest);
    }


    @PostMapping("/insertAppEmbed")
    @ApiOperation(value="应用嵌新增", httpMethod="POST",response = Response.class)
    public Response<Long> insertAppEmbed(@RequestBody AppEmbedRequest appEmbedRequest) {
        log.debug("insertAppEmbed:{}", appEmbedRequest.toString());
        return reportAppEmbedService.insertAppEmbed(appEmbedRequest);
    }



    @PostMapping("/updateAppEmbed")
    @ApiOperation(value="应用嵌修改", httpMethod="POST",response = Response.class)
    public Response<Boolean> updateAppEmbed(@RequestBody AppEmbedRequest appEmbedRequest) {
        log.debug("updateAppEmbed:{}", JSONUtil.toJsonStr(appEmbedRequest));
        return reportAppEmbedService.updateAppEmbedById(appEmbedRequest);
    }


    @PostMapping("/deleteAppEmbed")
    @ApiOperation(value="应用嵌删除", httpMethod="POST",response = Response.class)
    public Response<Boolean> deleteAppEmbed(@RequestBody AppEmbedRequest appEmbedRequest) {
        if(appEmbedRequest==null || appEmbedRequest.getId()==null){
            log.warn("deleteAppEmbed, id 为空");
            return Response.ok(false);
        }

        appEmbedRequest.setStatusCode(StatusCodeEnum.DELETE.getCode());
        log.debug("deleteAppEmbed:{}", appEmbedRequest);
        return reportAppEmbedService.updateAppEmbedById(appEmbedRequest);
    }


    @PostMapping("/onLineAppEmbed")
    @ApiOperation(value="应用嵌上线", httpMethod="POST",response = Response.class)
    public Response<Boolean> onLineAppEmbed(@RequestBody AppEmbedRequest appEmbedRequest) {
        log.debug("onLineAppEmbed:{}", appEmbedRequest.toString());
        appEmbedRequest.setStatusCode(StatusCodeEnum.ONLINE.getCode());
        return reportAppEmbedService.updateAppEmbedById(appEmbedRequest);
    }


    @PostMapping("/offLineAppEmbed")
    @ApiOperation(value="应用嵌下线", httpMethod="POST",response = Response.class)
    public Response<Boolean> offLineAppEmbed(@RequestBody AppEmbedRequest appEmbedRequest) {
        log.debug("onLineAppEmbed:{}", appEmbedRequest.toString());
        appEmbedRequest.setStatusCode(StatusCodeEnum.OFFLINE.getCode());
        return reportAppEmbedService.updateAppEmbedById(appEmbedRequest);
    }


    @PostMapping("/queryAppEmbedList")
    @ApiOperation(value="查询应用嵌入列表", httpMethod="POST",response = Response.class)
    public Response<PageQueryVO<AppEmbedVO>> queryAppEmbedListV2(@RequestBody AppEmbedListRequest request) {
        log.debug("/queryAppEmbedListV2, request:{}", JSONUtil.toJsonStr(request));

        try {
            Response<PageQueryVO<AppEmbedVO>> result
                    = reportAppEmbedService.queryAppEmbedListV2(request);

            log.debug("/queryAppEmbedListV2, result:{}", JSONUtil.toJsonStr(result));

            return result;

        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));
            throw e;
        }
    }


    @PostMapping("/getEncryptCodeUrl")
    @ApiOperation(value = "数据大屏分享-查看代码", httpMethod = "POST", response = Response.class)
    public Response<String> getEncryptCodeUrl(@RequestBody AppEmbedRequest appEmbedRequest) {
        return reportAppEmbedService.getEncryptCodeUrl(appEmbedRequest);
    }
}
