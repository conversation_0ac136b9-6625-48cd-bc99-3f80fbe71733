package com.bestpay.bigdata.bi.report.controller.dataset;

import cn.hutool.core.util.StrUtil;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.bean.dataset.ConfigRefreshVO;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetConfigVO;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetDetailVO;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetVO;
import com.bestpay.bigdata.bi.report.request.dataset.ComputeColumnGrammarCheckRequest;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetColumnConfigRequest;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetConfigRequest;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetFirstAddRequest;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetListRequest;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetPublishRequest;
import com.bestpay.bigdata.bi.report.request.dataset.MetaSyncRequest;
import com.bestpay.bigdata.bi.report.request.dataset.MetadataRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetMetaDataService;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.bestpay.bigdata.bi.report.util.HudiAdapterUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2023-07-05-10:37
 */
@RestController
@Slf4j
@RequestMapping("/biReport/dataset")
@Api(value = "数据集", tags = "数据集")
public class DatasetController {


    @Resource
    private DatasetService datasetService;

    @Resource
    private DatasetMetaDataService datasetMetaDataService;

    /**
     * 新增
     */
    @PostMapping("/first")
    @ApiOperation(httpMethod = "POST", value = "新增", notes = "新增")
    public Response<DatasetVO> firstAddDataset(@RequestBody DatasetFirstAddRequest request) {
        return datasetService.firstAddDataset(request);
    }


    /**
     * 备注：前端无需调用，用于后端查数据使用
     */
    @GetMapping("/all/{code}")
    @ApiOperation(httpMethod = "GET", value = "前端无需调用，用于后端查数据使用", notes = "前端无需调用，用于后端查数据使用")
    public Response<String> datasetInfo(@PathVariable String code) {
        return datasetService.datasetInfo(code);
    }


    /**
     * 数据集配置刷新
     */
    @PostMapping("/configRefresh")
    @ApiOperation(httpMethod = "POST", value = "数据集配置刷新", notes = "数据集配置刷新")
    public Response<ConfigRefreshVO> datasetConfigRefresh(@RequestBody DatasetConfigRequest request,
                                                          HttpServletRequest httpServletRequest) {

        HudiAdapterUtil.adapterDatasetConfigRefresh(request);

        ConfigRefreshVO refreshVO = new ConfigRefreshVO();
        Response<List<DatasetConfigVO>> listResponse = datasetService.configRefresh(request);
        List<DatasetConfigVO> data = listResponse.getData();
        refreshVO.setDatasetConfigVOList(data);
        refreshVO.setComputeColumnList(datasetService.computeColumn(request));
        return Response.ok(refreshVO);
    }


    /**
     * 获取数据源类型 和 数据源名称列表
     * @return
     */
    @GetMapping("/getDatasourceList")
    @ApiOperation(httpMethod = "GET", value = "获取数据源类型 和 数据源名称列表", notes = "获取数据源类型 和 数据源名称列表")
    public Response<Map<String, List<String>>> getDatasourceList(){
        return datasetService.getDatasourceList();
    }


    /**
     * 数据预览
     */
    @PostMapping("/dataPreview")
    @ApiOperation(httpMethod = "POST", value = "数据预览", notes = "数据预览")
    public Response<Map<String, Object>> datasetDataPreview(@RequestBody DatasetConfigRequest request) {

        HudiAdapterUtil.adapterDatasetDataPreview(request);

        return datasetService.dataPreview(request);
    }


    /**
     * 元数据同步
     * @param request
     * @return
     */
    @PostMapping("/metaSync")
    @ApiOperation(httpMethod = "POST", value = "元数据同步", notes = "元数据同步")
    public Response<String> metaSync(@RequestBody MetaSyncRequest request) {
        return datasetMetaDataService.metaSync(request);
    }

    /**
     * 上线、下线、删除
     */
    @PostMapping("/status")
    @ApiOperation(httpMethod = "POST", value = "上线、下线、删除", notes = "上线、下线、删除")
    public Response<Boolean> updateStatus(@RequestBody DatasetConfigRequest datasetConfigRequest) {

        HudiAdapterUtil.adapterDatasetUpdateStatus(datasetConfigRequest);

        return datasetService.updateStatus(datasetConfigRequest);
    }

    /**
     * 详情接口
     * @param id
     * @return
     */
    @GetMapping("/detail")
    @ApiOperation(httpMethod = "GET", value = "详情接口", notes = "详情接口")
    public Response<DatasetDetailVO> detail(@RequestParam("id") Long id) {
        // 刷新缓存数据
        datasetService.refresh(id);
        return datasetService.detail(id);
    }

    /**
     * 编辑发布
     * @param request
     * @return
     */
    @PostMapping("/publish")
    @ApiOperation(httpMethod = "POST", value = "编辑发布", notes = "编辑发布")
    public Response<String> publish(@RequestBody DatasetPublishRequest request) {

        return datasetService.publish(request);
    }


    /**
     * 检查计算字段语法信息
     * @param request
     * @return
     */
    @PostMapping("/checkGrammar")
    @ApiOperation(httpMethod = "POST", value = "检查计算字段语法信息", notes = "检查计算字段语法信息")
    Response checkComputeGrammar(@RequestBody ComputeColumnGrammarCheckRequest request) {

        HudiAdapterUtil.adapterCheckComputeGrammar(request);

        return datasetService.checkComputeGrammar(request);
    }


    /**
     * 列表接口
     * @param request
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(httpMethod = "POST", value = "列表接口", notes = "列表接口")
    public Response<PageQueryVO<DatasetVO>> list(@RequestBody DatasetListRequest request) {
        if (StrUtil.isBlank(request.getSortField())){
            request.setSortField("updatedAt");
        }
        if (StrUtil.isBlank(request.getSortRule())){
            request.setSortRule("DESC");
        }
        request.setSortRule(request.getSortRule().toUpperCase());

        Response<PageQueryVO<DatasetVO>> data = datasetService.list(request);

        return data;
    }

    /**
     * 数据集名称列表
     * @return
     */
    @GetMapping("/nameList")
    @ApiOperation(httpMethod = "GET", value = "数据集名称列表", notes = "数据集名称列表")
    public Response<List<String>> nameList() {
        return datasetService.nameList();
    }


    /**
     * 查询元数据 库列表信息
     * @param metadataRequest
     * @return
     */
    @PostMapping("/database")
    @ApiOperation(httpMethod = "POST", value = "查询元数据 库列表信息", notes = "查询元数据 库列表信息")
    public Response<List<String>> queryMetaDatabases(@RequestBody MetadataRequest metadataRequest) {

        // 适配hudi数据源
        HudiAdapterUtil.adapterDatesetDbQuery(metadataRequest);

        return datasetMetaDataService.queryMetaDatabases(metadataRequest);
    }


    /**
     * 查询元数据 表列表信息
     * @param metadataRequest
     * @return
     */
    @PostMapping("/table")
    @ApiOperation(httpMethod = "POST", value = "查询元数据 表列表信息", notes = "查询元数据 表列表信息")
    public Response<List<String>> queryMetaDataTables(@RequestBody MetadataRequest metadataRequest) {

        HudiAdapterUtil.adapterQueryMetaDataTables(metadataRequest);

        return datasetMetaDataService.queryMetaDataTables(metadataRequest);
    }

    @PostMapping("/getColumnConfigList")
    @ApiOperation(httpMethod = "POST", value = "获取列信息", notes = "获取列信息")
    public Response<List<DatasetColumnConfigDTO>> getColumnConfigList(@RequestBody DatasetColumnConfigRequest request) {
        return datasetService.getColumnConfigList(request);
    }



}
