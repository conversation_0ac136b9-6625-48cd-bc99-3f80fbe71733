package com.bestpay.bigdata.bi.report.refactor.service.impl;

import com.bestpay.bigdata.bi.report.download.bean.DownloadContext;
import com.bestpay.bigdata.bi.report.refactor.service.DownloadService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class DownloadServiceImpl implements DownloadService {

    @Override
    public List<List<String>> decryptSensitiveFields() {
        return Collections.emptyList();
    }

    @Override
    public DownloadContext buildDownloadContext() {
        return null;
    }

    @Override
    public String writeToLocalFile() {
        return "";
    }

    @Override
    public String uploadToRemote() {
        return "";
    }
}
