package com.bestpay.bigdata.bi.report.refactor.processor.validator;

import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.request.auth.ObjectAuthRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DatascreenValidator extends AbstractScenceValidator {

    @Override
    public RequestKey supportedKey() {
        return new RequestKey(SceneType.DATASCREEN, ProcessorType.QUERY);
    }

    @Override
    protected ObjectAuthRequest buildAuthRequest(ProcessContext context) {
        return null;
    }

}
