package com.bestpay.bigdata.bi.report.controller.dataset;

import cn.hutool.core.util.StrUtil;
import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.enums.dataset.DataSetResourceEnums;
import com.bestpay.bigdata.bi.report.request.dataset.DataSetResourceRequest;
import com.bestpay.bigdata.bi.report.request.dataset.ResNameRequest;
import com.bestpay.bigdata.bi.report.request.dataset.ResourceListDetailRequest;
import com.bestpay.bigdata.bi.report.request.dataset.SkipPermissionRequest;
import com.bestpay.bigdata.bi.report.response.dataset.DataSetResourceVO;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetResService;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;


@RestController
@Slf4j
@RequestMapping("/biReport/dataset/res/")
@Api(value = "数据集血缘资源", tags = "数据集血缘资源")
public class DatasetResController {


    @Resource
    private DatasetResService datasetResService;

    @PostMapping("list")
    @ApiOperation(httpMethod = "POST", value = "血缘资源列表", notes = "血缘资源列表")
    public Response<PageQueryVO<DataSetResourceVO>> resList(@RequestBody @Valid DataSetResourceRequest request) {
        String resType = request.getResType();
        if (StrUtil.isNotBlank(resType)){
            Preconditions.checkArgument(Objects.nonNull(DataSetResourceEnums.getByCode(resType)), "资源类型不存在");
        }
        if (StrUtil.isBlank(request.getSortField())){
            request.setSortField("createdAt");
            request.setSortRule("DESC");
        }
        PageQueryVO<DataSetResourceVO> queryVO = datasetResService.resList(request);
        return Response.ok(queryVO);

    }

    @PostMapping("detail")
    @ApiOperation(httpMethod = "POST", value = "资源详情", notes = "资源详情")
    public Response<List<DataSetResourceVO>> resDetail(@RequestBody @Valid ResourceListDetailRequest request) {
        String resType = request.getResType();
        Preconditions.checkArgument(DataSetResourceEnums.NEW_DATA_SCREEN.getCode().equals(resType)
                || DataSetResourceEnums.DASHBOARD.getCode().equals(resType)  , "仅支持数据大屏和仪表板查看资源详情");
        List<DataSetResourceVO> resourceVOList = datasetResService.resDetail(request);
        return  Response.ok(resourceVOList);

    }


    @PostMapping("resNameList")
    @ApiOperation(httpMethod = "POST", value = "资源名称", notes = "资源名称")
    public Response<List<String>> resNameList(@RequestBody @Valid  ResNameRequest request) {
        List<String> names = datasetResService.resNameList(request);
        return  Response.ok(names);

    }
    @PostMapping("skip/permission")
    @ApiOperation(httpMethod = "POST", value = "权限", notes = "权限")
    public Response<Void> resSkipPermission (@RequestBody @Valid  SkipPermissionRequest request) {
        datasetResService.resSkipPermission(request);
        return  Response.ok();

    }


}
