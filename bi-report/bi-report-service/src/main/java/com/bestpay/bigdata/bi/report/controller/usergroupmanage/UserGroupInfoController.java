package com.bestpay.bigdata.bi.report.controller.usergroupmanage;


import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserGroupDetailResponse;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserGroupDirResponse;
import com.bestpay.bigdata.bi.report.usermanage.bean.UserGroupTreeResponse;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupDetailRequest;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupDirRequest;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupInfoRequest;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupRemoveRequest;
import com.bestpay.bigdata.bi.report.usermanage.request.UserGroupUpdateRequest;
import com.bestpay.bigdata.bi.report.usermanage.service.UserGroupManagerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户组信息
 * @author: lm
 */
@RestController
@RequestMapping("/biReport/user/group")
@Api(value = "用户组信息", tags = "用户组信息")
public class UserGroupInfoController {

    @Resource
    private UserGroupManagerService userGroupManagerService;


    /**
     * 用户组新增接口
     *
     * @return
     */
    @ApiOperation(value = "用户组新增接口", httpMethod = "POST", notes = "用户组新增接口")
    @PostMapping("/add")
    public Response addUserGroupInfo(@RequestBody @Validated UserGroupInfoRequest userGroupInfoRequest){
        return Response.ok(userGroupManagerService.addUserGroupInfo(userGroupInfoRequest));
    }

    @RequestMapping("/detail")
    @ApiOperation(httpMethod = "POST", value = "用户组详情信息", notes = "用户组详情信息")
    public Response<UserGroupDetailResponse> userGroupInfoDetail(@RequestBody @Validated UserGroupDetailRequest request){
        UserGroupDetailResponse detailResponse = userGroupManagerService.userGroupInfoDetail(request);
        return Response.ok(detailResponse);

    }

    @RequestMapping("/remove")
    @ApiOperation(httpMethod = "POST", value = "删除用户或者用户组", notes = "删除用户或者用户组")
    public Response<Void> remove(@RequestBody @Validated UserGroupRemoveRequest request) {
        userGroupManagerService.remove(request);
        return Response.ok();
    }

    /**
     * 用户组目录树查询
     */
    @ApiOperation(httpMethod = "POST", value = "用户组目录树查询", notes = "用户组目录树查询")
    @PostMapping("/directory")
    public Response<List<UserGroupDirResponse>> userGroupInfoTree(@RequestBody @Validated UserGroupDirRequest request){
        return Response.ok(userGroupManagerService.userGroupInfoTree(request));
    }

    @RequestMapping("/update")
    @ApiOperation(httpMethod = "POST", value = "更新用户组", notes = "更新用户组")
    public Response<Void> update(@RequestBody @Validated UserGroupUpdateRequest request) {
        userGroupManagerService.update(request);
        return Response.ok();
    }



    @RequestMapping("/searchTree")
    @ApiOperation(httpMethod = "POST", value = "用户信息组树状查询", notes = "用户信息组树状查询")
    public Response<List<UserGroupTreeResponse>> searchTree() {
        return Response.ok( userGroupManagerService.searchTree());
    }

}
