package com.bestpay.bigdata.bi.report.controller.dashboard;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.request.tablecard.IndexCardListRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.IndexCardRequest;
import com.bestpay.bigdata.bi.report.response.dashboard.IndexCardVO;
import com.bestpay.bigdata.bi.report.service.report.EmbedIndexCardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/biReport/tableCard")
@Api(value = "嵌入指标文本", tags = "嵌入指标文本")
public class EmbedIndexCardController {

    @Resource
    private EmbedIndexCardService embedIndexCardService;

    @PostMapping("/addOrUpdateIndexCard")
    @ApiOperation(value = "新增嵌入指标文本卡片", httpMethod = "POST", response = Response.class)
    public Response<Long> publishTableCard(@RequestBody IndexCardRequest request) {
        return embedIndexCardService.addOrUpdateIndexCard(request);
    }

    @GetMapping("/indexCardDetail")
    @ApiOperation(value = "查询嵌入指标文本详情", httpMethod = "GET", response = Response.class)
    public Response<IndexCardVO> indexCardDetail(@RequestParam("id") Long id) {
        return embedIndexCardService.indexCardDetail(id);
    }

    @PostMapping("/getIndexCardList")
    @ApiOperation(value = "嵌入指标文本详情", httpMethod = "POST", response = Response.class)
    public Response<List<IndexCardVO>> indexCardDetail(@RequestBody IndexCardListRequest request) {
        return embedIndexCardService.indexCardList(request);
    }

}
