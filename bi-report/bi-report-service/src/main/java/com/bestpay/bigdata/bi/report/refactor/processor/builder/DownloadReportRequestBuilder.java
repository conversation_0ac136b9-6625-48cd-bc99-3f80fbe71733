package com.bestpay.bigdata.bi.report.refactor.processor.builder;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.database.api.report.ReportService;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.request.report.DownloadApplyRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class DownloadReportRequestBuilder extends ReportRequestBuilder {

    @Autowired
    private ReportService reportService;

    @Override
    public RequestKey supportedKey() {
        return new RequestKey(SceneType.REPORT, ProcessorType.DOWNLOAD);
    }

    @Override
    protected void preHandleDownloadChooseColumn(ProcessContext context) {

        DownloadApplyRequest reportRequest = (DownloadApplyRequest) context.getRawRequest();

        // 兼容直接下载场景
        if (reportRequest.getChooseColumnList() == null) {
            reportRequest.setChooseColumnList(new ArrayList<>());
        }

        // 获取报表信息
        Report dbReport = updateService.queryById(reportRequest.getReportId());

        // 判断选择字段是否包含加密字段
        containsSensitiveFields(dbReport, reportRequest);

        // 根据下载选择字段对报表进行排序
        sortChooseColumnList(reportRequest, dbReport);
    }

    @Override
    protected void buildReportRequest(ProcessContext context) {

        DownloadApplyRequest reportRequest = (DownloadApplyRequest) context.getRawRequest();

        // 报表下载场景，处理加密字段
        Report report = reportService.queryById(reportRequest.getReportId());
        reportRequest.setFileContainSensitiveInfo(report.getFileContainSensitiveInfo());
        reportRequest.setSensitiveFields(report.getSensitiveFields());

        // 处理分页参数
        reportRequest.setPageNum(null);
        reportRequest.setPageSize(null);

        context.setRawRequest(reportRequest);

        super.buildReportRequest(context);
    }

    @Override
    protected void postHandleDownloadChooseColumn(ProcessContext context) {
        DownloadApplyRequest downloadApplyRequest = (DownloadApplyRequest) context.getRawRequest();
        ReportRequest reportRequest = context.getReportRequest();

        // 标记非选择字段为隐藏字段
        markUnChooseColumnAsHidden(downloadApplyRequest, reportRequest);
    }

    /**
     * 判断报表是否包含加密字段，对reportRequest的FileContainSensitiveInfo进行赋值，为后续是否加密压缩做准备
     *
     * @param report
     * @param reportRequest
     */
    private void containsSensitiveFields(Report report, DownloadApplyRequest reportRequest) {
        Integer fileContainSensitiveInfo = report.getFileContainSensitiveInfo();
        String sensitiveFields = report.getSensitiveFields();
        if (StrUtil.isBlank(sensitiveFields) || fileContainSensitiveInfo == 0) {
            return;
        }

        JSONArray jsonArray = JSONUtil.parseArray(sensitiveFields);
        List<String> sensitiveFieldList = jsonArray.stream()
                .filter(Objects::nonNull)
                .filter(JSONObject.class::isInstance)
                .map(JSONObject.class::cast)
                .map(obj -> obj.getStr("uuid"))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(sensitiveFieldList)) {
            DownloadApplyRequest.judgeJiraProcess(reportRequest, sensitiveFieldList);
        }
    }
}
