package com.bestpay.bigdata.bi.report.refactor.processor.builder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.report.chart.handler.util.ReportOrderTypeEnum;
import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.request.report.QueryReportRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.service.report.ReportUpdateService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ReportRequestBuilder extends AbstractSceneRequestBuilder {

    @Autowired
    protected ReportUpdateService updateService;

    @Override
    public RequestKey supportedKey() {
        return new RequestKey(SceneType.REPORT, ProcessorType.QUERY);
    }

    @Override
    protected void buildReportRequest(ProcessContext context) {
        QueryReportRequest reportRequest = (QueryReportRequest) context.getRawRequest();

        ReportDetailVO detailVO = updateService.queryReportTemplate(reportRequest.getReportId()).getData();

        ReportRequest request = convertReportRequest(reportRequest, detailVO);

        // 报表默认排序 + 自定义排序
        if (CollUtil.isNotEmpty(reportRequest.getOrderColumns())) {
            request.setOrderColumnList(reportRequest.getOrderColumns()); // 自定义排序
        }

        // 关键字
        if (StringUtil.isNotEmpty(reportRequest.getKeyword())) {
            request.setQueryKeyword(reportRequest.getKeyword());
        }

        // 报表特殊排序 传默认值
        request.setOrderType(ReportOrderTypeEnum.DEFAULT.getType());

        // 设置上下文
        DatasetInfo datasetInfo = request.getDatasetInfoList().get(0);
        context.setDatasetColumnConfigList(getDatasetColumnConfig(datasetInfo.getDatasetId()));
        context.setReportRequest(request);
    }


    public ReportRequest convertReportRequest(QueryReportRequest reportRequest, ReportDetailVO detailVO) {
        ReportRequest report = JSONUtil.toBean(JSONUtil.toJsonStr(detailVO), ReportRequest.class);
        BeanUtils.copyProperties(reportRequest, report);
        return report;
    }
}
