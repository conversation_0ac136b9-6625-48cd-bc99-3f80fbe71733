package com.bestpay.bigdata.bi.report.usermanage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.bestpay.bigdata.bi.common.api.RedisService;
import com.bestpay.bigdata.bi.common.bean.aiapi.ApiPlusUserInfo;
import com.bestpay.bigdata.bi.common.bean.aiapi.ChannelInfoVO;
import com.bestpay.bigdata.bi.common.bean.aiapi.NewUserInfoRequest;
import com.bestpay.bigdata.bi.common.common.AIPlusPageable;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.ApiPlusChannelEnum;
import com.bestpay.bigdata.bi.common.util.RsaUtil;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.dao.usergroupmanage.UserManageUserInfoDO;
import com.bestpay.bigdata.bi.report.usermanage.service.AiPlusUserManageService;
import com.bestpay.bigdata.bi.report.usermanage.service.DbUserManageService;
import com.bestpay.bigdata.bi.report.usermanage.service.UserDataSyncService;
import com.bestpay.bigdata.bi.thirdparty.extcrypto.ExtcryptoService;
import com.bestpay.bigdata.bi.thirdparty.extcrypto.impl.ExcryptoServiceImpl;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

/**
 * 用户数据同步服务实现 负责从AI+平台同步用户数据到本地数据库（支持全量同步和单用户同步）
 *
 * @author: limin
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDataSyncServiceImpl implements UserDataSyncService {

  // 临时线程池配置（针对低频调用场景）
  /**
   * 核心线程数
   */
  private static final int CORE_POOL_SIZE = 1;

  /**
   * 最大线程数
   */
  private static final int MAX_POOL_SIZE = 3;

  /**
   * 队列容量
   */
  private static final int QUEUE_CAPACITY = 10;

  /**
   * 线程池名称前缀
   */
  private static final String THREAD_NAME_PREFIX = "user-sync-";

  /**
   * 等待终止时间 1h
   */
  private static final int AWAIT_TERMINATION_SECONDS = 60 * 60;
  /**
   * 同步锁TTL，10分钟
   */
  private static final int SYNC_LOCK_TTL = 10 * 60 * 1000;

  /**
   * 同步锁KEY
   */
  private static final String SYNC_LOCK_KEY = "aiplus:user:sync:lock";

  @Resource
  private RedisService redisService;

  @Resource
  private AiPlusUserManageService aiPlusUserManageService;

  @Resource
  private ApolloRefreshConfig apolloRefreshConfig;

  @Resource
  private DbUserManageService dbUserManageService;

  @Resource
  private ExtcryptoService extcryptoService;


  /**
   * 全量同步（异步执行，按需创建线程池）
   */
  @Override
  public void userDataSync() {
    Executor executor = createTempThreadPool();
    executor.execute(() -> {
      try {
        doFullSync();
      } finally {
        shutdownThreadPool(executor);
      }
    });
  }


  /**
   * 单用户同步（异步执行，按需创建线程池）
   */
  @Override
  public void syncUserByAccount(String accountName) {
    if (StringUtil.isEmpty(accountName)) {
      log.warn("单用户同步失败：账户名为空");
      return;
    }

    Executor executor = createTempThreadPool();
    executor.execute(() -> {
      try {
        doSingleUserSync(accountName);
      } finally {
        shutdownThreadPool(executor);
      }
    });
  }

  /**
   * 全量同步核心逻辑（带分布式锁）
   */
  private void doFullSync() {
    // 尝试获取分布式锁
    Boolean lockHolder = null;
    try {
      lockHolder = acquireLock();
      if (lockHolder == null || !lockHolder) {
        log.error("全量同步：获取分布式锁失败，已有其他进程执行");
        return;
      }

      log.debug("全量同步：开始执行（线程：{}）", Thread.currentThread().getName());
      UserInfo operator = UserContextUtil.getUserInfo();
      // 查询本地已存在的用户ID
      Set<String> existingOneIds = dbUserManageService.queryExistingOneIds(null);

      AtomicInteger totalSynced = new AtomicInteger(0);
      // 分页处理全量数据
      processAllPages(existingOneIds, operator, totalSynced);

      log.debug("全量同步：完成，共同步 {} 条记录", totalSynced.get());
    } catch (Exception e) {
      log.error("全量同步：执行失败", e);
    } finally {
      if (lockHolder != null && lockHolder) {
        redisService.unlock(SYNC_LOCK_KEY);
        log.debug("已释放同步锁");
      }
    }
  }

  /**
   * 单用户同步核心逻辑（带分布式锁）
   */
  private void doSingleUserSync(String accountName) {
    // 尝试获取分布式锁
    Boolean lockHolder = null;
    try {
      lockHolder = acquireLock();
      if (lockHolder == null || !lockHolder) {
        log.error("单用户同步[{}]：获取分布式锁失败，已有其他进程执行", accountName);
        return;
      }

      log.debug("单用户同步[{}]：开始执行（线程：{}）", accountName, Thread.currentThread().getName());
      UserInfo operator = UserContextUtil.getUserInfo();
      // 查询本地已存在的用户ID
      Set<String> existingOneIds = dbUserManageService.queryExistingOneIds(accountName);

      int syncedCount = processSingleAccount(accountName, existingOneIds, operator);
      log.debug("单用户同步[{}]：完成，同步结果：{}条", accountName, syncedCount);
    } catch (Exception e) {
      log.error("单用户同步[{}]：执行失败", accountName, e);
    } finally {
      if (lockHolder != null && lockHolder) {
        redisService.unlock(SYNC_LOCK_KEY);
        log.debug("已释放同步锁");
      }
    }
  }


  /**
   * 分页处理全量用户数据
   */
  private void processAllPages(Set<String> existingOneIds, UserInfo operator, AtomicInteger totalSynced) {
    int pageNum = 1;
    int pageSize = apolloRefreshConfig.getUserDataSyncBatchSize();
    Integer totalPage = 10000;

    while (pageNum <= totalPage) {
      // 获取单页数据（空结果或异常时容错处理）
      AIPlusPageable<ApiPlusUserInfo> pageResult = fetchPageData(pageNum, pageSize);

      if (pageNum == 1) {
        totalPage = pageResult.getPage();
      }
      // 处理空页或无数据的情况
      if (pageResult == null || CollectionUtils.isEmpty(pageResult.getRecords())) {
        // 若已无下一页，终止循环
        if (!hasNextPage(pageNum, totalPage)) {
          log.debug("全量同步：第{}页无数据且无下一页，终止分页", pageNum);
          break;
        }
        // 若有下一页，继续下一页
        log.debug("全量同步：第{}页无数据，继续下一分页", pageNum);
        pageNum++;
        continue;
      }

      // 处理当前页数据
      int pageSynced = processPageRecords(pageResult.getRecords(), existingOneIds, operator);
      totalSynced.addAndGet(pageSynced);
      log.debug("全量同步：第{}页处理完成，同步{}条", pageNum, pageSynced);

      // 判断是否有下一页
      if (!hasNextPage(pageNum, totalPage)) {
        break;
      }
      pageNum++;
    }
  }

  /**
   * 处理单页用户数据（区分新增/更新）
   */
  private int processPageRecords(List<ApiPlusUserInfo> records, Set<String> existingOneIds, UserInfo operator) {
    List<UserManageUserInfoDO> toUpdate = new ArrayList<>();
    List<UserManageUserInfoDO> toInsert = new ArrayList<>();

    for (ApiPlusUserInfo userInfo : records) {
      // 补全用户信息（渠道、详情）
      completeUserInfo(userInfo);

      // 转换为DO并分类
      UserManageUserInfoDO userDO = convertToDO(userInfo, operator);
      if (existingOneIds.contains(userInfo.getUuid())) {
        toUpdate.add(userDO);
      } else {
        toInsert.add(userDO);
      }
    }

    try {
      // 执行数据库操作
      dbUserManageService.batchInsertAndUpdateUserInfo(toUpdate, toInsert);
    } catch (Exception e) {
      log.error("全量同步：数据更新数据库失败，原因是{}", e);
    }
    return toUpdate.size() + toInsert.size();
  }

  /**
   * 处理单个账户同步
   */
  private int processSingleAccount(String accountName, Set<String> existingOneIds, UserInfo operator) {
    // 调用接口查询单用户信息
    ApiPlusUserInfo userInfo = aiPlusUserManageService.getAiPlusUserInfoByAccount(accountName);
    if (userInfo == null) {
      log.warn("单用户同步[{}]：AI+平台无此用户数据", accountName);
      return 0;
    }

    // 补全用户信息
    completeUserInfo(userInfo);

    // 转换为DO并分类
    UserManageUserInfoDO userDO = convertToDO(userInfo, operator);
    List<UserManageUserInfoDO> toUpdate = new ArrayList<>();
    List<UserManageUserInfoDO> toInsert = new ArrayList<>();

    if (existingOneIds.contains(userInfo.getUuid())) {
      toUpdate.add(userDO);
    } else {
      toInsert.add(userDO);
    }

    // 执行数据库操作
    dbUserManageService.batchInsertAndUpdateUserInfo(toUpdate, toInsert);
    return toUpdate.size() + toInsert.size();
  }

  /**
   * 补全用户信息（渠道、详情）
   */
  private void completeUserInfo(ApiPlusUserInfo userInfo) {
    try {
      //通过oneId 查询用户详情
      ApiPlusUserInfo userInfoByOneId = aiPlusUserManageService.getAiPlusUserInfoByOneId(userInfo.getUuid());
      BeanUtil.copyProperties(userInfoByOneId, userInfo);
    } catch (Exception e) {
      log.error("补全用户[{}]信息失败（不影响主同步流程）", userInfo.getUuid(), e);
    }
  }

  /**
   * 转换ApiPlusUserInfo为数据库实体DO
   */
  private UserManageUserInfoDO convertToDO(ApiPlusUserInfo userInfo, UserInfo operator) {
    UserManageUserInfoDO userDO = new UserManageUserInfoDO();

    // 核心字段映射
    userDO.setOneId(userInfo.getUuid());
    userDO.setAccountName(userInfo.getAccount());
    userDO.setUserStatus(userInfo.getStatus());

    // 组织信息映射
    if (userInfo.getOrg() != null) {
      userDO.setOrgCode(userInfo.getOrg().getCode());
      userDO.setOrgName(userInfo.getOrg().getName());
    }

    // 地区信息映射
    if (userInfo.getProvince() != null) {
      userDO.setProvinceCode(userInfo.getProvince().getCode());
      userDO.setProvinceName(userInfo.getProvince().getName());
    }

    if (userInfo.getCity() != null) {
      userDO.setCityCode(userInfo.getCity().getCode());
      userDO.setCityName(userInfo.getCity().getName());
    }

    // 敏感信息加密（RSA）
    try {
      if (StringUtil.isNotEmpty(userInfo.getUsername())) {
        userDO.setAccountCnName(extcryptoService.encrypt(userInfo.getUsername()));
      }
      if (StringUtil.isNotEmpty(userInfo.getEmail())) {
        userDO.setEmail(extcryptoService.encrypt(userInfo.getEmail()));
      }
    } catch (Exception e) {
      log.error("加密用户[{}]敏感信息失败", userInfo.getUuid(), e);
      // 加密失败中断同步
      throw new RuntimeException("用户信息加密失败", e);
    }

    // 操作人信息
    if (operator != null) {
      userDO.setCreatedBy(operator.getAccount());
      userDO.setUpdatedBy(operator.getAccount());
    }

    //dingding号补全
    List<ChannelInfoVO> channelInfoList = userInfo.getChannelInfoList();
    if (CollectionUtils.isNotEmpty(channelInfoList)) {
      channelInfoList.stream().forEach(channelInfoVO -> {
        if (ApiPlusChannelEnum.DINGTALK.getChannel().equals(channelInfoVO.getChannel())) {
          userDO.setDingtalkUserId(channelInfoVO.getAccount());
        }
      });
    }

    userDO.setPhone(userInfo.getMobile());

    return userDO;
  }

  /**
   * 从AI+平台获取单页数据
   */
  private AIPlusPageable<ApiPlusUserInfo> fetchPageData(int pageNum, int pageSize) {
    try {
      NewUserInfoRequest request = new NewUserInfoRequest();
      request.setPageNum(pageNum);
      request.setPageSize(pageSize);
      return aiPlusUserManageService.getAiPlusUserInfo(request);
    } catch (Exception e) {
      //首页失败终止同步
      if (pageNum == 1) {
        log.error("首次获取第1页数据失败，终止全量同步", e);
        throw new RuntimeException("AI+平台接口调用失败", e);
      }
      log.warn("获取第{}页数据失败，跳过当前页", pageNum, e);
      // 非首页失败跳过
      return null;
    }
  }

  /**
   * 判断是否有下一页
   */
  private boolean hasNextPage(int currentPage, Integer totalPage) {
    // 校验分页参数合法性（避免空指针和逻辑错误）
    if (totalPage == null) {
      return false;
    }
    // 当前页 < 总页数 → 有下一页
    return currentPage < totalPage;
  }


  /**
   * 创建临时线程池（按需创建，低频调用场景）
   */
  private Executor createTempThreadPool() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(CORE_POOL_SIZE);
    executor.setMaxPoolSize(MAX_POOL_SIZE);
    executor.setQueueCapacity(QUEUE_CAPACITY);
    executor.setThreadNamePrefix(THREAD_NAME_PREFIX);

    // 线程池关闭策略：等待任务完成后关闭
    executor.setWaitForTasksToCompleteOnShutdown(true);
    executor.setAwaitTerminationSeconds(AWAIT_TERMINATION_SECONDS);

    // 拒绝策略：提交线程执行（避免任务丢失，适合低频场景）
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

    executor.initialize();
    return executor;
  }

  /**
   * 关闭临时线程池
   */
  private void shutdownThreadPool(Executor executor) {
    if (executor instanceof ThreadPoolTaskExecutor) {
      ThreadPoolTaskExecutor threadPool = (ThreadPoolTaskExecutor) executor;
      // 已关闭则无需重复操作
      threadPool.shutdown();
      log.debug("临时线程池[{}]已关闭", THREAD_NAME_PREFIX);
    }
  }

  /**
   * 获取分布式锁
   */
  private Boolean acquireLock() {
    Boolean lockAcquired = redisService.lock(SYNC_LOCK_KEY, SYNC_LOCK_TTL);
    return lockAcquired != null && lockAcquired;
  }
}