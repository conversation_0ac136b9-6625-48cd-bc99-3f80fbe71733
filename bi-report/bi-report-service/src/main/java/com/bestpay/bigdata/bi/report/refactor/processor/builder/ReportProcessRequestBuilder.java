package com.bestpay.bigdata.bi.report.refactor.processor.builder;

import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.common.ChartTypeEnum;
import com.bestpay.bigdata.bi.common.dto.dataset.DataSet;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.report.chart.handler.util.ReportOrderTypeEnum;
import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.request.report.RefreshReportRequest;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import com.facebook.presto.jdbc.internal.guava.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ReportProcessRequestBuilder extends AbstractSceneRequestBuilder {

    @Autowired
    private DatasetService datasetService;

    @Autowired
    private AuthorityCheckUtil authorityCheckUtil;

    @Override
    public RequestKey supportedKey() {
        return new RequestKey(SceneType.REPORT_PROCESS, ProcessorType.QUERY);
    }

    @Override
    protected void buildReportRequest(ProcessContext context) {
        RefreshReportRequest reportRequest = (RefreshReportRequest) context.getRawRequest();

        ReportRequest report = getReportRequest(reportRequest);

        // 非对比表格，如果没有设置分页参数及最大行数，则默认只取一条数据
        if (Objects.nonNull(reportRequest.getChartType())
                && ChartTypeEnum.LIST_TABLE.getCode().equals(reportRequest.getChartType())) {
            if (Objects.isNull(reportRequest.getPageNum())
                    && Objects.isNull(reportRequest.getPageSize())
                    && StringUtil.isEmpty(reportRequest.getMaxRows())) {
                report.setMaxRows("1");
            }
        }

        // 报表排序默认值
        if (Objects.isNull(reportRequest.getOrderType())) {
            reportRequest.setOrderType(ReportOrderTypeEnum.DEFAULT.getType());
        }

        // 设置上下文
        DatasetInfo datasetInfo = report.getDatasetInfoList().get(0);
        context.setDatasetColumnConfigList(getDatasetColumnConfig(datasetInfo.getDatasetId()));
        context.setReportRequest(report);
    }

    private ReportRequest getReportRequest(RefreshReportRequest reportRequest) {

        List<DatasetInfo> datasetInfoList = reportRequest.getDatasetInfoList();
        DatasetInfo datasetInfo = datasetInfoList.get(0);

        List<DataSet> dataSets = datasetService.getDataSet(Lists.newArrayList(datasetInfo.getDatasetId()));
        DataSet dataSet = dataSets.get(0);

        // 权限组织校验
        authorityCheckUtil.checkAuthOrg(dataSet.getOrgAuth());

        List<DatasetInfo> infos = dataSets.stream().map(p -> {
            DatasetInfo info = new DatasetInfo();
            BeanUtils.copyProperties(p, info);
            return info;
        }).collect(Collectors.toList());

        reportRequest.setDatasetInfoList(infos);

        ReportRequest report = JSONUtil.toBean(JSONUtil.toJsonStr(reportRequest), ReportRequest.class);

        return report;
    }
}
