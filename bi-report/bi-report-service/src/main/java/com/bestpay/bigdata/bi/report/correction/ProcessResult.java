package com.bestpay.bigdata.bi.report.correction;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据处理结果类
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessResult {

    /**
     * 处理是否成功
     */
    private boolean success;

    /**
     * 处理后的值
     */
    private String processedValue;

    /**
     * 错误类型
     */
    private String errorType;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 异常堆栈
     */
    private String stackTrace;

    /**
     * 创建成功结果
     * 
     * @param processedValue 处理后的值
     * @return 成功结果
     */
    public static ProcessResult success(String processedValue) {
        return ProcessResult.builder()
                .success(true)
                .processedValue(processedValue)
                .build();
    }

    /**
     * 创建失败结果
     * 
     * @param errorType 错误类型
     * @param errorMessage 错误信息
     * @param stackTrace 异常堆栈
     * @param originalValue 原始值
     * @return 失败结果
     */
    public static ProcessResult failure(String errorType, String errorMessage, String stackTrace, String originalValue) {
        return ProcessResult.builder()
                .success(false)
                .processedValue(originalValue) // 失败时返回原始值
                .errorType(errorType)
                .errorMessage(errorMessage)
                .stackTrace(stackTrace)
                .build();
    }
}
