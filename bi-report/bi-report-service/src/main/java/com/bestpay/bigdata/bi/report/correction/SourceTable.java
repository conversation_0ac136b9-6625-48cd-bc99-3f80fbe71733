package com.bestpay.bigdata.bi.report.correction;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2023-07-14-10:32
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceTable implements Serializable {
    private static final long serialVersionUID = 164049386584360171L;

    private Long id;
    /**
     * 数据源类型
     */
    private String datasourceType;
    /**
     * 数据源名称
     */
    private String datasourceName;


    private String databaseName;

    /**
     * 来源表名称
     */
    private String name;

    private Integer statusCode;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

}
