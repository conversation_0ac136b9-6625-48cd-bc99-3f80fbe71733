package com.bestpay.bigdata.bi.report.correction;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.api.subscribe.ObjectSubscribeService;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO;
import com.bestpay.bigdata.bi.report.schedule.ScheduleConfig;
import com.bestpay.bigdata.bi.report.schedule.subscribe.ObjectSubscribeScheduler;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * ClassName: DashboardSubscribeCronReversion
 * Package: com.bestpay.bigdata.bi.report.controller.dataset.correction
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/2/28 13:56
 * @Version 1.0
 */
@Slf4j
@Service
public class DashboardSubscribeCronReversion
{

    @Resource
    private ObjectSubscribeService objectSubscribeService;

    @Resource
    private ObjectSubscribeScheduler subscribeScheduler;

    public Response executeReversion() {
        List<ObjectSubScribeDO> objectSubScribeDOList = objectSubscribeService.queryAll();
        for (ObjectSubScribeDO subScribeDO : objectSubScribeDOList) {
            try {
                ScheduleConfig scheduleConfig = subscribeScheduler.parseToScheduleConfigModel(subScribeDO);
                String cron = subscribeScheduler.parseScheduleConfig(scheduleConfig);

                ObjectSubScribeDO newSubscribeDo = new ObjectSubScribeDO();
                newSubscribeDo.setId(subScribeDO.getId());
                newSubscribeDo.setCronExp(cron);
                objectSubscribeService.updateById(newSubscribeDo);
            }
            catch (Throwable e) {
                log.error("dashboardSubscribeCronReversion happen error", e);
            }
        }
        return Response.ok();
    }

}
