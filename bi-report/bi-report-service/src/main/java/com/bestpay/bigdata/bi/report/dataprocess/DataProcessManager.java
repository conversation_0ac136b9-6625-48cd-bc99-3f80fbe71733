package com.bestpay.bigdata.bi.report.dataprocess;

import cn.hutool.core.util.StrUtil;
import com.bestpay.bigdata.bi.common.dto.ExcelHeaderInfo;
import com.bestpay.bigdata.bi.common.entity.ColumnName;
import com.bestpay.bigdata.bi.common.exception.DownloadException;
import com.bestpay.bigdata.bi.report.dataprocess.bean.DecryptRequest;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * ClassName: DataProcessManager
 * Package: com.bestpay.bigdata.bi.report.dataprocess
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/12/12 16:23
 * @Version 1.0
 */
@Slf4j
@Component
public class DataProcessManager
{
    @Resource
    private DecryptService decryptService;


    /**
     * 解析加密字段结果
     * @param request
     * @return
     */
    public List<List<String>> decryptParseResult(DecryptRequest request) {

        log.info("DecryptRequest request : {}, {}, {}", request.getSensitiveFields(), request.getExcelHeaderInfo(), request.getFileContainSensitiveInfo());
        Integer fileContainSensitiveInfo = request.getFileContainSensitiveInfo();
        String sensitiveFields = request.getSensitiveFields();
        ExcelHeaderInfo excelHeaderInfo = request.getExcelHeaderInfo();
        List<List<String>> data = request.getResults();

        if (Objects.isNull(fileContainSensitiveInfo) || fileContainSensitiveInfo != 1) {
            return request.getResults();
        }

        Stopwatch stopwatch = Stopwatch.createStarted();
        String[] splitArr = sensitiveFields.split(StrUtil.COMMA);
        List<ColumnName> columnNameMaps = excelHeaderInfo.getColumnNameMaps();
        List<Integer> columnNameIndex = new ArrayList<>();

        for (int i = 0; i < columnNameMaps.size(); i++) {
            String prop = columnNameMaps.get(i).getProp();
            for (String str : splitArr) {
                if (StrUtil.equals(str, prop)) {
                    columnNameIndex.add(i);
                }
            }
        }

        try {
            for (List<String> list : data) {
                for (Integer nameIndex : columnNameIndex) {
                    String name = list.get(nameIndex);
                    if (StringUtils.isNotEmpty(name)) {
                        try {
                            list.set(nameIndex, decryptService.decrypt(name));
                        } catch (Exception e) {
                            log.error("decrypt happen error", e);
                            list.set(nameIndex, name);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("parseResult error!",e);
            throw new DownloadException("数据udf解密发生异常: " + ExceptionUtils.getRootCauseMessage(e));
        }
        stopwatch.stop();
        log.info("parseResult method consume time：{} ms",stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return data;
    }
}
