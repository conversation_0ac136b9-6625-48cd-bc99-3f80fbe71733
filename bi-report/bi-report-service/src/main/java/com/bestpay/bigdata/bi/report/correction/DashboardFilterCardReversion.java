//package com.bestpay.bigdata.bi.report.correction;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.json.JSONArray;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
//import com.bestpay.bigdata.bi.database.api.dashboard.DashboardFilterCardService;
//import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO;
//import com.bestpay.bigdata.bi.report.request.dataset.DatasetColumnConfigRequest;
//import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
//import java.util.List;
//import javax.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// */
//@Component
//@Slf4j
//public class DashboardFilterCardReversion {
//
//  @Resource
//  private DashboardFilterCardService filterCardService;
//
//  @Resource
//  private DatasetService datasetService;
//
//  public void addFieldType() {
//    List<DashboardFilterCardDO> filterCardDOS = filterCardService.find();
//    if (CollUtil.isNotEmpty(filterCardDOS)) {
//      for (DashboardFilterCardDO filterCardDO : filterCardDOS) {
//
//        try {
//          DatasetColumnConfigRequest metadataRequest = new DatasetColumnConfigRequest();
//          metadataRequest.setDatasetId(filterCardDO.getDatasetId());
//
//          if (filterCardDO.getDatasetId() == null) {
//            log.error("filterId={}, datasetId is null",
//                filterCardDO.getId());
//            continue;
//          }
//
//          List<DatasetColumnConfigDTO> datasetColumnConfigDTOList
//              = datasetService.getColumnConfigList(metadataRequest, false).getData();
//
//          DashboardFilterCardDO update = new DashboardFilterCardDO();
//          update.setId(filterCardDO.getId());
//
//          JSONArray array = JSONUtil.parseArray(filterCardDO.getRelateCardsInfo());
//          for (Object o : array) {
//
//            JSONObject jsonObject = (JSONObject) o;
//            String fieldName = jsonObject.getStr("fieldName");
//
//            for (DatasetColumnConfigDTO columnConfigDTO : datasetColumnConfigDTOList) {
//              if (fieldName != null) {
//                if (columnConfigDTO.getEnName().equals(fieldName)
//                    || (columnConfigDTO.getOriginEnName()!=null
//                    && fieldName.contains(columnConfigDTO.getOriginEnName()))) {
//                  jsonObject.put("fieldType", columnConfigDTO.getFieldType().name());
//                }
//              }
//            }
//          }
//
//          update.setRelateCardsInfo(JSONUtil.toJsonStr(array));
//          filterCardService.updateAll(update);
//
//        } catch (Exception e) {
//          log.error("reversionDashFilter id={}", filterCardDO.getId(), e);
//        }
//      }
//    }
//  }
//
//}
