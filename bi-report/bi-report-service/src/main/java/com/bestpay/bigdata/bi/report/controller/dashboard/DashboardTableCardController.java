package com.bestpay.bigdata.bi.report.controller.dashboard;

import cn.hutool.core.collection.CollUtil;
import com.bestpay.bigdata.bi.common.api.RedisBaseService;
import com.bestpay.bigdata.bi.common.dto.dashboard.DashboardPageSettingModel;
import com.bestpay.bigdata.bi.common.dto.dashboard.TableCardInfoDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.WatermarkSetting;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewFilterCardDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewReportCardDTO;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.error.DashboardErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.request.TableCardInfoRequest;
import com.bestpay.bigdata.bi.common.request.TableCardPublishRequest;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.AssertUtil;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.report.cache.CacheHandleFactory;
import com.bestpay.bigdata.bi.report.cache.CardInfoCacheHandler;
import com.bestpay.bigdata.bi.report.cache.ChartCacheHandler;
import com.bestpay.bigdata.bi.report.cache.bean.CardInfoCacheBean;
import com.bestpay.bigdata.bi.report.cache.enums.SceneEnums;
import com.bestpay.bigdata.bi.report.enums.dashboard.PageSettingEnum;
import com.bestpay.bigdata.bi.report.request.auth.ObjectAuthRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.AddTableCardRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.EditTableCardAttrRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.EditTableCardRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.FilterCardDataSetRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.FilterCardRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardDataRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardListDTO;
import com.bestpay.bigdata.bi.report.request.tablecard.TableCardListRequest;
import com.bestpay.bigdata.bi.report.response.dashboard.NewCardDataSetColumnVO;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardCardDataQueryService;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardTableCardService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/biReport/newtableCard")
@Api(value = "仪表板卡片", tags = "仪表板卡片")
public class DashboardTableCardController {

  @Resource
  private DashboardTableCardService tableCardService;
  @Resource
  private DashboardDaoService dashboardService;
  @Resource
  private DashboardCardDataQueryService dashboardCardDataQueryService;
  @Resource
  private AuthorityCheckUtil authorityCheckUtil;
  @Resource
  private CacheHandleFactory cacheHandleFactory;
  @Resource
  private RedisBaseService redisBaseService;
  /**
   * 新增卡片
   * @param request
   * @return
   */
  @PostMapping("/addCard")
  @ApiOperation(httpMethod = "POST", value = "新增卡片", notes = "新增卡片")
  public Response<String> addCard(@RequestBody AddTableCardRequest request) {
    if(request.getDashboardId()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_DASHBOARD_ID_NULL,"dashboardId can not be null");
    }

    if(request.getCardType()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_DASHBOARD_CARD_TYPE_NULL,"cardType can not be null");
    }

    if(request.getCardInfo()==null){
      throw new BiException(DashboardErrorCode. DASHBOARD_DASHBOARD_CARD_INFO_NULL,"cardInfo can not be null");
    }

    return tableCardService.addCard(request);
  }

  /**
   * 编辑卡片
   * @param request
   * @return
   */
  @PostMapping("/editCard")
  @ApiOperation(httpMethod = "POST", value = "编辑卡片", notes = "编辑卡片")
  public Response<String> editCard(@RequestBody EditTableCardRequest request) {
    if(request.getDashboardId()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_DASHBOARD_ID_NULL,"dashboardId can not be null");
    }

    if(request.getCardType()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_DASHBOARD_CARD_TYPE_NULL,"cardType can not be null");
    }

    if(request.getCardInfo()==null){
      throw new BiException(DashboardErrorCode. DASHBOARD_DASHBOARD_CARD_INFO_NULL,"cardInfo can not be null");
    }

    if(request.getCardCode()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_CARD_CODE_INFO_NULL,"cardCode can not be null");
    }

    Response<String> data = tableCardService.editCard(request);
    invalidCache(request.getDashboardId(),request.getCardCode());
    return data;
  }

  @PostMapping("/editCardAttr")
  @ApiOperation(httpMethod = "POST", value = "编辑卡片属性", notes = "编辑卡片属性")
  public Response<String> editCardAttr(@RequestBody EditTableCardAttrRequest request) {
    if(request.getDashboardId()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_DASHBOARD_ID_NULL,"dashboardId can not be null");
    }

    if(request.getAttrType()==null){

      throw new BiException(DashboardErrorCode.DASHBOARD_ATTR_TYPE_NULL,"attrType can not be null");
    }

    if(request.getConfig()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_CONF_NULL,"config can not be null");
    }

    if(request.getCardCode()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_CARD_CODE_INFO_NULL,"cardCode can not be null");
    }

    Response<String> data = tableCardService.editCardAttr(request);
    invalidCache(request.getDashboardId(),request.getCardCode());
    return data;
  }

  /**
   * 卡片列表
   *
   * @param request
   * @return
   */
  @PostMapping("/getTableCardList")
  @ApiOperation(httpMethod = "POST", value = "卡片列表", notes = "卡片列表")
  public Response<List<TableCardListDTO.CardInfo>> getTableCardList(
      @RequestBody TableCardListRequest request) {

    if(request.getDashboardId()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_DASHBOARD_ID_NULL,"dashboardId can not be null");
    }

    Dashboard dashboard = dashboardService.getById(request.getDashboardId());

    if(dashboard==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_NOT_EXISTS,"仪表板不存在,id="+request.getDashboardId());
    }

    ObjectAuthRequest authRequest = new ObjectAuthRequest();
    authRequest.setAuthResourceId(request.getDashboardId()+"");
    authRequest.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());

    // 权限校验
    authorityCheckUtil.checkOwnerAndAuth(authRequest);

    return tableCardService.getTableCardList(request);
  }

  /**
   * 卡片详情
   *
   * @param request
   * @return
   */
  @PostMapping("/getCardInfoList")
  @ApiOperation(httpMethod = "POST", value = "卡片详情", notes = "卡片详情")
  public Response<List<TableCardInfoDTO>> getCardInfoList(
      @RequestBody TableCardInfoRequest request) {

    if(request.getDashboardId()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_DASHBOARD_ID_NULL,"dashboardId can not be null");
    }

    if(CollUtil.isEmpty(request.getCardCodeList())){
      throw new BiException(DashboardErrorCode.DASHBOARD_CARD_CODE_LIST_INFO_NULL,"cardCodeList can not be null");
    }

    Dashboard dashboard = dashboardService.getById(request.getDashboardId());

    if(dashboard==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_NOT_EXISTS,"仪表板不存在,id="+request.getDashboardId());
    }

    ObjectAuthRequest authRequest = new ObjectAuthRequest();
    authRequest.setAuthResourceId(request.getDashboardId()+"");
    authRequest.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());

    // 权限校验
    authorityCheckUtil.checkOwnerAndAuth(authRequest);

    List<TableCardInfoDTO> data = tableCardService.getCardInfoList(request);
    return Response.ok(data);
  }

  /**
   * 卡片发布
   *
   * @param request
   * @return
   */
  @PostMapping("/publish")
  @ApiOperation(httpMethod = "POST", value = "卡片发布", notes = "卡片发布")
  public Response<Integer> publish(@RequestBody TableCardPublishRequest request) {

    if(request.getDashboardId()==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_DASHBOARD_ID_NULL,"dashboardId can not be null");
    }
    if(CollUtil.isEmpty(request.getData())){
      throw new BiException(DashboardErrorCode.DASHBOARD_DATA_EMPTY,"data can not be empty");
    }
    DashboardPageSettingModel pageSettingModel = request.getDashboardPageSettingModel();
    if (Objects.nonNull(pageSettingModel)){
      PageSettingEnum pageSettingEnum = PageSettingEnum.getByCode(pageSettingModel.getPageWidthSetting());
      AssertUtil.notNull(pageSettingEnum, CodeEnum.DASHBOARD_PAGE_SETTING_NOT_EXISTS);
      // 水印设置
      WatermarkSetting watermarkSetting = pageSettingModel.getWatermarkSetting();
      Preconditions.checkArgument(Objects.nonNull(watermarkSetting), "水印设置不能为空");
    }

    Response<Integer> result = tableCardService.publish(request);
    invalidCache(request.getDashboardId(), null);
    return result;
  }

  private void invalidCache(Long dashboardId, String cardCode) {
    // 卡片信息缓存失效
    TableCardInfoRequest cardInfoRequest = new TableCardInfoRequest();
    cardInfoRequest.setDashboardId(dashboardId);
    CardInfoCacheHandler cacheHandler
        = (CardInfoCacheHandler) cacheHandleFactory.getHandleStrategy(SceneEnums.CARD_INFO);
    cacheHandler.invalidCache(CardInfoCacheBean
        .builder().redisKey(cacheHandler.generateKey(cardInfoRequest)).build());

    // 数据接口缓存失效
    TableCardDataRequest request = new TableCardDataRequest();
    request.setDashboardId(dashboardId);
    request.setCardCode(cardCode);
    ChartCacheHandler chartCacheHandler
        = (ChartCacheHandler) cacheHandleFactory.getHandleStrategy(SceneEnums.CHART);
    chartCacheHandler.invalidCache(request);
  }

  /**
   * 查询各个卡片展示数据
   *
   * @param request
   * @return
   */
  @PostMapping("/syncQueryData")
  @ApiOperation(httpMethod = "POST", value = "查询各个卡片展示数据", notes = "查询各个卡片展示数据")
  public Object syncQueryData(@RequestBody TableCardDataRequest request) {
    Dashboard dashboard = dashboardService.getById(request.getDashboardId());

    if(dashboard==null){
      throw new BiException(DashboardErrorCode.DASHBOARD_NOT_EXISTS,"仪表板不存在,id="+request.getDashboardId());
    }

    ObjectAuthRequest authRequest = new ObjectAuthRequest();
    authRequest.setAuthResourceId(request.getDashboardId()+"");
    authRequest.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());

    // 权限校验
    authorityCheckUtil.checkOwnerAndAuth(authRequest);

    return dashboardCardDataQueryService.syncQueryData(request);
  }

  /**
   * 筛选器数据集
   *
   * @param request
   * @return
   */
  @PostMapping("/getCardRelatedDataSetColumnList")
  @ApiOperation(httpMethod = "POST", value = "筛选器数据集", notes = "筛选器数据集")
  public Response<List<NewCardDataSetColumnVO>> getCardRelatedDataSetColumnList(
      @RequestBody FilterCardDataSetRequest request) {
    return tableCardService.getCardRelatedDataSetColumnList(request);
  }

  /**
   * 新建仪表板应用嵌入-数据权限列表
   * @param request
   * @return
   */
  @PostMapping("/getFilterList")
  @ApiOperation(httpMethod = "POST", value = "新建仪表板应用嵌入-数据权限列表", notes = "新建仪表板应用嵌入-数据权限列表")
  public Response<List<NewFilterCardDTO>> getFilterList(@RequestBody FilterCardRequest request) {
    return tableCardService.getFilterList(request);
  }


  /**
   * 临时接口 后续前端调整完毕删除
   * 报表卡片详情接口
   * @param dashboardId
   * @return
   */
  @GetMapping("/queryReportTemplate")
  @ApiOperation(value = "查询报表模板详情", httpMethod = "GET", response = Response.class)
  public Response<NewReportCardDTO> queryReportTemplate(
      @RequestParam(value = "code", required = false) String code,
      @RequestParam("dashboardId") Long dashboardId) {

    Dashboard dashboard = dashboardService.getById(dashboardId);
    if (dashboard == null) {
      throw new BiException(DashboardErrorCode.DASHBOARD_NOT_EXISTS,String.format("dashboardId=%s", dashboardId));
    }

    // 权限校验
    ObjectAuthRequest request = new ObjectAuthRequest();
    request.setAuthResourceId(dashboardId+"");
    request.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());

    // 权限校验
    authorityCheckUtil.checkOwnerAndAuth(request);

    TableCardInfoDTO cardInfoDTO = tableCardService.getReportCard(dashboardId, code);
    NewReportCardDTO dto = (NewReportCardDTO) cardInfoDTO.getCardInfo();
    return Response.ok(dto);
  }
}
