package com.bestpay.bigdata.bi.report.controller.dashboard;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardQueryRequest;
import com.bestpay.bigdata.bi.report.request.tablecard.CopyTableCardRequest;
import com.bestpay.bigdata.bi.report.response.dashboard.DashboardConfigVO;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardCopyService;
import java.util.List;
import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/biReport")
@Api(value = "仪表板复制", tags = "仪表板复制")
public class DashboardCopyController {

  @Resource
  private DashboardCopyService dashboardCopyService;

  /**
   * 查询仪表相关配置信息
   * @param dashboard
   * @return
   */
  @PostMapping("/dashboard/queryDashboardConfig")
  @ApiOperation(httpMethod = "POST", value = "查询仪表相关配置信息", notes = "查询仪表相关配置信息")
  public Response<List<DashboardConfigVO>> queryDashboardConfig(
      @RequestBody DashboardQueryRequest dashboard) {
    return Response.ok(dashboardCopyService.queryDashboardConfig(dashboard));
  }

  /**
   * copy卡片
   * @param request
   * @return
   */
  @PostMapping("/newtableCard/copyCard")
  @ApiOperation(httpMethod = "POST", value = "卡片复制", notes = "卡片复制")
  public Response copyCard(@RequestBody CopyTableCardRequest request) {
    return dashboardCopyService.copyCard(request);
  }

}
