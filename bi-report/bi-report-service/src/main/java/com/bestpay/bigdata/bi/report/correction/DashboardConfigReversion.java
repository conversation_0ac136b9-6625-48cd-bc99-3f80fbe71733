package com.bestpay.bigdata.bi.report.correction;

import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardConfigService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashBoardConfigDO;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import java.util.Date;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class DashboardConfigReversion {

  @Resource
  private DashboardConfigService configService;

  @Resource
  private DashboardDaoService daoService;

  public static final String grid_default
      = "{\"rowInterval\":8,\"colInterval\":8,\"cardUnitHeight\":18,\"gridColNum\":24}";

  public static final String bgColorDefault
      = "{\"bgColor\":\"rgb(255, 255, 255,0.75)\"}";

  public static final String watermarkSettingDefault
      = "{\"showWatermark\":false,\"fontSize\":12,\"fontColor\":\"rgb(245, 245, 245)\"}";


  public void reversion(){

    for (Long i = 0L; i < 10000; i++) {

      DashBoardConfigDO config = configService.queryByDashboardId(i);
      Dashboard dashboard = daoService.getById(i);

      if(dashboard!=null && config==null) {

        DashBoardConfigDO dashBoardConfigDO = new DashBoardConfigDO();
        dashBoardConfigDO.setDashboardId(i);
        dashBoardConfigDO.setCreatedBy(UserContextUtil.getUserInfo().getEmail());
        dashBoardConfigDO.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
        dashBoardConfigDO.setUpdatedAt(new Date());
        dashBoardConfigDO.setCreatedAt(new Date());

        // 默认值
        dashBoardConfigDO.setBgSetting(bgColorDefault);
        dashBoardConfigDO.setWatermarkSetting(watermarkSettingDefault);
        dashBoardConfigDO.setGridSetting(grid_default);
        dashBoardConfigDO.setPageWidthSetting(1);
        configService.insert(dashBoardConfigDO);
      }
    }
  }

}
