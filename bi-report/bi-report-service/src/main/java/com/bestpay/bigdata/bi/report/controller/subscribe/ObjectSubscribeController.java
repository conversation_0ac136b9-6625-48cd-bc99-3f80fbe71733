package com.bestpay.bigdata.bi.report.controller.subscribe;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.enums.dashboard.FrequencyEnum;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardSubScribeRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.AddSubScribeRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.IdRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.QueryStateRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.SearchSendLogRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.SearchSubRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.SubCountRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.UpdateSubScribeRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.UpdateTaskStatusRequest;
import com.bestpay.bigdata.bi.report.response.subscribe.SendDetailVO;
import com.bestpay.bigdata.bi.report.response.subscribe.SendLogVO;
import com.bestpay.bigdata.bi.report.response.subscribe.SubDetailVO;
import com.bestpay.bigdata.bi.report.response.subscribe.SubObjectVO;
import com.bestpay.bigdata.bi.report.response.subscribe.SubVO;
import com.bestpay.bigdata.bi.report.schedule.subscribe.ObjectSubscribeScheduler;
import com.bestpay.bigdata.bi.report.service.subscribe.DashboardSubService;
import com.bestpay.bigdata.bi.report.service.subscribe.ReportSubService;
import com.bestpay.bigdata.bi.report.service.subscribe.SubscribeManagerService;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 订阅接口
 *
 * <AUTHOR>
 * @create 2023-03-03-15:31
 */
@Slf4j
@RestController
@RequestMapping("/biReport/subscribe/")
@Api(value = "订阅", tags = "订阅")
public class ObjectSubscribeController {

  @Resource
  private DashboardSubService dashboardSubService;

  @Resource
  private ReportSubService reportSubService;

  @Resource
  private SubscribeManagerService managerService;

  @Resource
  private ObjectSubscribeScheduler objectSubscribeScheduler;

  /**
   * 新增仪表板订阅
   *
   * @param request
   * @return
   */
  @PostMapping("dashboard/add")
  @ApiOperation(value = "新增订阅", httpMethod = "POST", response = Response.class)
  public Response<Long> saveDashboard(@RequestBody DashboardSubScribeRequest request) {
    log.debug("start to insert new Subscribe request, subscribe request : {}", JSONUtil.toJsonStr(request));
    return dashboardSubService.insert(request);
  }

  @PutMapping("dashboard/{id}")
  @ApiOperation(value = "订阅修改", httpMethod = "PUT", response = Response.class)
  public Response<Long> updateDashboard(@PathVariable Long id, @RequestBody DashboardSubScribeRequest dashboard) {
    return dashboardSubService.updateById(id, dashboard);
  }


  @PostMapping("report/add")
  @ApiOperation(value = "新增报表订阅", httpMethod = "POST", response = Response.class)
  public Response<Long> saveReportSub(@RequestBody @Validated AddSubScribeRequest request) {
    reportSubCheck(request);
    return Response.ok(reportSubService.saveReportSub(request));
  }

  @PostMapping("report/update")
  @ApiOperation(value = "编辑报表订阅", httpMethod = "POST", response = Response.class)
  public Response<Long> updateReportSub(@RequestBody @Validated UpdateSubScribeRequest request) {
    Preconditions.checkArgument(Objects.nonNull(FrequencyEnum.getName(request.getPostFrequency())), "发送频率错误");
    reportSubCheck(request);
    Long id = request.getId();
    Preconditions.checkArgument(Objects.nonNull(id), "id 不能为空");
    return Response.ok(reportSubService.updateReportSub(request));
  }

  // =======================以下是管理的===================================================
  @PostMapping("retry")
  @ApiOperation(value = "任务重发", httpMethod = "POST", response = Response.class)
  public Response retry(@RequestBody @Validated IdRequest request) throws Exception {
    return Response.ok(objectSubscribeScheduler.addOnceObjectQuartzJob(request));
  }

  @PostMapping("queryState")
  @ApiOperation(value="查询订阅任务实例状态", httpMethod="POST",response = Response.class)
  public Response queryState(@RequestBody QueryStateRequest queryStateRequest) throws Exception {
    return  Response.ok(managerService.queryState(queryStateRequest));
  }

  @PostMapping("taskList")
  @ApiOperation(value = "订阅报表列表", httpMethod = "POST", response = Response.class)
  public Response<PageQueryVO<SubVO>> taskList(@RequestBody SearchSubRequest request) {
    return Response.ok(managerService.taskList(request));
  }

  @PostMapping("objectList")
  @ApiOperation(value = "对象列表", httpMethod = "POST", response = Response.class)
  public Response<List<SubObjectVO>> objectList() {
    return Response.ok(managerService.objectList());
  }

  @PostMapping("detail")
  @ApiOperation(value = "订阅详情", httpMethod = "POST", response = Response.class)
  public Response<SubDetailVO> detail(@RequestBody @Validated IdRequest request) {
    return Response.ok(managerService.detail(request));
  }

  @PostMapping("logList")
  @ApiOperation(value = "订阅报表列表", httpMethod = "POST", response = Response.class)
  public Response<PageQueryVO<SendLogVO>> logList(@RequestBody SearchSendLogRequest request) {
    return Response.ok(managerService.logList(request));
  }

  @PostMapping("logList/detail")
  @ApiOperation(value = "订阅报表详情", httpMethod = "POST", response = Response.class)
  public Response<SendDetailVO> logListDetail(@RequestBody @Validated IdRequest request) {
    return Response.ok(managerService.logListDetail(request));
  }

  @PostMapping("subCount")
  @ApiOperation(value = "统计与订阅对象ID 关联的订阅记录", httpMethod = "POST", response = Response.class)
  public Response<Integer> subCount(@RequestBody @Validated SubCountRequest request) {
    return Response.ok(managerService.subCount(request));
  }

  @PostMapping("updateTaskStatus")
  @ApiOperation(value = "开启关闭订阅", httpMethod = "POST", response = Response.class)
  public Response<Integer> updateTaskStatus(@RequestBody @Validated UpdateTaskStatusRequest request) {
    return Response.ok(managerService.updateTaskStatus(request));
  }

  //   删除接口不用改
  @DeleteMapping("/{id}")
  @ApiOperation(value = "订阅删除", httpMethod = "DELETE", response = Response.class)
  public Response<Boolean> deleteDashboard(@PathVariable Long id) {
    log.debug("start to delete dashboard sub id:{}", id);
    return dashboardSubService.deleteById(id);
  }

  private void reportSubCheck(AddSubScribeRequest request) {
    Preconditions.checkArgument("xlsx".equals(request.getPostType()), "发送格式有误，请核对");

    Preconditions.checkArgument(0 == request.getSubscribeType(), "订阅方式只能为邮件");

    Preconditions.checkArgument(Objects.nonNull(FrequencyEnum.getName(request.getPostFrequency())), "发送频率错误");
    String fileName = request.getFileName();

    Preconditions.checkArgument(StrUtil.isNotBlank(fileName) && fileName.length() < 50,
        "文件名称不能为空，并且文件名称不能超过50字符");
  }
}
