package com.bestpay.bigdata.bi.report.correction;

import com.bestpay.bigdata.bi.database.api.common.ObjectAuthDAOService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.report.ReportService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AuthOrgReversion
{
    @Resource
    private ReportService reportService;

    @Resource
    private ObjectAuthDAOService objectAuthDAOService;

    @Resource
    private DashboardDaoService dashboardDaoService;

//    public void executeReversion(Map<String, String> orgUserGroupMap) {
//        List<Report> reports = reportService.queryAll(new ReportQueryDTO());
//        for (Report report : reports) {
//            if(StringUtil.isEmpty(report.getOrgAuth())){
//                continue;
//            }
//
//            String[] orgs = report.getOrgAuth().split("\\.");
//            for (String org : orgs) {
//                if(orgUserGroupMap.get(org)!=null){
//                    ObjectAuthDo objectAuthDo = new ObjectAuthDo();
//                    objectAuthDo.setAuthResourceId(report.getId()+"");
//                    objectAuthDo.setAuthResourceType(AuthResourceTypeEnum.report.name());
//                    objectAuthDo.setAuthType(AuthTypeEnum.userGroup.name());
//                    objectAuthDo.setAuthOperate(AuthOperateEnum.edit.name());
//                    objectAuthDo.setStatusCode(0);
//                    objectAuthDo.setAuthUser(orgUserGroupMap.get(org));
//                    objectAuthDo.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
//                    objectAuthDo.setCreatedBy(UserContextUtil.getUserInfo().getEmail());
//                    objectAuthDAOService.insert(objectAuthDo);
//                }
//            }
//        }
//
//        List<Dashboard> dashboards = dashboardDaoService.queryByCondition(new DashboardQuery());
//        for (Dashboard dashboard : dashboards) {
//            if(StringUtil.isEmpty(dashboard.getOrgAuth())){
//                continue;
//            }
//
//            String[] orgs = dashboard.getOrgAuth().split(",");
//            for (String org : orgs) {
//                if(orgUserGroupMap.get(org)!=null){
//                    ObjectAuthDo objectAuthDo = new ObjectAuthDo();
//                    objectAuthDo.setAuthResourceId(dashboard.getId()+"");
//                    objectAuthDo.setAuthResourceType(AuthResourceTypeEnum.dashboard.name());
//                    objectAuthDo.setAuthType(AuthTypeEnum.userGroup.name());
//                    objectAuthDo.setAuthOperate(AuthOperateEnum.edit.name());
//                    objectAuthDo.setStatusCode(0);
//                    objectAuthDo.setAuthUser(orgUserGroupMap.get(org));
//                    objectAuthDo.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
//                    objectAuthDo.setCreatedBy(UserContextUtil.getUserInfo().getEmail());
//                    objectAuthDAOService.insert(objectAuthDo);
//                }
//            }
//        }
//    }
}
