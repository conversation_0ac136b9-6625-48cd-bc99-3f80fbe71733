package com.bestpay.bigdata.bi.report.controller.dataset;

import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetAuthUserDTO;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetAuthUserListVO;
import com.bestpay.bigdata.bi.report.request.dataset.*;
import com.bestpay.bigdata.bi.report.response.dataset.DatasetAuthListVO;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetAuthService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;

import com.bestpay.bigdata.bi.report.service.impl.dataset.DatasetAuthSyncServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/biReport/dataset/auth")
@Api(value = "数据集权限", tags = "数据集权限")
public class DatasetAuthController {

    @Resource
    private DatasetAuthService datasetAuthService;
    @Resource
    private DatasetAuthSyncServiceImpl datasetAuthSyncService;


    /**
     * 获取用户|用户组列表
     */
    @PostMapping("/getAllUserAndUserGroup")
    @ApiOperation(httpMethod = "POST", value = "获取用户|用户组列表", notes = "获取用户|用户组列表")
    public Response<List<DatasetAuthUserListVO>> getAllUserAndUserGroup() {
        List<DatasetAuthUserListVO> data = datasetAuthService.getAllUserAndUserGroup();
        return Response.ok(data);
    }

    /**
     * 获取用户列表（分页+关键字）
     */
    @PostMapping("/getUsers")
    @ApiOperation(httpMethod = "POST", value = "获取用户列表（分页+关键字）", notes = "获取用户列表（分页+关键字）")
    public Response<PageQueryVO<DatasetAuthUserListVO>> getUsers(@RequestBody UserAndGroupRequest request) {
        PageQueryVO<DatasetAuthUserListVO> data = datasetAuthService.getUserList(request);
        return Response.ok(data);
    }

    /**
     * 获取用户组列表（关键字）
     */
    @PostMapping("/getUserGroups")
    @ApiOperation(httpMethod = "POST", value = "获取用户组列表（关键字）", notes = "获取用户组列表（关键字）")
    public Response<List<DatasetAuthUserListVO>> getUserGroups(@RequestBody UserAndGroupRequest request) {
        List<DatasetAuthUserListVO> data = datasetAuthService.getUserGroupList(request);
        return Response.ok(data);
    }



    /**
     * 添加白名单
     */
    @PostMapping("/addWhiteUser")
    @ApiOperation(httpMethod = "POST", value = "添加白名单", notes = "添加白名单")
    public Response<List<DatasetAuthUserListVO>> addWhiteUser(@RequestBody DatesetAuthWhiteUserRequest request) {
        datasetAuthService.addWhiteUser(request);
        return Response.ok();
    }


    /**
     * 添加规则
     */
    @PostMapping("/addRule")
    @ApiOperation(httpMethod = "POST", value = "添加规则", notes = "添加规则")
    public Response addRule(@RequestBody @Validated DatesetAuthAddRequest request) {
        datasetAuthService.addRule(request);
        return Response.ok();
    }

    /**
     * 添加规则
     */
    @PostMapping("/editRule")
    @ApiOperation(httpMethod = "POST", value = "添加规则", notes = "添加规则")
    public Response editRule(@RequestBody @Validated DatesetAuthAddRequest request) {
        datasetAuthService.editRule(request);
        return Response.ok();
    }


    /**
     * 规则列表
     */
    @PostMapping("/ruleList")
    @ApiOperation(httpMethod = "POST", value = "规则列表", notes = "规则列表")
    public Response<DatasetAuthListVO> ruleList(@RequestBody DatesetAuthListRequest request) {
        return datasetAuthService.ruleList(request);
    }


    /**
     * 规则状态
     */
    @PostMapping("/updateRuleStatus")
    @ApiOperation(httpMethod = "POST", value = "规则状态", notes = "规则状态")
    public Response ruleList(@RequestBody DatesetAuthStatusUpdateRequest request) {
        return datasetAuthService.updateRuleStatus(request);
    }

    @GetMapping("/dataCorrection/authRowRule")
    @ApiOperation("数据订正行级权限接口")
    public Response dataCorrectionAuthRowRule(@RequestParam Boolean sync) {
        // 数据订正t_dataset_row_auth和t_dataset_row_auth_rule表数据
        if (Objects.isNull(sync)) {
            sync = false;
        }
        datasetAuthSyncService.syncRowAuthRule(sync);
        return Response.ok();
    }


    @GetMapping("/dataCorrection/authWhiteUser")
    @ApiOperation("数据订正行级权限白名单接口")
    public Response dataCorrectionAuthWhiteUser(@RequestParam Boolean sync) {
        if (Objects.isNull(sync)) {
            sync = false;
        }
        datasetAuthSyncService.syncWhiteUser(sync);
        return Response.ok();
    }


    @GetMapping("/dataCorrection/authRowRuleList")
    @ApiOperation("数据订正行级权限错误信息接口")
    public Response<Map<Long, List<DatasetAuthUserDTO>>> dataCorrectionAuthRowRuleErrorList() {
        Map<Long, List<DatasetAuthUserDTO>> errorMap = datasetAuthSyncService.dataCorrectionRowAuthRuleErrorList();
        return Response.ok(errorMap);
    }


    @GetMapping("/dataCorrection/authWhiteUserList")
    @ApiOperation("数据订正行级权限白名单错误信息接口")
    public Response<Map<Long, List<DatasetAuthUserDTO>>> authWhiteUserErrorList() {
        Map<Long, List<DatasetAuthUserDTO>> errorMap = datasetAuthSyncService.dataCorrectionWhiteUserErrorList();
        return Response.ok(errorMap);
    }
}
