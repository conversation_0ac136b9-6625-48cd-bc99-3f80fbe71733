package com.bestpay.bigdata.bi.report.dataprocess.bean;

import com.bestpay.bigdata.bi.common.dto.ExcelHeaderInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * ClassName: DecryptRequest
 * Package: com.bestpay.bigdata.bi.report.dataprocess.bean
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/12/12 16:30
 * @Version 1.0
 */
@Data
@AllArgsConstructor
public class DecryptRequest
{
    /**
     * 数据是否包含敏感信息: 0: false 1:true
     */
    private Integer fileContainSensitiveInfo;

    /**
     * 敏感字段
     */
    private String sensitiveFields;

    /**
     * 生成报表的Excel头信息
     */
    private ExcelHeaderInfo excelHeaderInfo;

    /**
     * 查詢結果
     **/
    private List<List<String>> results;
}
