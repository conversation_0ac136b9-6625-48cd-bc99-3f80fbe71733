package com.bestpay.bigdata.bi.report.refactor.service.impl;

import com.bestpay.bigdata.bi.common.refactor.bean.DataChunk;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.service.QueryExecutorService;
import com.bestpay.bigdata.bi.report.request.report.ReportRequest;
import com.bestpay.bigdata.bi.report.service.report.ReportQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;

@Service
public class QueryExecutorServiceImpl implements QueryExecutorService {

    @Autowired
    private ReportQueryService reportQueryService;

    @Override
    public Iterator<DataChunk> executeQuery(ProcessContext context) {
        return null;
    }

    @Override
    public String buildSQL(ReportRequest reportRequest, ProcessorType processorType) {
        return "";
    }
}
