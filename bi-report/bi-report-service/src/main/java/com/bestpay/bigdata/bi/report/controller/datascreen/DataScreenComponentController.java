package com.bestpay.bigdata.bi.report.controller.datascreen;


import com.bestpay.bigdata.bi.common.dto.datascreen.ComponentDTO;
import com.bestpay.bigdata.bi.common.enums.ComponentDataQueryType;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.request.new_datascreen.component.ComponentDataQueryRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.component.ComponentDetailQueryRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.component.ComponentQueryRequest;
import com.bestpay.bigdata.bi.report.request.new_datascreen.component.ComponentRequestRequest;
import com.bestpay.bigdata.bi.report.response.datascreen.ComponentVO;
import com.bestpay.bigdata.bi.report.service.datascreen.ComponentService;
import com.bestpay.bigdata.bi.report.service.impl.datascreen.ComponentFactory;
import java.util.List;
import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-08-26
 */
@Slf4j
@RestController
@RequestMapping("/biReport/datascreen/component")
@Api(value = "数据大屏组件", tags = "数据大屏组件")
public class DataScreenComponentController {
    @Resource
    private ComponentFactory componentFactory;
    @Resource
    private ComponentService componentService;

    /**
     * 新增组件
     *
     * @param request
     * @return
     */
    @PostMapping("/addComponent")
    @ApiOperation(httpMethod = "POST", value = "新增组件", notes = "新增组件")
    public Response<String> addComponent(@RequestBody @Validated ComponentRequestRequest request) {
        return Response.ok(componentFactory.getComponent(request.getComponentType()).buildSave(request));
    }

    /**
     * 编辑组件
     *
     * @param request
     * @return
     */
    @PostMapping("/editComponent")
    @ApiOperation(httpMethod = "POST", value = "编辑组件", notes = "编辑组件")
    public Response<String> editComponent(@RequestBody @Validated ComponentRequestRequest request) {
        return Response.ok(componentFactory.getComponent(request.getComponentType()).buildEdit(request));
    }

    /**
     * 数据查询
     *
     * @param request
     * @return
     */
    @PostMapping("/syncQueryData")
    @ApiOperation(httpMethod = "POST", value = "数据查询", notes = "数据查询")
    public Object syncQueryData(@RequestBody ComponentDataQueryRequest request) {
        return componentFactory.getComponent(request.getComponentType()).syncQueryData(request);
    }


    /**
     * 查询所有组件列表
     *
     * @param request
     * @return
     */
    @PostMapping("/queryComponentList")
    @ApiOperation(httpMethod = "POST", value = "查询所有组件列表", notes = "查询所有组件列表")
    public Response<List<ComponentVO>> queryComponentList(@RequestBody @Validated ComponentQueryRequest request) {/*
        List<ComponentVO> componentVOList = componentService.queryComponentListFromRedis(request);
        if (null == componentVOList) {
        }*/
        List<ComponentVO> componentVOList = componentService.queryComponentList(request);
        return Response.ok(componentVOList);
    }


    /**
     * 查询组件详情
     *
     * @param request
     * @return
     */
    @PostMapping("/queryDetails")
    @ApiOperation(httpMethod = "POST", value = "查询组件详情", notes = "查询组件详情")
    public Response<List<ComponentDTO>> queryComponentDetails(@RequestBody @Validated ComponentDetailQueryRequest request) {
        request.setQueryType(ComponentDataQueryType.MYSQL_ONLY);
        return Response.ok(componentService.queryComponentDetails(request));
    }
}
