package com.bestpay.bigdata.bi.report.controller.dashboard;

import static com.bestpay.bigdata.bi.common.constant.BIConstant.EMAIL;
import static java.util.Objects.requireNonNull;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDirectoryDAOService;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardDirectoryDo;
import com.bestpay.bigdata.bi.report.bean.dashboard.DashboardDirectoryVO;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardDirectoryRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.MoveDashboardDirectoryRequest;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardDirectoryService;
import com.bestpay.bigdata.bi.report.util.AuthorityCheckUtil;
import java.util.List;
import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @create 2023-06-02-14:01
 */
@RestController
@RequestMapping("/biReport/dashboard/directory")
@Api(value = "仪表板目录相关", tags = "仪表板目录相关")
public class DashboardDirectoryController {


    @Resource
    private DashboardDirectoryService directoryService;
    @Resource
    private DashboardDirectoryDAOService directoryDAOService;
    @Resource
    private AuthorityCheckUtil authorityCheckUtil;

    /**
     * 新增
     */
    @PostMapping("/add")
    @ApiOperation(httpMethod = "POST", value = "新增仪表板目录", notes = "新增仪表板目录")
    public Response<DashboardDirectoryVO> addDashboardDirectory(@RequestBody @Validated DashboardDirectoryRequest dashboardDirectoryRequest) {
        return directoryService.addDashboardDirectory(dashboardDirectoryRequest);
    }


    /**
     * 修改
     */
    @PutMapping("/update")
    @ApiOperation(httpMethod = "PUT", value = "更新仪表板目录", notes = "更新仪表板目录")
    public Response<DashboardDirectoryVO> updateDashboardDirectory(@RequestBody  @Validated DashboardDirectoryRequest dashboardDirectoryRequest) {

        // check param
        requireNonNull(dashboardDirectoryRequest.getId(), "id can not be null");

        DashboardDirectoryDo directoryDo
            = directoryDAOService.queryDbById(dashboardDirectoryRequest.getId());

        // 权限校验
        authorityCheckUtil.checkOwner(EMAIL, directoryDo.getCreatedBy());

        return directoryService.updateDashboardDirectory(dashboardDirectoryRequest);
    }


    /**
     * 删除
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(httpMethod = "DELETE", value = "删除目录", notes = "删除目录")
    public Response<Boolean> deleteDashboardDirectory(@PathVariable Long id) {
        DashboardDirectoryDo directoryDo
            = directoryDAOService.queryDbById(id);

        // 权限校验
        authorityCheckUtil.checkOwner(EMAIL, directoryDo.getCreatedBy());
        return directoryService.deleteDashboardDirectory(id);
    }


    /**
     * 通过组织编码查询目录列表
     *
     * @param orgCode 组织编码
     * @return
     */
    @GetMapping("/getDashboardDirectoryList")
    @ApiOperation(httpMethod = "GET", value = "通过组织编码查询目录列表", notes = "通过组织编码查询目录列表")
    public Response<List<DashboardDirectoryVO>> getDashboardDirectoryList(@RequestParam(name = "orgCode", required = false) String orgCode) {
        return this.directoryService.getDashboardDirectoryList(orgCode);
    }


    /**
     * 目录移动
     */
    @PostMapping("/move")
    @ApiOperation(httpMethod = "POST", value = "目录移动", notes = "目录移动")
    public Response<Boolean> moveDashboardDirectory(@RequestBody MoveDashboardDirectoryRequest moveDashboardDirectoryRequest) {
        return directoryService.moveDashboardDirectory(moveDashboardDirectoryRequest);
    }


    /**
     * dashboard directory info
     * @param id directory id
     * @param
     * @return
     */
    @GetMapping("/info/{id}")
    @ApiOperation(httpMethod = "GET", value = "仪表板目录信息", notes = "仪表板目录信息")
    public Response<DashboardDirectoryVO> getDashboardDirectoryInfo(@PathVariable Long id) {
        return directoryService.getDashboardDirectoryInfo(id);
    }


    /**
     * 仪表板新建目录 - 父子目录
     * 获取能够加入目录的目录列表
     * @param orgCode
     * @return
     */
    @GetMapping("/getDashboardAddDirectoryList")
    @ApiOperation(httpMethod = "GET", value = "获取能够加入目录的目录列表", notes = "获取能够加入目录的目录列表")
    public Response<List<DashboardDirectoryVO>> getDashboardAddDirectoryList(@RequestParam("orgCode") String orgCode) {
        return this.directoryService.getDashboardAddDirectoryList(orgCode);
    }

}
