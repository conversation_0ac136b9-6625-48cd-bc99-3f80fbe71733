package com.bestpay.bigdata.bi.report.correction;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-07-14-10:34
 */
@Data
public class DatasetTable implements Serializable {
    private static final long serialVersionUID = 711373119127227714L;

    private Long id;
    /**
     * 数据集id
     */
    private Long datasetId;
    /**
     * 来源表id
     */
    private Long sourceTableId;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;
}
