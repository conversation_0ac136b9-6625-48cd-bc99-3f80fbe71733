package com.bestpay.bigdata.bi.report.controller.dataset;

import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetElementVO;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetFieldVO;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetElementRequest;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetElementShowRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetElementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-07-05-14:36
 */
@RestController
@RequestMapping("/biReport/datasetElement")
@Api(value = "数据集元素", tags = "数据集元素")
public class DatasetElementController {

    @Resource
    private DatasetElementService datasetElementService;

    /**
     * 新增
     */
    @PostMapping("/add")
    @ApiOperation(httpMethod = "POST", value = "新增", notes = "新增")
    public Response<DatasetElementVO> addDatasetElement(@RequestBody DatasetElementRequest datasetElementRequest) {

        return datasetElementService.addDatasetElement(datasetElementRequest);
    }


    /**
     * 查询
     */
    @PostMapping("/show")
    @ApiOperation(httpMethod = "POST", value = "查询", notes = "查询")
    public Response<Map<String, List<DatasetFieldVO>>> show(@RequestBody DatasetElementShowRequest datasetElementRequest) {

        return datasetElementService.show(datasetElementRequest);
    }
}
