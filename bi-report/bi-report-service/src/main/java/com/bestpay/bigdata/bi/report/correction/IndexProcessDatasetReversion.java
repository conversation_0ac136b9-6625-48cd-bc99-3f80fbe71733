//package com.bestpay.bigdata.bi.report.correction;
//
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.bestpay.bigdata.bi.common.bean.UserInfoRequest;
//import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
//import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
//import com.bestpay.bigdata.bi.common.dto.dataset.DatasetQueryDTO;
//import com.bestpay.bigdata.bi.common.dto.report.ColumnProperty;
//import com.bestpay.bigdata.bi.common.dto.report.QueryReportConditionInfo;
//import com.bestpay.bigdata.bi.common.entity.UserInfo;
//import com.bestpay.bigdata.bi.common.enums.DataSetFieldTypeEnum;
//import com.bestpay.bigdata.bi.common.enums.QueryType;
//import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
//import com.bestpay.bigdata.bi.common.request.metaData.MetaDataQueryFieldRequest;
//import com.bestpay.bigdata.bi.common.response.Response;
//import com.bestpay.bigdata.bi.database.api.dashboard.DashboardFilterCardService;
//import com.bestpay.bigdata.bi.database.api.dashboard.DashboardIndexTextCardService;
//import com.bestpay.bigdata.bi.database.api.dataset.DatasetConfigDAOService;
//import com.bestpay.bigdata.bi.database.api.dataset.DatasetDAOService;
//import com.bestpay.bigdata.bi.database.api.dataset.DatasetElementDAOService;
//import com.bestpay.bigdata.bi.database.api.dataset.DatasetReversionService;
//import com.bestpay.bigdata.bi.database.api.report.ReportService;
//import com.bestpay.bigdata.bi.database.bean.dataset.DatasetReversionDo;
//import com.bestpay.bigdata.bi.database.bean.report.Report;
//import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO;
//import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO;
//import com.bestpay.bigdata.bi.database.dao.dataset.DatasetConfigDo;
//import com.bestpay.bigdata.bi.database.dao.dataset.DatasetDo;
//import com.bestpay.bigdata.bi.database.dao.dataset.DatasetElementDo;
//import com.bestpay.bigdata.bi.database.dao.dataset.DatasetFieldDo;
//import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
//import com.bestpay.bigdata.bi.report.service.impl.dataset.DatasetElementServiceImpl;
//import com.bestpay.bigdata.bi.report.service.impl.dataset.DatasetMetaDataServiceImpl;
//import com.bestpay.bigdata.bi.report.service.impl.dataset.DatasetServiceImpl;
//import com.bestpay.bigdata.bi.report.sql.provider.generate.ParseSqlContent;
//import com.bestpay.bigdata.bi.report.sql.provider.part.FromPart;
//import com.bestpay.bigdata.bi.report.sql.provider.part.SelectItem;
//import com.bestpay.bigdata.bi.report.sql.provider.part.SelectPart;
//import com.bestpay.bigdata.bi.report.sql.provider.part.SqlContent;
//import com.google.common.collect.Lists;
//import java.io.Serializable;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import javax.annotation.Resource;
//import javax.sql.DataSource;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.exception.ExceptionUtils;
//import org.springframework.dao.DataAccessException;
//import org.springframework.jdbc.core.BeanPropertyRowMapper;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.jdbc.datasource.DriverManagerDataSource;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * @create 2023-07-14-13:31
// */
//@Slf4j
//@Component
//public class IndexProcessDatasetReversion {
//
//
//    @Resource
//    private DatasetServiceImpl datasetService;
//    @Resource
//    private DatasetElementServiceImpl datasetElementService;
//    @Resource
//    private DatasetMetaDataServiceImpl metaDataService;
//    @Resource
//    private AiPlusUserService aiPlusUserService;
//    @Resource
//    private DatasetDAOService datasetDAOService;
//    @Resource
//    private DatasetElementDAOService elementDAOService;
//    @Resource
//    private DatasetConfigDAOService configDAOService;
//    @Resource
//    private ReportService reportService;
//    @Resource
//    private DashboardIndexTextCardService indexTextCardService;
//    @Resource
//    private DashboardFilterCardService filterCardService;
//    @Resource
//    private ApolloRefreshConfig apolloRefreshConfig;
//    @Resource
//    private DatasetReversionService datasetReversionService;
//
//
//    public Response indexProcessReversion() {
//
//        JdbcTemplate biJdbcTemplate = new JdbcTemplate();
//        biJdbcTemplate.setDataSource(biDataSource());
//
//        JdbcTemplate indexJdbcTemplate = new JdbcTemplate();
//        indexJdbcTemplate.setDataSource(indexProcessDataSource());
//        List<DataSet> dataSets = indexJdbcTemplate.query("select * from t_data_set where status_code != 9 ", new BeanPropertyRowMapper<>(DataSet.class));
//        log.info("query all data set info from index process, size : {}", dataSets.size());
//
//        List<DashboardFilterCardDO> filterCardDOList = biJdbcTemplate.query("select * from t_dashboard_filter_card where status_code != 9", new BeanPropertyRowMapper<>(DashboardFilterCardDO.class));
//        log.info("query all t_dashboard_filter_card records from bi, size : {}", filterCardDOList.size());
//
//        List<Long> idList = new ArrayList<>();
//        try {
//            for (DashboardFilterCardDO dashboardFilterCardDO : filterCardDOList) {
//                Long oldDatasetId = dashboardFilterCardDO.getDatasetId();
//                DataSet dataSet = findById(oldDatasetId, dataSets);
//
//                if (Objects.nonNull(dataSet)) {
//                    String name = dataSet.getName();
//                    DatasetQueryDTO queryDTO = DatasetQueryDTO.builder().name(name).build();
//                    List<DatasetDo> datasetDoList = datasetDAOService.query(queryDTO);
//
////                    if (CollectionUtils.isNotEmpty(datasetDoList)) {
////                        DatasetInfo datasetInfo = JSONUtil.toBean(dashboardFilterCardDO.getDataSetInfo(), DatasetInfo.class);
////                        if (Objects.isNull(datasetInfo.getDatasetId())) {
////                            DatasetDo datasetDo = datasetDoList.get(0);
////                            datasetInfo.setDatasetId(datasetDo.getId());
////                            datasetInfo.setDatasetName(datasetDo.getName());
////                            datasetInfo.setName(datasetDo.getName());
////                            dashboardFilterCardDO.setDataSetInfo(JSONUtil.toJsonStr(datasetInfo));
////                            filterCardService.updateAll(dashboardFilterCardDO);
////                            idList.add(dashboardFilterCardDO.getId());
////                        }
////                    }
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return Response.ok(idList);
//    }
//
//
//    private DataSet findById(Long oldDatasetId, List<DataSet> dataSets) {
//        for (DataSet dataSet : dataSets) {
//            if (oldDatasetId.equals(dataSet.getId())) {
//                return dataSet;
//            }
//        }
//        return null;
//    }
//
//
//    public Response indexProcessDataReversion() {
//
//        log.info("Sit tight and start the data correction !!!");
//        // empty bi t_dataset, t_dataset_element, t_dataset_config, t_dataset_field
//        JdbcTemplate biJdbcTemplate = new JdbcTemplate();
//        biJdbcTemplate.setDataSource(biDataSource());
//
//        biJdbcTemplate.execute("truncate table t_dataset");
//        biJdbcTemplate.execute("truncate table t_dataset_element");
//        biJdbcTemplate.execute("truncate table t_dataset_config");
//        biJdbcTemplate.execute("truncate table t_dataset_field");
//
//        log.info("finish bi empty t_dataset, t_dataset_element, t_dataset_config, t_dataset_field ....");
//        UserInfoRequest userInfoRequest = new UserInfoRequest();
//        userInfoRequest.setPageNum(1);
//        userInfoRequest.setPageSize(3000);
//        List<UserInfo> userInfoList = aiPlusUserService.getUserList(userInfoRequest);
//
//        log.info("finish get userInfo List from aiplus !!!, userInfoList : {}", userInfoList);
//        // query all data set info from index process
//        JdbcTemplate indexJdbcTemplate = new JdbcTemplate();
//        indexJdbcTemplate.setDataSource(indexProcessDataSource());
//        List<DataSet> dataSets = indexJdbcTemplate.query("select * from t_data_set where status_code != 9 ", new BeanPropertyRowMapper<>(DataSet.class));
//        log.info("query all data set info from index process, size : {}", dataSets.size());
//
//        Map<Long, Map<String, Long>> dataMap = new HashMap<>();
//        Map<Long, Long> oldDatasetIdMapNew = new HashMap<>();
//
//        List<Long> failDatasetIdList = new ArrayList<>();
//        for (DataSet dataSet : dataSets) {
//
//            DatasetReversionDo datasetReversionDo = new DatasetReversionDo();
//            DatasetDo datasetDo = null;
//            try {
//                Map<String, Long> enNameIdMap = new HashMap<>();
//
//                datasetReversionDo.setTableName("t_data_set");
//                datasetReversionDo.setTableId(dataSet.getId());
//                datasetReversionDo.setDatasetInfo(JSONUtil.toJsonStr(dataSet));
//
//                Long id = dataSet.getId();
//                log.info(" start process dataset from index process , id : {}", id);
//                DatasetTable datasetTable = indexJdbcTemplate.queryForObject("select * from t_dataset_table where dataset_id = ?", new BeanPropertyRowMapper<>(DatasetTable.class), id);
//                log.info("datasetTable from index process, datasetTable : {}", datasetTable);
//                Long sourceTableId = datasetTable.getSourceTableId();
//                SourceTable sourceTable = indexJdbcTemplate.queryForObject("select * from t_source_table where id = ?", new BeanPropertyRowMapper<>(SourceTable.class), sourceTableId);
//                log.info("sourceTable from index process, sourceTable : {}", sourceTable);
//
//                if (Objects.isNull(sourceTable.getDatasourceType()) || sourceTable.getDatasourceType().toLowerCase().equals("hive")) {
//                    boolean flag = false;
//                    List<DatasetScene> datasetSceneList = indexJdbcTemplate.query("select * from t_dataset_scene where dataset_id = ?", new BeanPropertyRowMapper<>(DatasetScene.class), id);
//                    for (DatasetScene datasetScene : datasetSceneList) {
//                        if (datasetScene.getCode().equals(0)) {
//                            // BI场景
//                            flag = true;
//                            break;
//                        }
//                    }
//                    if (!flag) {
//                        continue;
//                    }
//                }
//
//                // Transfer to bi t_dataset
//                datasetDo = new DatasetDo();
//                datasetDo.setName(dataSet.getName());
//                datasetDo.setStatusCode(dataSet.getStatusCode() == 0 ? 3 : 2); // 3 online 2 offline
//                datasetDo.setApplicationScenario(QueryType.QUERY_REPORT.getCode() + "");
//                datasetDo.setCode(datasetService.getDatasetCode());
//                datasetDo.setDatasourceName(sourceTable.getDatasourceType().toLowerCase().equals("hive") ? "ck_2" : sourceTable.getDatasourceName());
//                datasetDo.setDatasourceType(sourceTable.getDatasourceType().toLowerCase().equals("hive") ? "clickhouse" : sourceTable.getDatasourceType());
//                datasetDo.setType(dataSet.getTypeCode());
//                datasetDo.setCreatedAt(dataSet.getCreatedAt());
//                datasetDo.setCreatedBy(getUserEmail(userInfoList, dataSet.getCreatedBy()));
//                datasetDo.setUpdatedAt(dataSet.getUpdatedAt());
//                datasetDo.setUpdatedBy(getUserEmail(userInfoList, dataSet.getUpdatedBy()));
//                datasetDo.setOrgCode(dataSet.getOrgCode());
//                datasetDo.setOrgAuth(dataSet.getAccessOrgCode());
//                // owner
//                datasetDo.setOwner(getUserEmail(userInfoList, dataSet.getCreatedBy()));
//                datasetDo.setOwnerCn(getUserCnName(userInfoList, dataSet.getCreatedBy()));
//
//
//                // Transfer to bi t_dataset_element
//                DatasetElementDo datasetElementDo = new DatasetElementDo();
//                datasetElementDo.setCode(datasetElementService.getDatasetElementCode());
//                datasetElementDo.setDatasetCode(datasetDo.getCode());
//                datasetElementDo.setStatusCode(StatusCodeEnum.ONLINE.getCode());
//                if (sourceTable.getDatasourceType().toLowerCase().equals("hive")) {
//                    datasetElementDo.setName(sourceTable.getDatasourceName() + "_" + sourceTable.getDatabaseName() + "." + sourceTable.getName());
//                } else {
//                    datasetElementDo.setName(sourceTable.getDatabaseName() + "." + sourceTable.getName());
//                }
//                datasetElementDo.setLevel(1);
//                datasetElementDo.setSort(1);
//                datasetElementDo.setCreatedAt(dataSet.getCreatedAt());
//                datasetElementDo.setCreatedBy(getUserEmail(userInfoList, dataSet.getCreatedBy()));
//                datasetElementDo.setUpdatedAt(dataSet.getUpdatedAt());
//                datasetElementDo.setUpdatedBy(getUserEmail(userInfoList, dataSet.getUpdatedBy()));
//
//                // Transfer to bi t_dataset_config and t_dataset_field
//                MetaDataQueryFieldRequest fieldRequest = MetaDataQueryFieldRequest.builder()
//                        .datasourceType(datasetDo.getDatasourceType())
//                        .datasourceName(datasetDo.getDatasourceName())
//                        .dbName(datasetElementDo.getName().split("\\.")[0])
//                        .tableName(datasetElementDo.getName().split("\\.")[1])
//                        .build();
//
//                log.info("t_dataset_field fieldRequest : {}", fieldRequest);
//                Map<String, List<DatasetFieldDo>> metaFields = metaDataService.getMetaFields(Lists.newArrayList(fieldRequest));
//
//                List<DatasetFieldDo> datasetFieldDos = metaFields.get(fieldRequest.getUniqueKey());
//
//                // 校验是否能够根据 指标加工的数据集信息 从元数据获取到最新数据
//                if (CollectionUtils.isEmpty(datasetFieldDos)) {
//                    log.info("need check this fieldRequest from metadata : {}", fieldRequest);
//                    if (!failDatasetIdList.contains(dataSet.getId())) {
//                        failDatasetIdList.add(dataSet.getId());
//                    }
//                    datasetReversionDo.setMessage(JSONUtil.toJsonStr(fieldRequest) + "_获取元数据字段信息失败");
//                    datasetReversionDo.setStatus("fail");
//                    datasetReversionService.insert(datasetReversionDo);
//                    continue;
//                }
//
//                SqlContent sqlContent = new SqlContent();
//                List<SelectItem> selectItemList = new ArrayList<SelectItem>();
//                sqlContent.setFromPart(new FromPart(datasetElementDo.getName(), false));
//                for (DatasetFieldDo datasetFieldDo : datasetFieldDos) {
//                    selectItemList.add(new SelectItem(datasetFieldDo.getFieldEnName(), datasetFieldDo.getFieldEnName()));
//                }
//                sqlContent.setSelectPart(new SelectPart(selectItemList, false));
//                datasetDo.setQuerySql(ParseSqlContent.parseSqlContentToSqlStr(sqlContent));
//                datasetDAOService.insertOrUpdate(datasetDo);
//                datasetDo.setId(datasetDAOService.queryDoByDatasetCode(datasetDo.getCode()).getId());
//                log.info("insert into bi t_dataset, new id : {}", datasetDo.getId());
//                oldDatasetIdMapNew.put(id, datasetDo.getId());
//
//                elementDAOService.batchInsert(Lists.newArrayList(datasetElementDo));
//                log.info("insert into bi t_dataset, new element code : {}", datasetElementDo.getCode());
//
//                List<DatasetColumn> datasetColumns = indexJdbcTemplate.query("select * from t_dataset_column where dataset_id = ?", new BeanPropertyRowMapper<>(DatasetColumn.class), id);
//                log.info("datasetColumns : {}", datasetColumns);
//                for (DatasetColumn datasetColumn : datasetColumns) {
//
//                    DatasetFieldDo datasetFieldDo = getDatasetFieldDo(datasetFieldDos, datasetColumn.getEnName());
//                    if (Objects.nonNull(datasetFieldDo)) {
//                        DatasetConfigDo datasetConfigDo = new DatasetConfigDo();
//                        datasetConfigDo.setDatasetCode(datasetDo.getCode());
//                        datasetConfigDo.setElementCode(datasetElementDo.getCode());
//                        datasetConfigDo.setFieldId(datasetFieldDo.getId());
//                        datasetConfigDo.setSource(datasetElementDo.getName());
//                        datasetConfigDo.setLogicEnName(datasetColumn.getEnName());
//                        datasetConfigDo.setType(judgeDataSetFieldTypeEnum(datasetColumn).name());
//                        datasetConfigDo.setCreatedAt(dataSet.getCreatedAt());
//                        datasetConfigDo.setCreatedBy(getUserEmail(userInfoList, dataSet.getCreatedBy()));
//                        datasetConfigDo.setUpdatedAt(dataSet.getUpdatedAt());
//                        datasetConfigDo.setUpdatedBy(getUserEmail(userInfoList, dataSet.getUpdatedBy()));
//                        datasetConfigDo.setStatusCode(StatusCodeEnum.ONLINE.getCode());
//                        datasetConfigDo.setFieldFormat(getFieldFormat(datasetColumn));
//                        configDAOService.batchInsertOrUpdate(Lists.newArrayList(datasetConfigDo));
//
//                        // get config id
//                        DatasetConfigDo configDo = biJdbcTemplate.queryForObject("select * from t_dataset_config where element_code = ? and field_id = ? and `type` = ? and status_code != 9", new BeanPropertyRowMapper<>(DatasetConfigDo.class),
//                                datasetElementDo.getCode(), datasetFieldDo.getId(), judgeDataSetFieldTypeEnum(datasetColumn).name());
//
//                        if (StringUtils.isNoneEmpty(datasetColumn.getConvertEnName())) {
//                            enNameIdMap.put(datasetColumn.getConvertEnName(), configDo.getId());
//                        } else {
//                            enNameIdMap.put(datasetColumn.getEnName(), configDo.getId());
//                        }
//                    } else {
//                        log.info("data set config data exception, datasetColumn.getEnName : {}", datasetColumn.getEnName());
//
//                        datasetReversionDo.setMessage("data set config data exception, datasetColumn.getEnName " + datasetColumn.getEnName());
//                        datasetReversionDo.setStatus("fail");
//                        datasetReversionService.insert(datasetReversionDo);
//                    }
//                }
//                dataMap.put(datasetDo.getId(), enNameIdMap);
//            } catch (Exception e) {
//                e.printStackTrace();
//                String fullStackTrace = ExceptionUtils.getStackTrace(e);
//
//                datasetReversionDo.setMessage(fullStackTrace);
//                datasetReversionDo.setStatus("fail");
//                datasetReversionService.insert(datasetReversionDo);
//                if (datasetDo != null) {
//                    deleteBiData(datasetDo.getCode(), biJdbcTemplate);
//                }
//                if (!failDatasetIdList.contains(dataSet.getId())) {
//                    failDatasetIdList.add(dataSet.getId());
//                }
//            }
//        }
//        log.info("dataMap key set : {}", dataMap.keySet());
//
//        // query all t_report records from bi
//        List<Report> reportList = biJdbcTemplate.query("select * from t_report where status_code !=9", new BeanPropertyRowMapper<>(Report.class));
//        log.info("query all t_report records from bi, size :{}", reportList.size());
//
//        List<DashboardIndexTextCardDO> indexTextCardDOList =  biJdbcTemplate.query("select * from t_dashboard_index_text_card where status_code != 9", new BeanPropertyRowMapper<>(DashboardIndexTextCardDO.class));
//        log.info("query all t_dashboard_index_text_card records from bi, size : {}", indexTextCardDOList.size());
//
//        List<DashboardFilterCardDO> filterCardDOList = biJdbcTemplate.query("select * from t_dashboard_filter_card where status_code != 9", new BeanPropertyRowMapper<>(DashboardFilterCardDO.class));
//        log.info("query all t_dashboard_filter_card records from bi, size : {}", filterCardDOList.size());
//
//
//        // 订正BI侧 t_report 表 中 dataset_info字段
//        List<Long> failReportIdList = reversionReport(reportList, biJdbcTemplate, dataMap, oldDatasetIdMapNew);
//
//        log.info("failReportIdList : {}", failReportIdList);
//        // 订正BI侧 t_dashboard_index_text_card
//        List<Long> failIndexTextIdList = reversionIndexTextCardDataset(indexTextCardDOList, biJdbcTemplate, dataMap, oldDatasetIdMapNew);
//        log.info("failIndexTextIdList : {}", failIndexTextIdList);
//
//        // 订正BI侧 t_dashboard_filter_card
//        List<Long> failFilterCardIdList = reversionFilterCardDataset(filterCardDOList, biJdbcTemplate, dataMap, oldDatasetIdMapNew);
//        log.info("failFilterCardIdList : {}", failFilterCardIdList);
//
//
//        Map<String, List<Long>> result = new HashMap<>();
//        result.put("t_data_set", failDatasetIdList);
//        result.put("t_report", failReportIdList);
//        result.put("t_dashboard_index_text_card", failIndexTextIdList);
//        result.put("t_dashboard_filter_card", failFilterCardIdList);
//        log.info("result : {}", result );
//        return Response.ok(result);
//    }
//
//
//    private void deleteBiData(String datasetCode, JdbcTemplate biJdbcTemplate) {
//        try {
//            String datasetSql = "delete from t_dataset where code = '%s'";
//            biJdbcTemplate.execute(String.format(datasetSql, datasetCode));
//
//            String elementSql = "delete from t_dataset_element where dataset_code = '%s'";
//            biJdbcTemplate.execute(String.format(elementSql, datasetCode));
//        } catch (DataAccessException e) {
//        }
//    }
//
//
//    private List<Long> reversionReport(List<Report> reportList, JdbcTemplate biJdbcTemplate, Map<Long, Map<String, Long>> dataMap, Map<Long, Long> oldDatasetIdMapNew) {
//
//        List<Long> idList = new ArrayList<>();
//        for (Report report : reportList) {
//            DatasetReversionDo datasetReversionDo = new DatasetReversionDo();
//
//            datasetReversionDo.setTableName("t_report");
//            datasetReversionDo.setTableId(report.getId());
//            datasetReversionDo.setDatasetInfo(report.getDatasetInfo());
//
//            try {
//                if (reversionReportDataset(report, biJdbcTemplate, dataMap, oldDatasetIdMapNew)) {
//                    log.info("after reversion Report : {}", JSONUtil.toJsonStr(report));
//                    reportService.update(report);
//                    log.info("finish update report id : {}", report.getId());
//                } else {
//                    idList.add(report.getId());
//                    datasetReversionDo.setMessage("report 竟然没更新");
//                    datasetReversionDo.setStatus("fail");
//                    datasetReversionService.insert(datasetReversionDo);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                idList.add(report.getId());
//                String fullStackTrace = ExceptionUtils.getStackTrace(e);
//                datasetReversionDo.setMessage(fullStackTrace);
//                datasetReversionDo.setStatus("fail");
//                datasetReversionService.insert(datasetReversionDo);
//            }
//        }
//        return idList;
//    }
//
//
//    private boolean reversionReportDataset(Report report, JdbcTemplate biJdbcTemplate, Map<Long, Map<String, Long>> dataMap, Map<Long, Long> oldDatasetIdMapNew) {
//        boolean flag = false;
//
//        Map<String, Long> enNameIdMap = new HashMap<>();
//
//        if (StringUtils.isNoneEmpty(report.getDatasetInfo())) {
//            List<DatasetInfo> datasetInfos = null;
//            try {
//                datasetInfos = JSONUtil.toList(report.getDatasetInfo(), DatasetInfo.class);
//                // dataset
//                for (DatasetInfo datasetInfo : datasetInfos) {
//                    if (Objects.nonNull(datasetInfo) && Objects.nonNull(datasetInfo.getDatasetId())) {
//                        Long newDatasetId = oldDatasetIdMapNew.get(datasetInfo.getDatasetId());
//                        DatasetDo datasetDo = biJdbcTemplate.queryForObject("select * from t_dataset where `id` = ?", new BeanPropertyRowMapper<>(DatasetDo.class), newDatasetId);
//                        log.info("datasetDo : {}", datasetDo);
//                        if (datasetDo != null) {
//                            datasetInfo.setDatasetId(datasetDo.getId());
//                            flag = true;
//                            if (datasetInfo.getDataSourceType().equals("hive")) {
//                                datasetInfo.setDataBaseName(datasetInfo.getDataSourceName() + "_" + datasetInfo.getDataBaseName());
//                                datasetInfo.setDataSourceType(datasetDo.getDatasourceType());
//                                datasetInfo.setDataSourceName(datasetDo.getDatasourceName());
//                            }
//                            enNameIdMap = dataMap.get(datasetDo.getId());
//                            log.info("enNameIdMap : {}", enNameIdMap);
//                            if (enNameIdMap == null) {
//                                return false;
//                            }
//                        }
//                    }
//                }
//                report.setDatasetInfo(JSONUtil.toJsonStr(datasetInfos));
//            } catch (Exception e) {
//                e.printStackTrace();
//                return false;
//            }
//        }
//
//        // show_column
//        // [{"showTypeName":"CHARACTER_SELECT","name":"流水申请状态","enName":"apply_stat","id":5870},{"showTypeName":"CHARACTER_INPUT","name":"事业群代码","enName":"dept_cd","id":5892},{"showTypeName":"CHARACTER_INPUT","name":"事业群名称","enName":"dept_nm","id":5893},{"showTypeName":"CHARACTER_SELECT","name":"手机归属省份CD","enName":"mobile_prov_cd","id":5912},{"showTypeName":"CHARACTER_SELECT","name":"手机归属省份NM","enName":"mobile_prov_nm","id":5913}]
//        if (StringUtils.isNoneEmpty(report.getShowColumn())) {
//            String str = setJsonColumnPropertyConfigId(report.getShowColumn(), enNameIdMap);
//            if (Objects.nonNull(str)) {
//                flag = true;
//                report.setShowColumn(str);
//            }
//        }
//
//        // index_column
//        // [{"polymerization":"count","nickName":"创建时间","indexName":"create_dt_index","calculateLogic":"count(create_dt)","showTypeName":"DATETIME","showSubtotal":false,"reportField":"index","name":"创建时间","enName":"create_dt","id":13053}]
//        if (StringUtils.isNoneEmpty(report.getIndexColumn())) {
//            String str = setJsonColumnPropertyConfigId(report.getIndexColumn(), enNameIdMap);
//            if (Objects.nonNull(str)) {
//                flag = true;
//                report.setIndexColumn(str);
//            }
//        }
//
//        // filter_column
//        // [{"fieldName":"crdt_lmt","values":[],"reportField":"field","stringValue":"5000000","name":"授信额度","id":11096,"fieldType":"DECIMAL","scopeFilterType":"greaterOrEquals"}]
//        if (StringUtils.isNoneEmpty(report.getFilterColumn())) {
//            String str = setJsonReportConditionInfoConfigId(report.getFilterColumn(), enNameIdMap);
//            if (Objects.nonNull(str)) {
//                flag = true;
//                report.setFilterColumn(str);
//            }
//        }
//
//        // con_control
//        // [{"showTypeName":"CHARACTER_INPUT","name":"事业群名称","enName":"dept_nm","id":5893},{"showTypeName":"CHARACTER_SELECT","name":"手机归属省份CD","enName":"mobile_prov_cd","id":5912},{"showTypeName":"CHARACTER_SELECT","name":"流水申请状态","enName":"apply_stat","id":5870}]
//        if (StringUtils.isNoneEmpty(report.getCondition())) {
//            String str = setJsonReportConditionInfoConfigId(report.getCondition(), enNameIdMap);
//            if (Objects.nonNull(str)) {
//                flag = true;
//                report.setCondition(str);
//            }
//        }
//
//        // keyword
//        // [{"showTypeName":"CHARACTER_SELECT","name":"流水申请状态","enName":"apply_stat","id":5675},{"showTypeName":"CHARACTER_INPUT","name":"批次申请状态","enName":"batch_apply_stat","id":5680}]
//        if (StringUtils.isNoneEmpty(report.getKeyword())) {
//            String str = setJsonColumnPropertyConfigId(report.getKeyword(), enNameIdMap);
//            if (Objects.nonNull(str)) {
//                flag = true;
//                report.setKeyword(str);
//            }
//        }
//
//        // order_column
//        // [{"id":13923,"name":"星座","enName":"star_sign","showTypeName":"CHARACTER_INPUT","reportField":"field","type":"ASC"}]
//        if (StringUtils.isNoneEmpty(report.getOrderColumn())) {
//            String str = setJsonColumnPropertyConfigId(report.getOrderColumn(), enNameIdMap);
//            if (Objects.nonNull(str)) {
//                flag = true;
//                report.setOrderColumn(str);
//            }
//        }
//
//        // contrast_column
//        // [{"id":1,"name":"度量名","enName":"measureName","showTypeName":"MEASURE","reportField":"measure"}]
//        // [{"id":13609,"name":"创建时间","enName":"create_dt","showTypeName":"DATETIME","reportField":"contrast","dateGroupType":1},{"id":13610,"name":"更新时间","enName":"upd_dt","showTypeName":"DATETIME","reportField":"contrast","dateGroupType":1},{"id":1,"name":"度量名","enName":"measureName","showTypeName":"MEASURE","reportField":"measure"}]
//        if (StringUtils.isNoneEmpty(report.getContrastColumn())) {
//            String str = setJsonColumnPropertyConfigId(report.getContrastColumn(), enNameIdMap);
//            if (Objects.nonNull(str)) {
//                flag = true;
//                report.setContrastColumn(str);
//            }
//        }
//
//        if (StringUtils.isNotEmpty(report.getSensitiveFields())) {
//            String str = setJsonColumnPropertyConfigId(report.getSensitiveFields(), enNameIdMap);
//            if (Objects.nonNull(str)) {
//                flag = true;
//                report.setSensitiveFields(str);
//            }
//        }
//
//        return flag;
//    }
//
//    private String setJsonColumnPropertyConfigId(String json, Map<String, Long> enNameIdMap) {
//        boolean flag = false;
//        List<ColumnProperty> showColumnList = JSONUtil.toList(json, ColumnProperty.class);
//        for (ColumnProperty columnProperty : showColumnList) {
//            if (StringUtils.isEmpty(columnProperty.getFun())) {
//                // 非计算字段
//                Long id = enNameIdMap.get(columnProperty.getEnName());
//                if (Objects.nonNull(id)) {
//                    columnProperty.setId(id);
//                    flag = true;
//                }
//            }
//        }
//
//        if (flag) {
//            return JSONUtil.toJsonStr(showColumnList);
//        } else {
//            return null;
//        }
//    }
//
//
//    private String setJsonReportConditionInfoConfigId(String json, Map<String, Long> enNameIdMap) {
//        boolean flag = false;
//        List<QueryReportConditionInfo> reportConditionInfoList = JSONUtil.toList(json, QueryReportConditionInfo.class);
//        for (QueryReportConditionInfo columnProperty : reportConditionInfoList) {
//            if (StringUtils.isEmpty(columnProperty.getFun())) {
//                // 非计算字段
//                Long id = enNameIdMap.get(columnProperty.getFieldName());
//                if (Objects.nonNull(id)) {
//                    columnProperty.setId(id);
//                    flag = true;
//                }
//            }
//        }
//
//        if (flag) {
//            return JSONUtil.toJsonStr(reportConditionInfoList);
//        } else {
//            return null;
//        }
//    }
//
//
//
//    private List<Long> reversionIndexTextCardDataset(List<DashboardIndexTextCardDO> indexTextCardDOList, JdbcTemplate biJdbcTemplate, Map<Long, Map<String, Long>> dataMap, Map<Long, Long> oldDatasetIdMapNew) {
//        List<Long> idList = new ArrayList<>();
//
//        for (DashboardIndexTextCardDO dashboardIndexTextCardDO : indexTextCardDOList) {
//
//            DatasetReversionDo datasetReversionDo = new DatasetReversionDo();
//
//            datasetReversionDo.setTableName("t_dashboard_index_text_card");
//            datasetReversionDo.setTableId(dashboardIndexTextCardDO.getId());
//            datasetReversionDo.setRelateId(dashboardIndexTextCardDO.getDashboardId());
////            datasetReversionDo.setDatasetInfo(dashboardIndexTextCardDO.getDatasetInfo());
//
//            try {
//                log.info("pre reversionIndexTextCardDataset : {}", dashboardIndexTextCardDO);
//                boolean flag = false;
//
//                Map<String, Long> enNameIdMap = new HashMap<>();
//                DatasetDo datasetDo = null;
//
////                // dataset_info
////                if (StringUtils.isNoneEmpty(dashboardIndexTextCardDO.getDatasetInfo())) {
////                    DatasetInfo datasetInfo = JSONUtil.toBean(dashboardIndexTextCardDO.getDatasetInfo(), DatasetInfo.class);
////                    if (Objects.nonNull(datasetInfo) && Objects.nonNull(datasetInfo.getDatasetId())) {
////                        Long newDatasetId = oldDatasetIdMapNew.get(datasetInfo.getDatasetId());
////                        datasetDo = biJdbcTemplate.queryForObject("select * from t_dataset where `id` = ?", new BeanPropertyRowMapper<>(DatasetDo.class), newDatasetId);
////                        log.info("datasetDo : {}", datasetDo);
////                        if (datasetDo != null) {
////                            datasetInfo.setDatasetId(datasetDo.getId());
////                            flag = true;
////                            if ((Objects.nonNull(datasetInfo.getDataSourceType()) && datasetInfo.getDataSourceType().equals("hive")) ||
////                                    (datasetInfo.getDataSourceName().startsWith("hive"))) {
////                                datasetInfo.setDataBaseName(datasetInfo.getDataSourceName() + "_" + datasetInfo.getDataBaseName());
////                                datasetInfo.setDataSourceType(datasetDo.getDatasourceType());
////                                datasetInfo.setDataSourceName(datasetDo.getDatasourceName());
////                            }
////                            dashboardIndexTextCardDO.setDatasetInfo(JSONUtil.toJsonStr(datasetInfo));
////                            enNameIdMap = dataMap.get(datasetDo.getId());
////                        }
////                    }
////                }
//
////                // search_data
////                // {"filterConditions":[],"showColumnList":[],"reportType":1,"keywordList":[],"conditionList":[],"datasetInfoList":[{"dataSourceName":"hive_11","tableName":"dws_crdt_l_apply_detail_df","name":"测试新建数据集","datasetId":322,"dataBaseName":"cdm_dsj","dataSourceType":"hive"}],"indexColumnList":[{"polymerization":"count","calculateLogic":"count(apply_dt)","showTypeName":"DATETIME","reportField":"index","name":"申请日期","enName":"apply_dt_index","id":10019}],"chartType":0,"orgSelected":"20005","orderColumn":"[]"}
////                if (StringUtils.isNoneEmpty(dashboardIndexTextCardDO.getSearchData())) {
////                    ReportRequest reportRequest = JSONUtil.toBean(dashboardIndexTextCardDO.getSearchData(), ReportRequest.class);
////
////                    if (setConfigId(reportRequest.getShowColumnList(), enNameIdMap)) {
////                        flag = true;
////                    }
////
////                    if (setConfigId(reportRequest.getIndexColumnList(), enNameIdMap)) {
////                        flag = true;
////                    }
////
////                    if (setConfigId(reportRequest.getConditionList(), enNameIdMap)) {
////                        flag = true;
////                    }
////
////                    if (setConfigId(reportRequest.getKeywordList(), enNameIdMap)) {
////                        flag = true;
////                    }
////
////                    if (setReportConditionInfoConfigId(reportRequest.getFilterConditions(), enNameIdMap)) {
////                        flag = true;
////                    }
////
////                    if (setReportConditionInfoConfigId(reportRequest.getQueryConditions(), enNameIdMap)) {
////                        flag = true;
////                    }
////
////                    if (CollectionUtils.isNotEmpty(reportRequest.getDatasetInfoList())) {
////                        for (DatasetInfo info : reportRequest.getDatasetInfoList()) {
////                            if (Objects.nonNull(info.getDatasetId())) {
////                                Long newDatasetId = oldDatasetIdMapNew.get(info.getDatasetId());
////                                DatasetDo newDatasetDo = biJdbcTemplate.queryForObject("select * from t_dataset where `id` = ?", new BeanPropertyRowMapper<>(DatasetDo.class), newDatasetId);
////                                info.setDatasetId(newDatasetDo.getId());
////
////                                if ((Objects.nonNull(info.getDataSourceType()) && info.getDataSourceType().equals("hive")) ||
////                                        (info.getDataSourceName().startsWith("hive"))) {
////                                    info.setDataBaseName(info.getDataSourceName() + "_" + info.getDataBaseName());
////                                    info.setDataSourceType(newDatasetDo.getDatasourceType());
////                                    info.setDataSourceName(newDatasetDo.getDatasourceName());
////                                }
////                                flag = true;
////                            }
////                        }
////                    }
////
////                    if (flag) {
////                        dashboardIndexTextCardDO.setSearchData(JSONUtil.toJsonStr(reportRequest));
////                    }
////                }
//
//                // index_info
//                // {"showFiled":true,"styleGroup":0,"reportType":1,"name":"love","orgSelected":"20005","dataSet":322,"desc":"nice"}
//                if (StringUtils.isNotEmpty(dashboardIndexTextCardDO.getIndexInfo())) {
//                    JSONObject jsonObject = JSONUtil.parseObj(dashboardIndexTextCardDO.getIndexInfo());
//                    long dataSetId = Long.parseLong(jsonObject.get("dataSet").toString());
//                    Long newDatasetId = oldDatasetIdMapNew.get(dataSetId);
//                    if (newDatasetId != null) {
//                        jsonObject.set("dataSet", datasetDo.getId());
//                        flag = true;
//                        dashboardIndexTextCardDO.setIndexInfo(jsonObject.toString());
//                    }
//                }
//
//                // drag_result
//                // [{"polymerization":"count","calculateLogic":"count(operator_id)","showTypeName":"CHARACTER_INPUT","reportField":"index","advancedComputing":{"type":0,"dateGroupType":1,"dateType":0,"enName":"apply_dt","name":"申请日期"},"name":"翼支付用户ID","enName":"operator_id","id":10012}]
//                if (StringUtils.isNotEmpty(dashboardIndexTextCardDO.getDragResult())) {
//                    String str = setJsonColumnPropertyConfigId(dashboardIndexTextCardDO.getDragResult(), enNameIdMap);
//                    if (Objects.nonNull(str)) {
//                        dashboardIndexTextCardDO.setDragResult(str);
//                        flag = true;
//                    }
//                }
//
//                // filterdrag_result
//                // [{"fieldName":"mobile_prov_nm","showTypeName":"CHARACTER_SELECT","values":[],"reportField":"field","originalName":"省份","stringValue":"上海市","name":"省份","enName":"mobile_prov_nm","id":14070,"fieldType":"CHARACTER_SELECT","scopeFilterType":"in"}]
//                if (StringUtils.isNoneEmpty(dashboardIndexTextCardDO.getFilterdragResult())) {
//                    List<QueryReportConditionInfo> filterDragResultList = JSONUtil.toList(dashboardIndexTextCardDO.getFilterdragResult(), QueryReportConditionInfo.class);
//                    if (CollectionUtils.isNotEmpty(filterDragResultList)) {
//                        if (setReportConditionInfoConfigId(filterDragResultList, enNameIdMap)) {
//                            dashboardIndexTextCardDO.setFilterdragResult(JSONUtil.toJsonStr(filterDragResultList));
//                            flag = true;
//                        }
//                    }
//                }
//
//                // count_filed_list
//                // [{"indexName":"test","showTypeName":"DECIMAL","reportField":"index","name":"计算字段","enName":"test","id":46090958,"fun":"COUNT(succe_lsjr_cust_id)/COUNT(lsjr_cust_id)"}]
//                if (StringUtils.isNotEmpty(dashboardIndexTextCardDO.getCountFiledList())) {
//                    String str = setJsonColumnPropertyConfigId(dashboardIndexTextCardDO.getCountFiledList(), enNameIdMap);
//                    if (Objects.nonNull(str)) {
//                        dashboardIndexTextCardDO.setCountFiledList(str);
//                        flag = true;
//                    }
//                }
//
//                if (flag) {
//                    indexTextCardService.update(dashboardIndexTextCardDO);
//                    log.info("after reversionIndexTextCardDataset : {}", dashboardIndexTextCardDO);
//                } else {
//                    idList.add(dashboardIndexTextCardDO.getId());
//                    datasetReversionDo.setMessage("竟然没更新");
//                    datasetReversionDo.setStatus("fail");
//                    datasetReversionService.insert(datasetReversionDo);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                idList.add(dashboardIndexTextCardDO.getId());
//
//                String fullStackTrace = ExceptionUtils.getStackTrace(e);
//                datasetReversionDo.setMessage(fullStackTrace);
//                datasetReversionDo.setStatus("fail");
//                datasetReversionService.insert(datasetReversionDo);
//            }
//        }
//        return idList;
//    }
//
//
//    private boolean setConfigId(List<ColumnProperty> columnPropertyList, Map<String, Long> enNameIdMap) {
//        boolean flag = false;
//        if (CollectionUtils.isNotEmpty(columnPropertyList)) {
//            for (ColumnProperty columnProperty : columnPropertyList) {
//                if (StringUtils.isEmpty(columnProperty.getFun())) {
//                    // 非计算字段
//                    Long id = enNameIdMap.get(columnProperty.getEnName());
//                    if (Objects.nonNull(id)) {
//                        columnProperty.setId(id);
//                        flag = true;
//                    }
//                }
//            }
//        }
//        return flag;
//    }
//
//
//    private boolean setReportConditionInfoConfigId(List<QueryReportConditionInfo> conditionInfoList, Map<String, Long> enNameIdMap) {
//        boolean flag = false;
//        if (CollectionUtils.isNotEmpty(conditionInfoList)) {
//            for (QueryReportConditionInfo queryReportConditionInfo : conditionInfoList) {
//                if (StringUtils.isEmpty(queryReportConditionInfo.getFun())) {
//                    // 非计算字段
//                    Long id = enNameIdMap.get(queryReportConditionInfo.getFieldName());
//                    if (Objects.nonNull(id)) {
//                        queryReportConditionInfo.setId(id);
//                        flag = true;
//                    }
//                }
//            }
//        }
//        return flag;
//    }
//
//
//
//    private List<Long> reversionFilterCardDataset(List<DashboardFilterCardDO> filterCardDOList, JdbcTemplate biJdbcTemplate, Map<Long, Map<String, Long>> dataMap, Map<Long, Long> oldDatasetIdMapNew) {
//
//        List<Long> idList = new ArrayList<>();
//        for (DashboardFilterCardDO dashboardFilterCardDO : filterCardDOList) {
//
//
//            DatasetReversionDo datasetReversionDo = new DatasetReversionDo();
//
//            datasetReversionDo.setTableName("t_dashboard_filter_card");
//            datasetReversionDo.setTableId(dashboardFilterCardDO.getId());
//            datasetReversionDo.setRelateId(dashboardFilterCardDO.getDashboardId());
////            datasetReversionDo.setDatasetInfo(dashboardFilterCardDO.getDataSetInfo());
//
//            try {
//                log.info("pre reversion FilterCard Dataset : {}", dashboardFilterCardDO);
//                boolean flag = false;
//                Map<String, Long> enNameIdMap = new HashMap<>();
//
//                // data_set_info
////                if (StringUtils.isNoneEmpty(dashboardFilterCardDO.getDataSetInfo())) {
////                    DatasetInfo datasetInfo = JSONUtil.toBean(dashboardFilterCardDO.getDataSetInfo(), DatasetInfo.class);
////                    if (Objects.nonNull(datasetInfo) && Objects.nonNull(datasetInfo.getDatasetId())) {
////                        Long newDatasetId = oldDatasetIdMapNew.get(datasetInfo.getDatasetId());
////                        DatasetDo datasetDo = biJdbcTemplate.queryForObject("select * from t_dataset where `id` = ?", new BeanPropertyRowMapper<>(DatasetDo.class), newDatasetId);
////                        log.info("datasetDo : {}", datasetDo);
////                        if (datasetDo != null) {
////                            datasetInfo.setDatasetId(datasetDo.getId());
////                            flag = true;
////                            if ((Objects.nonNull(datasetInfo.getDataSourceType()) && datasetInfo.getDataSourceType().equals("hive")) ||
////                                    (datasetInfo.getDataSourceName().startsWith("hive"))) {
////                                datasetInfo.setDataBaseName(datasetInfo.getDataSourceName() + "_" + datasetInfo.getDataBaseName());
////                                datasetInfo.setDataSourceType(datasetDo.getDatasourceType());
////                                datasetInfo.setDataSourceName(datasetDo.getDatasourceName());
////                            }
////                            dashboardFilterCardDO.setDataSetInfo(JSONUtil.toJsonStr(datasetInfo));
////                            enNameIdMap = dataMap.get(datasetDo.getId());
////                        }
////                    } else {
////                        datasetReversionDo.setMessage("数据集信息没有 id");
////                    }
////                }
//
//                // fields
//                // {"defaultValue":"recentThrM","showTypeName":"DATETIME","typeName":"Nullable(String)","enName":"toDateTime(tm)","name":"日期","id":13894,"value":[]}
//                if (StringUtils.isNotEmpty(dashboardFilterCardDO.getFields())) {
//                    JSONObject jsonObject = JSONUtil.parseObj(dashboardFilterCardDO.getFields());
//                    String enName = jsonObject.get("enName").toString();
//                    if (Objects.nonNull(enName)) {
//                        jsonObject.set("enName", enName);
//                        Long id = enNameIdMap.get(enName);
//                        if (Objects.nonNull(id)) {
//                            jsonObject.set("id", id);
//                            dashboardFilterCardDO.setFields(jsonObject.toString());
//                            flag = true;
//                        }
//                    }
//                }
//
//                if (flag) {
//                    filterCardService.updateAll(dashboardFilterCardDO);
//                    log.info("after reversion FilterCard Dataset : {}", dashboardFilterCardDO);
//                } else {
//                    idList.add(dashboardFilterCardDO.getId());
//                    datasetReversionDo.setMessage("竟然没更新" + datasetReversionDo.getMessage());
//                    datasetReversionDo.setStatus("fail");
//                    datasetReversionService.insert(datasetReversionDo);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                idList.add(dashboardFilterCardDO.getId());
//                String fullStackTrace = ExceptionUtils.getStackTrace(e);
//                datasetReversionDo.setMessage(fullStackTrace);
//                datasetReversionDo.setStatus("fail");
//                datasetReversionService.insert(datasetReversionDo);
//            }
//        }
//
//        return idList;
//    }
//
//
//
//    private String getFieldFormat(DatasetColumn datasetColumn) {
//        if (datasetColumn.getShowTypeCode() == 0) {
//            return "CHARACTER_INPUT";
//        } else if (datasetColumn.getShowTypeCode() == 1) {
//            return "DECIMAL";
//        } else if (datasetColumn.getShowTypeCode() == 2) {
//            return "DATETIME";
//        } else if (datasetColumn.getShowTypeCode() == 3) {
//            return "CHARACTER_SELECT";
//        }
//        return null;
//    }
//
//
//    private DataSetFieldTypeEnum judgeDataSetFieldTypeEnum(DatasetColumn datasetColumn) {
//
//        if (Objects.isNull(datasetColumn.getIsMeasure())) {
//            return DataSetFieldTypeEnum.DIMENSION;
//        }
//
//        if (Objects.isNull(datasetColumn.getIsDimenssion())) {
//            return DataSetFieldTypeEnum.MEASURE;
//        }
//
//        if (datasetColumn.getIsMeasure() == 0 && datasetColumn.getIsDimenssion() == 0) {
//            return DataSetFieldTypeEnum.ALL;
//        } else if (datasetColumn.getIsMeasure() == 1 && datasetColumn.getIsDimenssion() == 0) {
//            return DataSetFieldTypeEnum.DIMENSION;
//        } else if (datasetColumn.getIsMeasure() == 0 && datasetColumn.getIsDimenssion() == 1) {
//            return DataSetFieldTypeEnum.MEASURE;
//        }
//        return null;
//    }
//
//
//    private DatasetFieldDo getDatasetFieldDo(List<DatasetFieldDo> datasetFieldDos, String column) {
//
//        for (DatasetFieldDo datasetFieldDo : datasetFieldDos) {
//            if (datasetFieldDo.getFieldEnName().equals(column)) {
//                return datasetFieldDo;
//            }
//        }
//
//        return null;
//    }
//
//
//    private String getUserEmail(List<UserInfo> userInfoList, String account) {
//
//        for (UserInfo userInfo : userInfoList) {
//            if (userInfo.getAccount().equals(account)) {
//                return userInfo.getEmail();
//            }
//        }
//        return null;
//    }
//
//
//    private String getUserCnName(List<UserInfo> userInfoList, String account) {
//        for (UserInfo userInfo : userInfoList) {
//            if (userInfo.getAccount().equals(account)) {
//                return userInfo.getNickName();
//            }
//        }
//        return null;
//    }
//
//
//    private DataSource biDataSource() {
//        DriverManagerDataSource dataSource = new DriverManagerDataSource();
//
//        DataSourceConfig biMysqlConfig = JSONUtil.toBean(apolloRefreshConfig.getBiMysqlDatasource(), DataSourceConfig.class);
//
//        dataSource.setDriverClassName(biMysqlConfig.getDriveClass());
//        dataSource.setUrl(biMysqlConfig.getJdbcUrl());
//        dataSource.setUsername(biMysqlConfig.getUserName());
//        dataSource.setPassword(biMysqlConfig.getPassWord());
//
//        return dataSource;
//    }
//
//
//
//    private DataSource indexProcessDataSource() {
//        DriverManagerDataSource dataSource = new DriverManagerDataSource();
//
//        DataSourceConfig indexMysqlConfig = JSONUtil.toBean(apolloRefreshConfig.getIndexMysqlDatasource(), DataSourceConfig.class);
//        dataSource.setDriverClassName(indexMysqlConfig.getDriveClass());
//        dataSource.setUrl(indexMysqlConfig.getJdbcUrl());
//        dataSource.setUsername(indexMysqlConfig.getUserName());
//        dataSource.setPassword(indexMysqlConfig.getPassWord());
//
//        return dataSource;
//    }
//
//
//    @Data
//    class DataSourceConfig implements Serializable {
//        private String driveClass;
//        private String jdbcUrl;
//        private String userName;
//        private String passWord;
//        private String validationQuery;
//        private Integer maxTotal;
//        private Long maxWaitMillis;
//        private Boolean testOnBorrow;
//        private String jmxBeanName;
//    }
//}
