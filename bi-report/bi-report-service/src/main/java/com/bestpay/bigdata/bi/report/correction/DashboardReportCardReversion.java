package com.bestpay.bigdata.bi.report.correction;

import cn.hutool.core.collection.CollUtil;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardReportCardService;
import com.bestpay.bigdata.bi.database.api.report.ReportService;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.dashboard.DashboardQuery;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardCardDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * ClassName: DashboardReportCardReversion
 * Package: com.bestpay.bigdata.bi.report.controller.dataset.correction
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/1/25 10:07
 * @Version 1.0
 */
@Slf4j
@Component
public class DashboardReportCardReversion
{
    @Resource
    private ReportService reportService;

    @Resource
    private DashboardCardService dashboardCardService;

    @Resource
    private DashboardDaoService dashboardDaoService;

    @Resource
    private DashboardReportCardService dashboardReportCardService;

    public Response startReversion() {

        List<Dashboard> allDashboardList = dashboardDaoService.queryByCondition(new DashboardQuery());

        List<Long> dashboardIdList = allDashboardList.stream()
                .map(Dashboard::getId).collect(Collectors.toList());
        log.info("query all dashboard list size : {}", dashboardIdList.size());

        for (Long dashboardId : dashboardIdList) {
            log.info("start startReversion()");
            List<DashboardCardDO> oldReportCardRecordList =
                    dashboardCardService.findById(dashboardId, "report", null);
            Dashboard dashboard = dashboardDaoService.getById(dashboardId);
            if (CollUtil.isNotEmpty(oldReportCardRecordList)) {
                log.info("find this dashboard : {}, contains report card", dashboard.getName());

                for (DashboardCardDO dashboardCardDO : oldReportCardRecordList) {
                  extracted(dashboardId, dashboardCardDO);
                }
            }
        }

        return Response.ok();
    }

  @Transactional(rollbackFor = Throwable.class)
  public void extracted(Long dashboardId, DashboardCardDO dashboardCardDO) {
    Long oldReportId = dashboardCardDO.getCardId();
    Report report = reportService.queryById(oldReportId);
    if (report != null) {
       report.setId(null);

       DashboardReportCardDO reportCardDO = new DashboardReportCardDO();
       BeanUtils.copyProperties(report, reportCardDO);
       reportCardDO.setDashboardId(dashboardId);
       reportCardDO.setStatusCode(0);
       Long newReportId = dashboardReportCardService.insert(reportCardDO);

       // update dashboardCardDO cardId
       DashboardCardDO cardDO = new DashboardCardDO();
       cardDO.setId(dashboardCardDO.getId());
       cardDO.setCardId(newReportId);
       dashboardCardService.updateById(cardDO);
   }
  }


}
