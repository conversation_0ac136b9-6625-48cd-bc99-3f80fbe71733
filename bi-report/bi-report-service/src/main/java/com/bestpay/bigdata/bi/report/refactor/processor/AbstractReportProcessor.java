package com.bestpay.bigdata.bi.report.refactor.processor;

import com.bestpay.bigdata.bi.common.refactor.bean.DataChunk;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.report.chart.bean.ChartData;
import com.bestpay.bigdata.bi.report.chart.bean.ChartHeader;
import com.bestpay.bigdata.bi.report.chart.bean.ChartResult;
import com.bestpay.bigdata.bi.report.refactor.chart.IChart;
import com.bestpay.bigdata.bi.report.refactor.chart.IChartManager;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.service.QueryExecutorService;
import com.bestpay.bigdata.bi.report.refactor.service.RequestBuilderService;
import com.bestpay.bigdata.bi.report.refactor.service.ValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.function.Consumer;


@Slf4j
@Component
public abstract class AbstractReportProcessor implements ReportProcessor {

    @Autowired
    protected ValidationService validationService;

    @Autowired
    protected RequestBuilderService requestBuilderService;

    @Autowired
    protected QueryExecutorService queryExecutorService;

    @Autowired
    protected IChartManager chartManager;

    public final Object process(ProcessContext context) {
        log.info("--- Starting {} processing pipeline ---", getProcessorType());

        // 0. set user info
        context.setUserInfo(UserContextUtil.getUserInfo());

        // 1. validate request
        validateRequest(context);

        // 2. build report request
        buildRequest(context);

        // 3. validate data volume（only download scene）
        validateDataVolume(context);

        // 4. execute query SQL, get Iterator<DataChunk>
        executeQuery(context);

        // 5. process chart header & chart data
        processChart(context);

        log.info("--- Finished {} processing pipeline ---", getProcessorType());
        return context.getResponse();
    }

    protected void validateRequest(ProcessContext context) {
        log.info("{} Step 1: Validating request...", getProcessorType());

        beforeValidateRequest(context);

        validationService.validate(context);

        afterValidateRequest(context);
    }

    protected void buildRequest(ProcessContext context) {
        log.info("{} Step 2: Building request...", getProcessorType());

        beforeBuildRequest(context);

        requestBuilderService.build(context);

        afterBuildRequest(context);
    }

    protected void validateDataVolume(ProcessContext context) {
    }

    protected void executeQuery(ProcessContext context) {
        log.info("{} Step 4: Executing query...", getProcessorType());

        beforeExecuteQuery(context);

        queryExecutorService.executeQuery(context);

        afterExecuteQuery(context);
    }

    protected void processChart(ProcessContext context) {
        log.info("{} Step 5: Processing chart...", getProcessorType());

        beforeProcessChart(context);
        Consumer<ChartResult> resultConsumer = context.getResultConsumer();

        IChart chart = chartManager.createChart(context.getChartContext());
        ChartHeader chartHeader = chart.getChartHeader();

        Iterator<DataChunk> dataChunkIterator = context.getDataChunkIterator();
        while (dataChunkIterator.hasNext()) {
            DataChunk dataChunk = dataChunkIterator.next();
            List<List<String>> baseData = dataChunk.getData();
            ChartData chartData = chart.getChartData(chartHeader, baseData);
            ChartResult chartResult = chart.processHideFormat(chartHeader, chartData);
            resultConsumer.accept(chartResult);
        }

        afterProcessChart(context);
    }

    protected abstract Consumer<ChartResult> defineResultConsumer(ProcessContext context);

    protected void beforeValidateRequest(ProcessContext context) {
    }

    protected void afterValidateRequest(ProcessContext context) {
    }

    protected void beforeBuildRequest(ProcessContext context) {
    }

    protected void afterBuildRequest(ProcessContext context) {
    }

    protected void beforeExecuteQuery(ProcessContext context) {
    }

    protected void afterExecuteQuery(ProcessContext context) {
    }

    protected void beforeProcessChart(ProcessContext context) {
        Consumer<ChartResult> resultConsumer = defineResultConsumer(context);
        context.setResultConsumer(resultConsumer);
    }

    protected void afterProcessChart(ProcessContext context) {
    }

    public abstract ProcessorType getProcessorType();

}
