package com.bestpay.bigdata.bi.report.refactor.processor;

import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.enums.FileType;
import com.bestpay.bigdata.bi.common.error.ReportErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.report.chart.bean.ChartResult;
import com.bestpay.bigdata.bi.report.refactor.chart.IChart;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.service.DownloadService;
import com.bestpay.bigdata.bi.report.request.report.DownloadApplyRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

@Slf4j
@Component
public class DownloadReportProcessor extends AbstractReportProcessor {

    @Autowired
    private DownloadService downloadService;

    @Autowired
    private ApolloRefreshConfig refreshConfig;

    private static final Integer EXCEL_MAX_ROW = 1048576;
    private static final Integer SUBSCRIBE_DEFAULT_MAX_ROW = 1000000;

    @Override
    protected void validateDataVolume(ProcessContext context) {
        log.info("[Download] Validating data volume...");

        DownloadApplyRequest request = (DownloadApplyRequest) context.getRawRequest();
        FileType fileType = Optional.ofNullable(request.getFileType())
                .map(FileType::getFileType)
                .orElse(FileType.EXCEL);

        IChart chart = chartManager.createChart(context.getChartContext());
        Long chartRowCount = chart.getChartRowCount();

        if (chartRowCount == null || chartRowCount == 0) {
            throw new BiException(ReportErrorCode.REPORT_DATA_EMPTY, "报表数据为空");
        }

        log.info("[Download] fileType={}, rowCount={}, isSubscribe={}", fileType, chartRowCount, context.isSubscribe());

        // Excel 文件限制
        if (FileType.EXCEL.equals(fileType) && chartRowCount > EXCEL_MAX_ROW) {
            throw new BiException(ReportErrorCode.REPORT_DOWNLOAD_RESTRICT,
                    "下载总条数超过 " + EXCEL_MAX_ROW + " 行，不能下载 Excel 文件，推荐使用 CSV 格式"
            );
        }

        // 订阅限制
        if (context.isSubscribe()) {
            int rowLimit = Optional.ofNullable(refreshConfig.getResultRowsNumLimit()).orElse(SUBSCRIBE_DEFAULT_MAX_ROW);
            if (chartRowCount > rowLimit) {
                throw new BiException(ReportErrorCode.REPORT_DOWNLOAD_RESTRICT, "订阅数据行数上限为 " + rowLimit + " 行"
                );
            }
        }
    }

    @Override
    protected Consumer<ChartResult> defineResultConsumer(ProcessContext context) {
        return chartResult -> {
            log.info("[Download] Consuming a data chunk for download...");
            // 每接收到一块数据，就立即处理并写入文件
            processResult(context, chartResult);
        };
    }

    private void processResult(ProcessContext context, ChartResult chartResult) {
        log.info("[Download] Processing result...");

        downloadService.decryptSensitiveFields();

        downloadService.buildDownloadContext();

        downloadService.writeToLocalFile();

        downloadService.uploadToRemote();
    }

    @Override
    public ProcessorType getProcessorType() {
        return ProcessorType.DOWNLOAD;
    }
}
