package com.bestpay.bigdata.bi.report.refactor.processor.validator;

import com.bestpay.bigdata.bi.database.bean.AuthResourceTypeEnum;
import com.bestpay.bigdata.bi.report.refactor.bean.RequestKey;
import com.bestpay.bigdata.bi.report.refactor.context.ProcessContext;
import com.bestpay.bigdata.bi.report.refactor.enums.ProcessorType;
import com.bestpay.bigdata.bi.report.refactor.enums.SceneType;
import com.bestpay.bigdata.bi.report.request.auth.ObjectAuthRequest;
import com.bestpay.bigdata.bi.report.request.report.QueryReportRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ReportValidator extends AbstractScenceValidator {

    @Override
    public RequestKey supportedKey() {
        return new RequestKey(SceneType.REPORT, ProcessorType.QUERY);
    }

    @Override
    protected boolean isAuthCheckEnabled() {
        return true;
    }

    @Override
    protected ObjectAuthRequest buildAuthRequest(ProcessContext context) {
        QueryReportRequest request = (QueryReportRequest) context.getRawRequest();
        ObjectAuthRequest authRequest = new ObjectAuthRequest();
        authRequest.setAuthResourceId(request.getReportId() + "");
        authRequest.setAuthResourceType(AuthResourceTypeEnum.report.name());
        return authRequest;
    }

}
