package com.bestpay.bigdata.bi.report.correction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.dashboard.ReportCardQueryDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardReportCardService;
import com.bestpay.bigdata.bi.database.api.report.component.NewReportService;
import com.bestpay.bigdata.bi.database.bean.report.ReportQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import com.bestpay.bigdata.bi.database.dao.report.NewReportDO;
import com.bestpay.bigdata.bi.report.bean.dataset.DatasetAuthFieldMappingDTO;
import com.bestpay.bigdata.bi.report.request.dataset.DatesetAuthAddRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetAuthService;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataAuthToDatasetAuthReversion {

  @Resource
  private NewReportService reportService;

  @Resource
  private DashboardReportCardService reportCardService;

  @Resource
  private DatasetAuthService datasetAuthService;

  public void correct(){
    List<NewReportDO> reports =  reportService.queryAll(new ReportQueryDTO());

    for(int i=0;i < reports.size();i++){
      try {
        log.info("processing i={}", reports.get(i).getId() );


        String auth = reports.get(i).getDataAuth();
        Long datasetId = reports.get(i).getDatasetId();
        // 添加规则
        addRule(auth, datasetId);

      }catch (Exception e){
        log.error("correct error, reportId={}",reports.get(i).getId(), e);
      }
    }

    List<DashboardReportCardDO> reportCards
        = reportCardService.find(new ReportCardQueryDTO());

    for (DashboardReportCardDO reportCard : reportCards) {

      try {
        log.info("processing id={}", reportCard.getId());
        String auth = reportCard.getDataAuth();
        // 添加规则
        addRule(auth, getDatasetInfos(reportCard).get(0).getDatasetId());

      }catch (Exception e){
        log.error("correct error, reportId={}",reportCard.getId(), e);
      }


    }

  }

  private void addRule(String auth, Long datasetId) {
    if(StringUtil.isNotEmpty(auth)){
      String[] args = auth.split("\\.");
      List<DatasetAuthFieldMappingDTO> fieldMappingList = Lists.newArrayList();
      if(!args[0].equals("-1")){
        DatasetAuthFieldMappingDTO mappingDTO = new DatasetAuthFieldMappingDTO();
        mappingDTO.setDatasetField(args[0]);
        mappingDTO.setLabelField("orgCode");
        fieldMappingList.add(mappingDTO);
      }

      if(!args[1].equals("-1")){
        DatasetAuthFieldMappingDTO mappingDTO = new DatasetAuthFieldMappingDTO();
        mappingDTO.setDatasetField(args[1]);
        mappingDTO.setLabelField("provinceCode");
        fieldMappingList.add(mappingDTO);
      }

      if(!args[2].equals("-1")){
        DatasetAuthFieldMappingDTO mappingDTO = new DatasetAuthFieldMappingDTO();
        mappingDTO.setDatasetField(args[2]);
        mappingDTO.setLabelField("cityCode");
        fieldMappingList.add(mappingDTO);
      }

      DatesetAuthAddRequest authAddRequest = new DatesetAuthAddRequest();
      authAddRequest.setUserType("all");
      authAddRequest.setDatasetId(datasetId);
      authAddRequest.setRuleType("userLabel");
      authAddRequest.setRuleName("数据权限");
      authAddRequest.setAuthUser(Lists.newArrayList());

      authAddRequest.setFieldMappingList(fieldMappingList);
      if(CollUtil.isNotEmpty(fieldMappingList)){
        datasetAuthService.addRule(authAddRequest);
      }
    }
  }

  private static List<DatasetInfo> getDatasetInfos(DashboardReportCardDO report) {
    List<DatasetInfo> datasetInfos = new ArrayList<>();
    try {
      datasetInfos = JSONUtil.toList(report.getDatasetInfo(), DatasetInfo.class);
    } catch (Exception e) {
      DatasetInfo datasetInfo = JSONUtil.toBean(report.getDatasetInfo(), DatasetInfo.class);
      datasetInfos.add(datasetInfo);
    }

    for (DatasetInfo datasetInfo : datasetInfos) {
      if (datasetInfo.getDataSourceType() == null) {
        log.info("dataSetId {}, dataSourceType {}", datasetInfo.getDatasetId(), 0);
        datasetInfo.setDataSourceTypeCode(0);
      }
    }
    return datasetInfos;
  }

}
