package com.bestpay.bigdata.bi.report.correction;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-07-14-10:36
 */
@Data
public class DatasetColumn implements Serializable {
    private static final long serialVersionUID = -55204504929317824L;

    private Long id;
    /**
     * 数据集ID
     */
    private Long datasetId;
    /**
     * 字段名称
     */
    private String name;
    /**
     * 字段英文名称
     */
    private String enName;
    /**
     * 增加了转换函数的字段英文名称
     */
    private String convertEnName;
    /**
     * 来源表
     */
    private Long sourceColumnId;
    /**
     * 字段类型
     */
    private String typeCode;
    /**
     * 字段显示类型
     */
    private Integer showTypeCode;


    /**
     * 是否是度量
     */
    private Integer isMeasure;
    /**
     * 是否是维度
     */
    private Integer isDimenssion;


    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

}
